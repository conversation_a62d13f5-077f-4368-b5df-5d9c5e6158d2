using DragonCD2.UserSegmentation.Rule.Parameter;

namespace DragonCD2.UserSegmentation
{
    public class UserSegmentation
    {
        public static readonly BehavioralSegmentationEngine BehavioralEngine = new();

        public static ISegmentationEngine CreateCustomEngine(string rulesJson, string rulePresetsJson, params FlexibleRuleParam[] ruleParams)
        {
            return new FlexibleUserSegmentationEngine(rulesJson, rulePresetsJson, ruleParams);
        }
    }
}