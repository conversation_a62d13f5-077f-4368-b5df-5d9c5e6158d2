using UnityEngine;

namespace DragonCD2.UserSegmentation.Rule.Parameter
{
    internal sealed class BasicGroupIdParam : FlexibleRuleParam
    {
        public override string Alias => "BasicGroup";

        public override object Value
        {
            get
            {
                var ret = UserSegmentation.BehavioralEngine.BasicSegId;
                Debug.Log($"[用户分层框架] 用户所在的基础分组是[{ret}]");
                return ret;
            }
        }
    }
}