using System.Collections.Generic;
using DragonCD2.UserSegmentation.Rule.Parameter;

namespace DragonCD2.UserSegmentation
{
    public interface ISegmentationEngine
    {
        protected const string RULE_EXPRESSION_PATTERN = @"\{([^}]*)\}|《([^》]*)》";
        
        public int SegmentationId { get; }
        
        internal IReadOnlyDictionary<string, FlexibleRuleParam> RuleParamsDictionary { get; }
    }
}