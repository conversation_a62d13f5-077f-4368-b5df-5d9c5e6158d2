using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using DragonCD2.UserSegmentation.Bean;
using DragonCD2.UserSegmentation.Rule.Parameter;
using DragonCD2.UserSegmentation.Rule.Validator;
using Newtonsoft.Json;

namespace DragonCD2.UserSegmentation
{
    public class FlexibleUserSegmentationEngine : ISegmentationEngine
    {
        public int SegmentationId
        {
            get
            {
                foreach (var userGroupRule in _segmentationRuleList)
                {
                    var isOk = FlexibleRuleValidator.InvokeValidate(this, userGroupRule);
                    if (!isOk)
                    {
                        continue;
                    }

                    return userGroupRule.SegmentationId;
                }

                return -1;
            }
        }

        private readonly Dictionary<string, FlexibleRuleParam> _ruleParamsDic = new();
        IReadOnlyDictionary<string, FlexibleRuleParam> ISegmentationEngine.RuleParamsDictionary => _ruleParamsDic;

        // 分层规则中配置的预设字典
        private readonly Dictionary<string, string> _rulePresetDic = new();

        // 分层的计算规则列表
        private List<SegmentationRule> _segmentationRuleList = new();

        internal FlexibleUserSegmentationEngine(string rulesJson, string rulePresetsJson, params FlexibleRuleParam[] ruleParams)
        {
            RegisterParam(ruleParams);

            LoadPresets(rulePresetsJson);

            AnalyzeRules(rulesJson);
        }

        /// <summary>
        /// 注册自定义分层条件参数到规则引擎
        /// </summary>
        /// <param name="ruleParams">自定义条件参数对象列表</param>
        private void RegisterParam(params FlexibleRuleParam[] ruleParams)
        {
            foreach (var flexibleRuleParam in ruleParams)
            {
                var type = flexibleRuleParam.GetType();
                if (type.IsAbstract)
                {
                    continue;
                }

                _ruleParamsDic[flexibleRuleParam.Alias] = flexibleRuleParam;
            }
        }

        private void LoadPresets(string rulePresetsJson)
        {
            if (string.IsNullOrEmpty(rulePresetsJson))
            {
                return;
            }

            var presets = JsonConvert.DeserializeObject<List<SegmentationRulePreset>>(rulePresetsJson);
            if (presets == null || presets.Count == 0)
            {
                return;
            }

            foreach (var userGroupPreset in presets)
            {
                _rulePresetDic[userGroupPreset.PresetKey] = userGroupPreset.PresetValue;
            }
        }

        private void AnalyzeRules(string rulesJson)
        {
            if (string.IsNullOrEmpty(rulesJson))
            {
                throw new Exception("客户端用户分层框架初始化失败，BehavioralRulesJson数据解析为空!");
            }

            _segmentationRuleList = JsonConvert.DeserializeObject<List<SegmentationRule>>(rulesJson);
            if (_segmentationRuleList == null || _segmentationRuleList.Count == 0)
            {
                throw new Exception("客户端用户分层框架初始化失败，无法成功加载BehavioralRulesJson配置数据！");
            }

            foreach (var userGroupRule in _segmentationRuleList)
            {
                while (IsAnyUnAnalyzedPreset(userGroupRule.RuleExpression))
                {
                    userGroupRule.RuleExpression = AnalyzeExpressionPresets(userGroupRule.RuleExpression);
                }
            }
        }

        private string AnalyzeExpressionPresets(string expression)
        {
            return Regex.Replace(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN, Analyze);

            string Analyze(Match match)
            {
                // 根据匹配到的内容生成动态替换文本
                for (int i = 1; i < match.Groups.Count; i++)
                {
                    if (_rulePresetDic.TryGetValue(match.Groups[i].Value, out var presetValue))
                    {
                        return presetValue;
                    }
                }

                return match.Groups[0].Value;
            }
        }

        private bool IsAnyUnAnalyzedPreset(string expression)
        {
            var matches = Regex.Matches(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN);
            if (matches.Count == 0)
            {
                return false;
            }

            foreach (Match matchInfo in matches)
            {
                for (int i = 1; i < matchInfo.Groups.Count; i++)
                {
                    if (_rulePresetDic.ContainsKey(matchInfo.Groups[i].Value))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
    }
}