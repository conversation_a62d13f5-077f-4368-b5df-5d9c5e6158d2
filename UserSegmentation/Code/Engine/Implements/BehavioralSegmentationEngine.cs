using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Text.RegularExpressions;
using DragonCD2.UserSegmentation.Bean;
using DragonCD2.UserSegmentation.Rule.Parameter;
using DragonCD2.UserSegmentation.Rule.Validator;
using Newtonsoft.Json;
using UnityEngine;

namespace DragonCD2.UserSegmentation
{
    public class BehavioralSegmentationEngine : ISegmentationEngine
    {
        public bool IsInitialized => _helper != null;

        public class SegmentationInfo
        {
            public int SegmentationId;

            public long UpdateTimeStamp;
        }

        public interface IEngineHelper
        {
            /// <summary>
            /// 获取分层配置表Preset中的配置导出的Json文本内容
            /// </summary>
            string RulePresetJson { get; }

            /// <summary>
            /// 获取分层配置表BasicSegmentationRules中的配置导出的Json文本内容
            /// </summary>
            public string BasicSegmentationRulesJson { get; }

            /// <summary>
            /// 获取分层配置表BehavioralSegmentationRules中的配置导出的Json文本内容
            /// </summary>
            public string BehavioralSegmentationRulesJson { get; }

            /// <summary>
            /// 将基础分层信息存入存档或从存档中读取
            /// </summary>
            SegmentationInfo BasicSegmentInfo { get; set; }

            /// <summary>
            /// 将行为画像分层信息存入存档或从存档中读取
            /// </summary>
            SegmentationInfo BehavioralSegmentInfo { get; set; }

            /// <summary>
            /// 获取当前UTC日期的毫秒时间戳
            /// </summary>
            long UtcNowMilliseconds { get; }

            /// <summary>
            /// 各项目根据自身需求自定义拓展的分层规则条件参数对象列表
            /// </summary>
            FlexibleRuleParam[] RuleParams { get; }
        }

        internal BehavioralSegmentationEngine()
        {
        }

        internal int BasicSegId
        {
            get
            {
                if (_helper == null)
                {
                    throw new Exception("未设置分层规则引擎必须的IHelper对象!");
                }

                var segInfo = _helper.BasicSegmentInfo;
                if (segInfo == null)
                {
                    return 0;
                }

                return segInfo.SegmentationId;
            }

            private set
            {
                if (_helper == null)
                {
                    throw new Exception("未设置分层规则引擎必须的IHelper对象!");
                }

                var segmentationInfo = new SegmentationInfo()
                {
                    SegmentationId = value,
                    UpdateTimeStamp = _helper.UtcNowMilliseconds,
                };

                _helper.BasicSegmentInfo = segmentationInfo;

                Debug.Log($"[用户分层框架] 用户基础分层id被划分在[{value}]");
            }
        }

        /// <summary>
        /// 用户行为画像分层Id
        /// </summary>
        private int BehavioralSegId
        {
            get
            {
                if (_helper == null)
                {
                    throw new Exception("未设置分层规则引擎必须的IHelper对象!");
                }

                EnsureGroupCalculated();

                return _helper.BehavioralSegmentInfo.SegmentationId;
            }
            set
            {
                if (!IsSegmentationUpdateable())
                {
                    return;
                }

                if (_helper == null)
                {
                    throw new Exception("未设置分层规则引擎必须的IHelper对象!");
                }

                var segmentationInfo = new SegmentationInfo()
                {
                    SegmentationId = value,
                    UpdateTimeStamp = _helper.UtcNowMilliseconds,
                };

                _helper.BehavioralSegmentInfo = segmentationInfo;

                Debug.Log($"[用户分层框架] 当日用户分层id被划分在[{value}]");
            }
        }

        private IEngineHelper _helper;

        public int SegmentationId => BehavioralSegId;

        // 用于规则引擎计算分层的条件参数对象字典
        private readonly Dictionary<string, FlexibleRuleParam> _ruleParamsDic = new();

        IReadOnlyDictionary<string, FlexibleRuleParam> ISegmentationEngine.RuleParamsDictionary => RuleParamsDictionary;

        internal IReadOnlyDictionary<string, FlexibleRuleParam> RuleParamsDictionary => _ruleParamsDic;

        // 分层规则中配置的预设字典
        private readonly Dictionary<string, string> _rulePresetDic = new();

        // 基础分层的计算规则列表
        private List<SegmentationRule> _basicSegmentationRuleList = new();

        // 游戏分层的计算规则列表
        private List<SegmentationRule> _behavioralSegmentationRuleList = new();

        // 分层规则相关的配置及参数等数据是否都已经进行过读取加载
        private bool _isRuleDataLoaded;

        // 是否已经完成过分层计算
        private bool _isUserGroupCalculated;

        /// <summary>
        /// 初始化引擎，计算用户所在分层
        /// </summary>
        public void Init([NotNull] IEngineHelper engineHelper)
        {
            _helper = engineHelper;

            RegisterParam(engineHelper.RuleParams);

            EnsureGroupCalculated();
        }

        /// <summary>
        /// 注册自定义分层条件参数到规则引擎
        /// </summary>
        /// <param name="ruleParams">自定义条件参数对象列表</param>
        private void RegisterParam(params FlexibleRuleParam[] ruleParams)
        {
            foreach (var flexibleRuleParam in ruleParams)
            {
                _ruleParamsDic[flexibleRuleParam.Alias] = flexibleRuleParam;
            }
        }

        /// <summary>
        /// 进行分层计算
        /// </summary>
        private void EnsureGroupCalculated()
        {
            if (_isUserGroupCalculated)
            {
                return;
            }

            EnsureRuleData();

            CalculateSegmentation();

            _isUserGroupCalculated = true;
        }

        private void EnsureRuleData()
        {
            if (_isRuleDataLoaded)
            {
                return;
            }

            EnsureRuleParameters();
            EnsureRulePresets();
            EnsureBasicSegmentationRules();
            EnsureBehavioralSegmentationRules();

            _isRuleDataLoaded = true;
        }

        private void EnsureRuleParameters()
        {
            if (_isRuleDataLoaded)
            {
                return;
            }

            var assembly = Assembly.GetAssembly(GetType());
            foreach (var type in assembly.GetTypes())
            {
                if (type.IsAbstract || !type.IsSealed || !type.IsSubclassOf(typeof(FlexibleRuleParam)))
                {
                    continue;
                }

                var paramInstance = Activator.CreateInstance(type) as FlexibleRuleParam;

                _ruleParamsDic[paramInstance.Alias] = paramInstance;
            }
        }

        private void EnsureRulePresets()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            if (_isRuleDataLoaded)
            {
                return;
            }

            var rulePresetsJson = _helper.RulePresetJson;
            if (string.IsNullOrEmpty(rulePresetsJson))
            {
                return;
            }

            var presets = JsonConvert.DeserializeObject<List<SegmentationRulePreset>>(rulePresetsJson);
            if (presets == null || presets.Count == 0)
            {
                return;
            }

            foreach (var userGroupPreset in presets)
            {
                _rulePresetDic[userGroupPreset.PresetKey] = userGroupPreset.PresetValue;
            }
        }

        private void EnsureBasicSegmentationRules()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            if (_isRuleDataLoaded)
            {
                return;
            }

            var rulesJson = _helper.BasicSegmentationRulesJson;
            if (string.IsNullOrEmpty(rulesJson))
            {
                throw new Exception("客户端用户分层框架初始化失败，BasicRulesJson数据解析为空!");
            }

            _basicSegmentationRuleList = JsonConvert.DeserializeObject<List<SegmentationRule>>(rulesJson);
            if (_basicSegmentationRuleList == null || _basicSegmentationRuleList.Count == 0)
            {
                throw new Exception("客户端用户分层框架初始化失败，无法成功加载BasicGroupRules配置数据！");
            }

            foreach (var userGroupRule in _basicSegmentationRuleList)
            {
                while (IsAnyUnAnalyzedPreset(userGroupRule.RuleExpression))
                {
                    userGroupRule.RuleExpression = AnalyzeExpressionPresets(userGroupRule.RuleExpression);
                }
            }
        }

        private void EnsureBehavioralSegmentationRules()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            if (_isRuleDataLoaded)
            {
                return;
            }

            var rulesJson = _helper.BehavioralSegmentationRulesJson;
            if (string.IsNullOrEmpty(rulesJson))
            {
                throw new Exception("客户端用户分层框架初始化失败，BehavioralRulesJson数据解析为空!");
            }

            _behavioralSegmentationRuleList = JsonConvert.DeserializeObject<List<SegmentationRule>>(rulesJson);
            if (_behavioralSegmentationRuleList == null || _behavioralSegmentationRuleList.Count == 0)
            {
                throw new Exception("客户端用户分层框架初始化失败，无法成功加载BehavioralRulesJson配置数据！");
            }

            foreach (var userGroupRule in _behavioralSegmentationRuleList)
            {
                while (IsAnyUnAnalyzedPreset(userGroupRule.RuleExpression))
                {
                    userGroupRule.RuleExpression = AnalyzeExpressionPresets(userGroupRule.RuleExpression);
                }
            }
        }

        private string AnalyzeExpressionPresets(string expression)
        {
            return Regex.Replace(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN, Analyze);

            string Analyze(Match match)
            {
                // 根据匹配到的内容生成动态替换文本
                for (int i = 1; i < match.Groups.Count; i++)
                {
                    if (_rulePresetDic.TryGetValue(match.Groups[i].Value, out var presetValue))
                    {
                        return presetValue;
                    }
                }

                return match.Groups[0].Value;
            }
        }

        private bool IsAnyUnAnalyzedPreset(string expression)
        {
            // 谷歌表格导出的规则表达式可能为空
            if (string.IsNullOrEmpty(expression))
            {
                return false;
            }
            var matches = Regex.Matches(expression, ISegmentationEngine.RULE_EXPRESSION_PATTERN);
            if (matches.Count == 0)
            {
                return false;
            }

            foreach (Match matchInfo in matches)
            {
                for (int i = 1; i < matchInfo.Groups.Count; i++)
                {
                    if (_rulePresetDic.ContainsKey(matchInfo.Groups[i].Value))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private void EnsureBasicSegmentation()
        {
            if (_isUserGroupCalculated)
            {
                return;
            }

            foreach (var userGroupRule in _basicSegmentationRuleList)
            {
                var isOk = FlexibleRuleValidator.InvokeValidate(this, userGroupRule);
                if (!isOk)
                {
                    continue;
                }

                BasicSegId = userGroupRule.SegmentationId;

                break;
            }
        }

        private void CalculateSegmentation()
        {
            if (_isUserGroupCalculated)
            {
                return;
            }

            if (!IsSegmentationUpdateable())
            {
                return;
            }

            EnsureBasicSegmentation();

            foreach (var userGroupRule in _behavioralSegmentationRuleList)
            {
                // 只对符合基础分层划分的规则进行验证
                if (userGroupRule.SegmentationId / BasicSegId != 1)
                {
                    continue;
                }

                var isOk = FlexibleRuleValidator.InvokeValidate(this, userGroupRule);
                if (!isOk)
                {
                    continue;
                }

                BehavioralSegId = userGroupRule.SegmentationId;
                break;
            }
        }

        private bool IsSegmentationUpdateable()
        {
            if (_helper == null)
            {
                throw new Exception("未设置分层规则引擎必须的IHelper对象!");
            }

            var segInfo = _helper.BehavioralSegmentInfo;
            if (segInfo is not { UpdateTimeStamp: > 0 })
            {
                return true;
            }

            var timestampLastUpdate = segInfo.UpdateTimeStamp;
            var timestampNow = _helper.UtcNowMilliseconds;

            var dateTimeLastUpdate = DateTimeOffset.FromUnixTimeMilliseconds(timestampLastUpdate);
            var dateTimeNow = DateTimeOffset.FromUnixTimeMilliseconds(timestampNow);

            Debug.Log($"[用户分层框架] 上一次计算分层id的时间是[{dateTimeLastUpdate}],现在是[{dateTimeNow}]");
            // 如果距离上一次分层更新日期已经跨天，则可以进行更新
            return dateTimeNow.Date != dateTimeLastUpdate.Date;
        }
    }
}