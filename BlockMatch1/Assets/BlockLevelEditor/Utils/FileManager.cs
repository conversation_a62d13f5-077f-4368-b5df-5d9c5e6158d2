#if UNITY_EDITOR

using System;
using System.IO;
using UnityEngine;
using UnityEditor;
using BlockLevelEditor.Core.Data;

namespace BlockLevelEditor.Utils
{
    /// <summary>
    /// 文件管理工具类 - 处理关卡文件的导入导出
    /// </summary>
    public static class FileManager
    {
        // 关卡文件目录
        public static readonly string LEVEL_LAYOUT_PATH = "Assets/Res/LevelLayout/";
        public static readonly string LEVEL_EDITOR_PATH = "Assets/LevelEditor/";
        
        /// <summary>
        /// 保存关卡数据到文件
        /// </summary>
        public static bool SaveLevelData(LevelData levelData, string fileName, bool formatted = true)
        {
            try
            {
                // 确保目录存在
                if (!Directory.Exists(LEVEL_LAYOUT_PATH))
                {
                    Directory.CreateDirectory(LEVEL_LAYOUT_PATH);
                }

                // 生成JSON
                string json = formatted ? 
                    JsonHelper.SerializeFormatted(levelData) : 
                    JsonHelper.SerializeCompressed(levelData);

                if (string.IsNullOrEmpty(json))
                {
                    Debug.LogError("JSON序列化失败");
                    return false;
                }

                // 确保文件名有正确的扩展名
                if (!fileName.EndsWith(".json"))
                    fileName += ".json";

                // 写入文件
                string filePath = Path.Combine(LEVEL_LAYOUT_PATH, fileName);
                File.WriteAllText(filePath, json);

                // 刷新AssetDatabase
                AssetDatabase.Refresh();

                Debug.Log($"关卡数据已保存到: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"保存关卡数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从文件加载关卡数据
        /// </summary>
        public static LevelData LoadLevelData(string fileName)
        {
            try
            {
                // 确保文件名有正确的扩展名
                if (!fileName.EndsWith(".json"))
                    fileName += ".json";

                // 尝试从LevelLayout目录加载
                string filePath = Path.Combine(LEVEL_LAYOUT_PATH, fileName);
                
                if (!File.Exists(filePath))
                {
                    // 尝试从LevelEditor目录加载
                    filePath = Path.Combine(LEVEL_EDITOR_PATH, fileName);
                }

                if (!File.Exists(filePath))
                {
                    Debug.LogWarning($"关卡文件不存在: {fileName}");
                    return new LevelData();
                }

                // 读取文件内容
                string json = File.ReadAllText(filePath);
                
                if (string.IsNullOrEmpty(json))
                {
                    Debug.LogWarning($"关卡文件为空: {fileName}");
                    return new LevelData();
                }

                // 反序列化
                var levelData = JsonHelper.Deserialize(json);
                
                Debug.Log($"关卡数据已加载: {filePath}");
                return levelData;
            }
            catch (Exception ex)
            {
                Debug.LogError($"加载关卡数据失败: {ex.Message}");
                return new LevelData();
            }
        }

        /// <summary>
        /// 获取所有关卡文件列表
        /// </summary>
        public static string[] GetAllLevelFiles()
        {
            try
            {
                if (!Directory.Exists(LEVEL_LAYOUT_PATH))
                    return new string[0];

                var files = Directory.GetFiles(LEVEL_LAYOUT_PATH, "*.json");
                
                // 只返回文件名，不包含路径和扩展名
                for (int i = 0; i < files.Length; i++)
                {
                    files[i] = Path.GetFileNameWithoutExtension(files[i]);
                }

                return files;
            }
            catch (Exception ex)
            {
                Debug.LogError($"获取关卡文件列表失败: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        public static bool FileExists(string fileName)
        {
            if (!fileName.EndsWith(".json"))
                fileName += ".json";

            string filePath = Path.Combine(LEVEL_LAYOUT_PATH, fileName);
            return File.Exists(filePath);
        }

        /// <summary>
        /// 删除关卡文件
        /// </summary>
        public static bool DeleteLevelFile(string fileName)
        {
            try
            {
                if (!fileName.EndsWith(".json"))
                    fileName += ".json";

                string filePath = Path.Combine(LEVEL_LAYOUT_PATH, fileName);
                
                if (!File.Exists(filePath))
                {
                    Debug.LogWarning($"要删除的文件不存在: {fileName}");
                    return false;
                }

                File.Delete(filePath);
                AssetDatabase.Refresh();

                Debug.Log($"关卡文件已删除: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"删除关卡文件失败: {ex.Message}");
                return false;
            }
        }

        // 注意：以下方法已移除，因为在UI中未被使用：
        // - CopyLevelFile() - 复制关卡文件功能
        // - ExportLevelAsText() - 导出文本格式功能
        // 如需要这些功能，可以重新添加

        // 注意：GenerateLevelText 方法已移除，因为它只被已删除的 ExportLevelAsText 方法使用
    }
}

#endif
