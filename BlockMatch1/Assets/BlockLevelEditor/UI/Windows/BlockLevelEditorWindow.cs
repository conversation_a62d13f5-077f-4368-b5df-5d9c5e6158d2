#if UNITY_EDITOR

using UnityEngine;
using UnityEditor;
using BlockLevelEditor.Core.Data;
using BlockLevelEditor.Core.Manager;
using BlockLevelEditor.UI.Components;
using BlockLevelEditor.Utils;
using BlockLevelEditor.Config;
using static BlockLevelEditor.Core.Data.BlockTypeDefinitions;
using System.Collections.Generic;
using System.Linq;

namespace BlockLevelEditor.UI.Windows
{
    /// <summary>
    /// 方块关卡编辑器主窗口
    /// </summary>
    public class BlockLevelEditorWindow : EditorWindow
    {
        private LevelEditorManager _editorManager;
        private GridManager _gridManager;
        private GridRenderer _gridRenderer;

        // UI状态
        private Vector2 _scrollPosition;
        private string _newFileName = "new_level";
        private string _loadFileName = "";
        private bool _showStatistics = true;
        private bool _showTools = true;
        private bool _showLayerSettings = true;
        private bool _showProperties = true;
        private bool _showTargetConfig = true;

        // 层数设置
        private int _selectedEntityLayers = 1;
        private int _selectedOverlayLayers = 1;

        // 窗口布局
        private const float TOOLBAR_HEIGHT = 30f;
        private const float SIDEBAR_WIDTH = 200f;
        private const float TARGET_PANEL_WIDTH = 250f;
        private const float GRID_PADDING = 20f;

        [MenuItem("关卡编辑器/打开关卡编辑器 %J", priority = 10)]
        //[MenuItem("Tools/Block Level Editor")]
        public static void ShowWindow()
        {
            var window = GetWindow<BlockLevelEditorWindow>("Block Level Editor");
            window.minSize = new Vector2(1200, 600); // 增加宽度以适应三列布局
            window.Show();
        }

        private void OnEnable()
        {
            InitializeManagers();
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        private void InitializeManagers()
        {
            _editorManager = LevelEditorManager.Instance;
            _gridManager = new GridManager(_editorManager);
            _gridRenderer = new GridRenderer(_editorManager, _gridManager);
        }

        private void SubscribeToEvents()
        {
            if (_editorManager != null)
            {
                _editorManager.OnLevelDataChanged += OnLevelDataChanged;
                _editorManager.OnFileNameChanged += OnFileNameChanged;
                _editorManager.OnDirtyStateChanged += OnDirtyStateChanged;
                _editorManager.OnHistoryChanged += OnHistoryChanged;
            }
        }

        private void UnsubscribeFromEvents()
        {
            if (_editorManager != null)
            {
                _editorManager.OnLevelDataChanged -= OnLevelDataChanged;
                _editorManager.OnFileNameChanged -= OnFileNameChanged;
                _editorManager.OnDirtyStateChanged -= OnDirtyStateChanged;
                _editorManager.OnHistoryChanged -= OnHistoryChanged;
            }
        }

        private void OnGUI()
        {
            if (_editorManager == null)
            {
                InitializeManagers();
                return;
            }

            // 处理快捷键
            HandleKeyboardInput();

            DrawToolbar();

            var contentRect = new Rect(0, TOOLBAR_HEIGHT, position.width, position.height - TOOLBAR_HEIGHT);
            DrawMainContent(contentRect);
        }

        private void DrawToolbar()
        {
            var toolbarRect = new Rect(0, 0, position.width, TOOLBAR_HEIGHT);
            GUILayout.BeginArea(toolbarRect);
            GUILayout.BeginHorizontal(EditorStyles.toolbar);

            // 文件操作
            if (GUILayout.Button("新建", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                _editorManager.NewLevel();
            }

            // 文件名输入框
            GUILayout.Label("文件名:", EditorStyles.toolbarButton, GUILayout.Width(40));
            _loadFileName = GUILayout.TextField(_loadFileName, EditorStyles.toolbarTextField, GUILayout.Width(100));

            if (GUILayout.Button("打开", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                if (!string.IsNullOrEmpty(_loadFileName))
                {
                    _editorManager.LoadLevel(_loadFileName);
                }
                else
                {
                    ShowLoadDialog();
                }
            }

            if (GUILayout.Button("保存", EditorStyles.toolbarButton, GUILayout.Width(50)))
            {
                if (string.IsNullOrEmpty(_editorManager.CurrentFileName))
                    ShowSaveDialog();
                else
                    _editorManager.SaveLevel();
            }

            if (GUILayout.Button("另存为", EditorStyles.toolbarButton, GUILayout.Width(60)))
            {
                ShowSaveDialog();
            }

            GUILayout.Space(20);

            // 编辑工具
            var tools = System.Enum.GetValues(typeof(EditorToolType));
            foreach (EditorToolType tool in tools)
            {
                if (tool == EditorToolType.None) continue;
                // 注意：Fill 工具已移除，填充功能通过UI按钮实现
                bool isSelected = _editorManager.CurrentTool == tool;
                string toolName = GetToolDisplayName(tool);
                if (GUILayout.Toggle(isSelected, toolName, EditorStyles.toolbarButton, GUILayout.Width(60)) && !isSelected)
                {
                    _editorManager.SetTool(tool);
                }
            }

            GUILayout.Space(20);

            // 撤销/重做按钮
            GUI.enabled = _editorManager.CanUndo;
            if (GUILayout.Button("撤销", EditorStyles.toolbarButton, GUILayout.Width(40)))
            {
                _editorManager.Undo();
            }
            GUI.enabled = _editorManager.CanRedo;
            if (GUILayout.Button("撤销恢复", EditorStyles.toolbarButton, GUILayout.Width(80)))
            {
                _editorManager.Redo();
            }
            GUI.enabled = true;

            GUILayout.Space(10);

            // 显示模式
            var displayModes = System.Enum.GetValues(typeof(GridDisplayMode));
            foreach (GridDisplayMode mode in displayModes)
            {
                bool isSelected = _editorManager.DisplayMode == mode;
                if (GUILayout.Toggle(isSelected, mode.ToString(), EditorStyles.toolbarButton, GUILayout.Width(50)) && !isSelected)
                {
                    _editorManager.SetDisplayMode(mode);
                }
            }

            GUILayout.FlexibleSpace();

            // 状态信息
            string statusText = _editorManager.IsDirty ? "*" : "";
            statusText += string.IsNullOrEmpty(_editorManager.CurrentFileName) ? "未命名" : _editorManager.CurrentFileName;
            GUILayout.Label(statusText, EditorStyles.toolbarButton);

            GUILayout.EndHorizontal();
            GUILayout.EndArea();
        }

        private void DrawMainContent(Rect contentRect)
        {
            // 左侧工具面板
            var sidebarRect = new Rect(contentRect.x, contentRect.y, SIDEBAR_WIDTH, contentRect.height);
            DrawSidebar(sidebarRect);

            // 右侧目标面板
            var targetPanelRect = new Rect(
                contentRect.x + contentRect.width - TARGET_PANEL_WIDTH,
                contentRect.y,
                TARGET_PANEL_WIDTH,
                contentRect.height
            );
            DrawTargetPanel(targetPanelRect);

            // 中间网格区域
            var gridAreaRect = new Rect(
                contentRect.x + SIDEBAR_WIDTH,
                contentRect.y,
                contentRect.width - SIDEBAR_WIDTH - TARGET_PANEL_WIDTH,
                contentRect.height
            );
            DrawGridArea(gridAreaRect);
        }

        private void DrawSidebar(Rect sidebarRect)
        {
            GUILayout.BeginArea(sidebarRect);
            _scrollPosition = GUILayout.BeginScrollView(_scrollPosition);

            // 方块选择面板
            DrawBlockSelectionPanel();

            GUILayout.Space(10);
            // 批量操作按钮
            GUILayout.Label("批量操作:", EditorStyles.boldLabel);

            if (GUILayout.Button("填充全部"))
            {
                if (EditorUtility.DisplayDialog("确认", "确定要用当前选择填充整个网格吗？", "确定", "取消"))
                {
                    FillEntireGrid();
                }
            }

            if (GUILayout.Button("随机填充"))
            {
                if (EditorUtility.DisplayDialog("确认", "确定要随机填充网格吗？", "确定", "取消"))
                {
                    RandomFillGrid();
                }
            }

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("镜像水平"))
            {
                MirrorGridHorizontal();
            }

            if (GUILayout.Button("镜像垂直"))
            {
                MirrorGridVertical();
            }
            GUILayout.EndHorizontal();
            // 工具面板
            //if (_showTools)
            {
                DrawToolsPanel();
            }

            GUILayout.EndScrollView();
            GUILayout.EndArea();
        }

        private void DrawBlockSelectionPanel()
        {
            GUILayout.Label("方块选择", EditorStyles.boldLabel);

            // 初始化配置系统
            UnifiedBlockConfig.Initialize();

            // 颜色方块选择
            GUILayout.Label("颜色方块:", EditorStyles.miniLabel);
            var colorBlocks = UnifiedBlockConfig.GetAllColorBlocks();
            foreach (var colorBlock in colorBlocks)
            {
                bool isSelected = _editorManager.SelectedColor == (ColorType)colorBlock.value;
                if (GUILayout.Toggle(isSelected, colorBlock.Name, GUILayout.Width(80)) && !isSelected)
                {
                    _editorManager.SetSelectedColor((ColorType)colorBlock.value);
                    _editorManager.SetSelectedGem(GemType.None);
                    _editorManager.SetSelectedAdvancedBlock(AdvancedBlockId.None);
                }
            }

            GUILayout.Space(5);

            // 宝石方块选择
            GUILayout.Label("宝石方块:", EditorStyles.miniLabel);
            var gemBlocks = UnifiedBlockConfig.GetAllGemBlocks();
            foreach (var gemBlock in gemBlocks)
            {
                bool isSelected = _editorManager.SelectedGem == (GemType)gemBlock.value;
                if (GUILayout.Toggle(isSelected, gemBlock.Name, GUILayout.Width(80)) && !isSelected)
                {
                    _editorManager.SetSelectedGem((GemType)gemBlock.value);
                    _editorManager.SetSelectedColor(ColorType.None);
                    _editorManager.SetSelectedAdvancedBlock(AdvancedBlockId.None);
                }
            }

            GUILayout.Space(5);

            // 实体方块选择
            GUILayout.Label("实体方块:", EditorStyles.miniLabel);
            var entityBlocks = UnifiedBlockConfig.GetAllEntityBlocks();
            foreach (var entityBlock in entityBlocks)
            {
                bool isSelected = _editorManager.SelectedAdvancedBlock == (AdvancedBlockId)entityBlock.id;
                string displayName = $"{entityBlock.Name}";
                if (entityBlock.IsMultiLayer)
                {
                    displayName += $"({entityBlock.MinLayers}-{entityBlock.MaxLayers}层)";
                }
                if (entityBlock.Is2x2)
                {
                    displayName += "(2x2)";
                }

                if (GUILayout.Toggle(isSelected, displayName, GUILayout.Width(120)) && !isSelected)
                {
                    _editorManager.SetSelectedAdvancedBlock((AdvancedBlockId)entityBlock.id);
                    _editorManager.SetSelectedColor(ColorType.None);
                    _editorManager.SetSelectedGem(GemType.None);
                }
            }

            GUILayout.Space(5);

            // 覆盖物方块选择
            GUILayout.Label("覆盖物方块:", EditorStyles.miniLabel);
            var overlayBlocks = UnifiedBlockConfig.GetAllOverlayBlocks();
            foreach (var overlayBlock in overlayBlocks)
            {
                bool isSelected = _editorManager.SelectedAdvancedBlock == (AdvancedBlockId)overlayBlock.id;
                string displayName = $"{overlayBlock.Name}";
                if (overlayBlock.IsMultiLayer)
                {
                    displayName += $"({overlayBlock.MinLayers}-{overlayBlock.MaxLayers}层)";
                }

                if (GUILayout.Toggle(isSelected, displayName, GUILayout.Width(100)) && !isSelected)
                {
                    _editorManager.SetSelectedAdvancedBlock((AdvancedBlockId)overlayBlock.id);
                    _editorManager.SetSelectedColor(ColorType.None);
                    _editorManager.SetSelectedGem(GemType.None);

                    // 重置层数为默认值
                    _selectedOverlayLayers = overlayBlock.MinLayers;
                }
            }

            GUILayout.Space(5);

            // 层数设置面板
            DrawLayerSettingsPanel();
        }



        private void DrawLayerSettingsPanel()
        {
            GUILayout.BeginVertical("box");
            _showLayerSettings = EditorGUILayout.Foldout(_showLayerSettings, "层数设置", true);
            if (_showLayerSettings)
            {
                // 实体方块层数设置
                if (_editorManager.SelectedAdvancedBlock != AdvancedBlockId.None)
                {
                    int blockId = (int)_editorManager.SelectedAdvancedBlock;
                    var entityBlock = UnifiedBlockConfig.GetEntityBlock(blockId);
                    var overlayBlock = UnifiedBlockConfig.GetOverlayBlock(blockId);

                    if (entityBlock != null && entityBlock.IsMultiLayer && !entityBlock.Is2x2)
                    {
                        GUILayout.Label($"{entityBlock.Name}层数:", EditorStyles.miniLabel);
                        int newLayers = EditorGUILayout.IntSlider(_selectedEntityLayers,
                            entityBlock.MinLayers, entityBlock.MaxLayers);
                        if (newLayers != _selectedEntityLayers)
                        {
                            _selectedEntityLayers = newLayers;
                            _gridManager.CustomEntityLayers = newLayers;
                        }
                    }
                    else if (overlayBlock != null && overlayBlock.IsMultiLayer)
                    {
                        GUILayout.Label($"{overlayBlock.Name}层数:", EditorStyles.miniLabel);
                        int newLayers = EditorGUILayout.IntSlider(_selectedOverlayLayers,
                            overlayBlock.MinLayers, overlayBlock.MaxLayers);
                        if (newLayers != _selectedOverlayLayers)
                        {
                            _selectedOverlayLayers = newLayers;
                            _gridManager.CustomOverlayLayers = newLayers;
                        }
                    }
                }

                GUILayout.Space(5);

                // 覆盖物操作按钮
                // if (GUILayout.Button("添加覆盖物到选中瓦片"))
                // {
                //     AddOverlayToSelectedTile();
                // }

                // if (GUILayout.Button("移除选中瓦片的覆盖物"))
                // {
                //     RemoveOverlayFromSelectedTile();
                // }
            }
            GUILayout.EndVertical();
        }

        private void DrawToolsPanel()
        {
            GUILayout.BeginVertical("box");
            GUILayout.Label("工具:", EditorStyles.boldLabel);
            //_showTools = EditorGUILayout.Foldout(_showTools, "工具", true);
            //if (_showTools)
            {
                if (GUILayout.Button("清空关卡"))
                {
                    if (EditorUtility.DisplayDialog("确认", "确定要清空当前关卡吗？", "确定", "取消"))
                    {
                        _editorManager.ClearLevel();
                    }
                }

                if (GUILayout.Button("验证关卡"))
                {
                    bool isValid = _editorManager.ValidateLevel();
                    EditorUtility.DisplayDialog("验证结果",
                        isValid ? "关卡数据有效" : "关卡数据无效", "确定");
                }

                // if (GUILayout.Button("导出文本"))
                // {
                //     string fileName = string.IsNullOrEmpty(_editorManager.CurrentFileName) ?
                //         "level_export" : _editorManager.CurrentFileName + "_export";
                //     FileManager.ExportLevelAsText(_editorManager.CurrentLevelData, fileName);
                // }

                GUILayout.Space(5);

                // 历史记录信息
                GUILayout.Label($"撤销历史: {_editorManager.UndoCount}", EditorStyles.miniLabel);
                GUILayout.Label($"重做历史: {_editorManager.RedoCount}", EditorStyles.miniLabel);

                if (GUILayout.Button("清空历史记录"))
                {
                    _editorManager.ClearHistory();
                }
            }
            GUILayout.EndVertical();
        }

        private void DrawGridArea(Rect gridAreaRect)
        {
            // 绘制网格背景
            EditorGUI.DrawRect(gridAreaRect, new Color(0.15f, 0.15f, 0.15f, 1f));

            // 计算网格居中位置
            var gridContainerRect = new Rect(
                gridAreaRect.x + GRID_PADDING,
                gridAreaRect.y + GRID_PADDING,
                gridAreaRect.width - GRID_PADDING * 2,
                gridAreaRect.height - GRID_PADDING * 2
            );

            // 渲染网格
            _gridRenderer.RenderGrid(gridContainerRect);
        }

        /// <summary>
        /// 获取工具的中文显示名称
        /// </summary>
        private string GetToolDisplayName(EditorToolType tool)
        {
            switch (tool)
            {
                case EditorToolType.Brush: return "放置";
                case EditorToolType.Eraser: return "消除";
                case EditorToolType.Selection: return "选择";
                // 注意：Fill 工具已移除，填充功能通过UI按钮实现
                default: return tool.ToString();
            }
        }

        private string GetAdvancedBlockDisplayName(AdvancedBlockId blockId)
        {
            switch (blockId)
            {
                case AdvancedBlockId.WoodenBox: return "木箱";
                case AdvancedBlockId.Cat: return "小猫";
                case AdvancedBlockId.BirdNest: return "鸟窝";
                case AdvancedBlockId.Leaf: return "树叶";
                case AdvancedBlockId.Ice: return "冰块";
                case AdvancedBlockId.Trigger: return "触发";
                default: return blockId.ToString();
            }
        }

        private string GetColorDisplayName(ColorType color)
        {
            switch (color)
            {
                case ColorType.Red: return "红色";
                case ColorType.Orange: return "橙色";
                case ColorType.Yellow: return "黄色";
                case ColorType.Green: return "绿色";
                case ColorType.SkyBlue: return "天蓝";
                case ColorType.DarkBlue: return "深蓝";
                case ColorType.Purple: return "紫色";
                case ColorType.GemBg: return "宝石底";
                default: return color.ToString();
            }
        }

        private void ShowLoadDialog()
        {
            // 使用输入对话框让用户直接输入文件名
            string fileName = EditorUtility.DisplayDialogComplex("加载关卡",
                $"请输入关卡文件名（不含.json扩展名）\n当前输入: {_loadFileName}",
                "加载", "取消", "浏览") switch
            {
                0 => _loadFileName, // 加载
                1 => null,          // 取消
                2 => ShowFileBrowser(), // 浏览
                _ => null
            };

            if (!string.IsNullOrEmpty(fileName))
            {
                _editorManager.LoadLevel(fileName);
            }
        }

        private string ShowFileBrowser()
        {
            var files = FileManager.GetAllLevelFiles();
            if (files.Length == 0)
            {
                EditorUtility.DisplayDialog("提示", "没有找到关卡文件", "确定");
                return _loadFileName;
            }

            // 显示文件选择菜单
            var menu = new GenericMenu();
            foreach (var file in files)
            {
                menu.AddItem(new GUIContent(file), false, () => {
                    _loadFileName = file;
                    _editorManager.LoadLevel(file);
                });
            }
            menu.ShowAsContext();
            return _loadFileName;
        }

        private void ShowSaveDialog()
        {
            string fileName = EditorUtility.SaveFilePanel("保存关卡", 
                FileManager.LEVEL_LAYOUT_PATH, _newFileName, "json");
            
            if (!string.IsNullOrEmpty(fileName))
            {
                fileName = System.IO.Path.GetFileNameWithoutExtension(fileName);
                _editorManager.SaveLevel(fileName);
            }
        }

        private void OnLevelDataChanged(LevelData levelData)
        {
            Repaint();
        }

        private void OnFileNameChanged(string fileName)
        {
            Repaint();
        }

        private void OnDirtyStateChanged(bool isDirty)
        {
            Repaint();
        }

        private void OnHistoryChanged()
        {
            Repaint();
        }

        /// <summary>
        /// 处理键盘输入和快捷键
        /// </summary>
        private void HandleKeyboardInput()
        {
            Event e = Event.current;

            if (e.type == EventType.KeyDown)
            {
                // Ctrl+Z - 撤销
                if (e.control && e.keyCode == KeyCode.Z && !e.shift)
                {
                    if (_editorManager.CanUndo)
                    {
                        _editorManager.Undo();
                        e.Use();
                    }
                }
                // Ctrl+Y 或 Ctrl+Shift+Z - 重做
                else if ((e.control && e.keyCode == KeyCode.Y) ||
                         (e.control && e.shift && e.keyCode == KeyCode.Z))
                {
                    if (_editorManager.CanRedo)
                    {
                        _editorManager.Redo();
                        e.Use();
                    }
                }
                // Ctrl+S - 保存
                else if (e.control && e.keyCode == KeyCode.S)
                {
                    if (!string.IsNullOrEmpty(_editorManager.CurrentFileName))
                    {
                        _editorManager.SaveLevel();
                        e.Use();
                    }
                }
                // Ctrl+O - 打开
                else if (e.control && e.keyCode == KeyCode.O)
                {
                    ShowLoadDialog();
                    e.Use();
                }
                // Ctrl+N - 新建
                else if (e.control && e.keyCode == KeyCode.N)
                {
                    if (EditorUtility.DisplayDialog("新建关卡", "确定要新建关卡吗？未保存的更改将丢失。", "确定", "取消"))
                    {
                        _editorManager.NewLevel();
                        e.Use();
                    }
                }
                // 工具切换快捷键
                // else if (e.keyCode == KeyCode.B)
                // {
                //     _editorManager.SetTool(EditorToolType.Brush);
                //     e.Use();
                // }
                // else if (e.keyCode == KeyCode.E)
                // {
                //     _editorManager.SetTool(EditorToolType.Eraser);
                //     e.Use();
                // }
                // else if (e.keyCode == KeyCode.S && !e.control)
                // {
                //     _editorManager.SetTool(EditorToolType.Selection);
                //     e.Use();
                // }
                // 显示模式切换快捷键
                else if (e.keyCode == KeyCode.Alpha1)
                {
                    _editorManager.SetDisplayMode(GridDisplayMode.Text);
                    e.Use();
                }
                else if (e.keyCode == KeyCode.Alpha2)
                {
                    _editorManager.SetDisplayMode(GridDisplayMode.Color);
                    e.Use();
                }
                else if (e.keyCode == KeyCode.Alpha3)
                {
                    _editorManager.SetDisplayMode(GridDisplayMode.Icon);
                    e.Use();
                }
                // 快速颜色选择
                else if (e.keyCode >= KeyCode.F1 && e.keyCode <= KeyCode.F7)
                {
                    int colorIndex = e.keyCode - KeyCode.F1 + 1;
                    if (colorIndex <= 7)
                    {
                        _editorManager.SetSelectedColor((ColorType)colorIndex);
                        _editorManager.SetSelectedGem(GemType.None);
                        _editorManager.SetSelectedAdvancedBlock(AdvancedBlockId.None);
                        e.Use();
                    }
                }
            }
        }



        private void AddOverlayToSelectedTile()
        {
            if (_editorManager.SelectedAdvancedBlock == AdvancedBlockId.None)
            {
                EditorUtility.DisplayDialog("提示", "请先选择一个覆盖物方块", "确定");
                return;
            }

            int blockId = (int)_editorManager.SelectedAdvancedBlock;
            var overlayBlock = UnifiedBlockConfig.GetOverlayBlock(blockId);
            if (overlayBlock == null)
            {
                EditorUtility.DisplayDialog("提示", "选中的不是覆盖物方块", "确定");
                return;
            }

            // 这里需要获取当前选中的瓦片位置
            // 暂时使用(0,0)作为示例，实际应该从GridRenderer获取选中位置
            int x = 0, y = 0; // TODO: 获取实际选中位置

            var tile = _editorManager.GetTile(x, y);
            if (tile == null)
            {
                // 创建新瓦片
                tile = new TileData(x, y);
                tile.baseType = "color";
                tile.color = 1;
            }
            else
            {
                tile = tile.Clone();
            }

            bool success = tile.AddOverlay(blockId, _selectedOverlayLayers);
            if (success)
            {
                _editorManager.SetTile(x, y, tile);
                EditorUtility.DisplayDialog("成功", $"已添加{overlayBlock.Name}({_selectedOverlayLayers}层)", "确定");
            }
            else
            {
                EditorUtility.DisplayDialog("失败", "添加覆盖物失败", "确定");
            }
        }

        private void RemoveOverlayFromSelectedTile()
        {
            if (_editorManager.SelectedAdvancedBlock == AdvancedBlockId.None)
            {
                EditorUtility.DisplayDialog("提示", "请先选择一个覆盖物方块", "确定");
                return;
            }

            int blockId = (int)_editorManager.SelectedAdvancedBlock;
            var overlayBlock = UnifiedBlockConfig.GetOverlayBlock(blockId);
            if (overlayBlock == null)
            {
                EditorUtility.DisplayDialog("提示", "选中的不是覆盖物方块", "确定");
                return;
            }

            // 这里需要获取当前选中的瓦片位置
            int x = 0, y = 0; // TODO: 获取实际选中位置

            var tile = _editorManager.GetTile(x, y);
            if (tile == null)
            {
                EditorUtility.DisplayDialog("提示", "该位置没有方块", "确定");
                return;
            }

            tile = tile.Clone();
            bool success = tile.RemoveOverlay(blockId);
            if (success)
            {
                _editorManager.SetTile(x, y, tile);
                EditorUtility.DisplayDialog("成功", $"已移除{overlayBlock.Name}", "确定");
            }
            else
            {
                EditorUtility.DisplayDialog("失败", "该方块上没有此覆盖物", "确定");
            }
        }

        /// <summary>
        /// 填充整个网格
        /// </summary>
        private void FillEntireGrid()
        {
            for (int x = 0; x < 8; x++)
            {
                for (int y = 0; y < 8; y++)
                {
                    _gridManager.HandleBrushTool(x, y);
                }
            }
            Debug.Log("已填充整个网格");
        }

        /// <summary>
        /// 随机填充网格
        /// </summary>
        private void RandomFillGrid()
        {
            var colorBlocks = UnifiedBlockConfig.GetAllColorBlocks();
            var random = new System.Random();

            for (int x = 0; x < 8; x++)
            {
                for (int y = 0; y < 8; y++)
                {
                    // 50%概率放置方块
                    if (random.NextDouble() > 0.5)
                    {
                        // 随机选择颜色
                        var randomColor = colorBlocks[random.Next(colorBlocks.Count)];
                        var originalColor = _editorManager.SelectedColor;
                        var originalGem = _editorManager.SelectedGem;
                        var originalAdvanced = _editorManager.SelectedAdvancedBlock;

                        _editorManager.SetSelectedColor((ColorType)randomColor.value);
                        _editorManager.SetSelectedGem(GemType.None);
                        _editorManager.SetSelectedAdvancedBlock(AdvancedBlockId.None);

                        _gridManager.HandleBrushTool(x, y);

                        // 恢复原始选择
                        _editorManager.SetSelectedColor(originalColor);
                        _editorManager.SetSelectedGem(originalGem);
                        _editorManager.SetSelectedAdvancedBlock(originalAdvanced);
                    }
                }
            }
            Debug.Log("已随机填充网格");
        }

        /// <summary>
        /// 水平镜像网格
        /// </summary>
        private void MirrorGridHorizontal()
        {
            var levelData = _editorManager.CurrentLevelData;
            var tiles = new List<TileData>(levelData.tiles);

            // 清空右半部分
            for (int x = 4; x < 8; x++)
            {
                for (int y = 0; y < 8; y++)
                {
                    _editorManager.RemoveTile(x, y);
                }
            }

            // 复制左半部分到右半部分
            foreach (var tile in tiles)
            {
                if (tile.x < 4)
                {
                    int mirrorX = 7 - tile.x;
                    var newTile = tile.Clone();
                    newTile.x = mirrorX;
                    _editorManager.SetTile(mirrorX, tile.y, newTile);
                }
            }

            Debug.Log("已水平镜像网格");
        }

        /// <summary>
        /// 垂直镜像网格
        /// </summary>
        private void MirrorGridVertical()
        {
            var levelData = _editorManager.CurrentLevelData;
            var tiles = new List<TileData>(levelData.tiles);

            // 清空下半部分
            for (int x = 0; x < 8; x++)
            {
                for (int y = 4; y < 8; y++)
                {
                    _editorManager.RemoveTile(x, y);
                }
            }

            // 复制上半部分到下半部分
            foreach (var tile in tiles)
            {
                if (tile.y < 4)
                {
                    int mirrorY = 7 - tile.y;
                    var newTile = tile.Clone();
                    newTile.y = mirrorY;
                    _editorManager.SetTile(tile.x, mirrorY, newTile);
                }
            }

            Debug.Log("已垂直镜像网格");
        }

        // 右侧面板滚动位置
        private Vector2 _rightPanelScrollPosition;

        /// <summary>
        /// 绘制右侧面板（包含目标配置、统计信息、属性面板）
        /// </summary>
        private void DrawTargetPanel(Rect targetPanelRect)
        {
            // 绘制面板背景
            EditorGUI.DrawRect(targetPanelRect, new Color(0.2f, 0.2f, 0.2f, 1f));

            GUILayout.BeginArea(targetPanelRect);

            // 添加滚动视图
            _rightPanelScrollPosition = GUILayout.BeginScrollView(_rightPanelScrollPosition);

            // 添加一些内边距
            GUILayout.BeginVertical(GUILayout.Width(targetPanelRect.width - 20));
            GUILayout.Space(10);

            // 目标配置面板
            DrawTargetConfigSection();

            GUILayout.Space(15);

            // 统计信息面板
            DrawStatisticsSection();

            GUILayout.Space(15);

            // 属性面板
            DrawPropertiesSection();

            GUILayout.Space(10);
            GUILayout.EndVertical();
            GUILayout.EndScrollView();
            GUILayout.EndArea();
        }

        /// <summary>
        /// 绘制目标配置区域（带折叠功能）
        /// </summary>
        private void DrawTargetConfigSection()
        {
            GUILayout.BeginVertical("box");
            _showTargetConfig = EditorGUILayout.Foldout(_showTargetConfig, "关卡目标配置", true);
            if (_showTargetConfig)
            {
                DrawTargetConfigContent();
            }
            GUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制目标配置内容
        /// </summary>
        private void DrawTargetConfigContent()
        {
            var targetData = _editorManager.CurrentLevelData.targets;
            if (targetData == null)
            {
                targetData = new LevelTargetData();
                _editorManager.CurrentLevelData.targets = targetData;
            }

            // 目标类型切换
            GUILayout.Label("目标类型:", EditorStyles.boldLabel);

            var currentType = targetData.TargetType;

            // 创建中文选项数组
            string[] typeOptions = { "分数关", "目标关" };
            int currentIndex = currentType == LevelTargetType.Score ? 0 : 1;

            int newIndex = EditorGUILayout.Popup(currentIndex, typeOptions);
            var newType = newIndex == 0 ? LevelTargetType.Score : LevelTargetType.Collection;

            if (newType != currentType)
            {
                targetData.TargetType = newType;
                _editorManager.SetDirty(true);
            }

            GUILayout.Space(10);

            // 根据目标类型显示不同的配置界面
            if (targetData.IsScoreLevel)
            {
                DrawScoreTargetConfig(targetData);
            }
            else if (targetData.IsCollectionLevel)
            {
                DrawCollectionTargetConfig(targetData);
            }

            GUILayout.Space(10);

            // 显示目标摘要
            GUILayout.Label("目标摘要:", EditorStyles.boldLabel);

            // 使用文本区域显示摘要，支持换行
            var summaryStyle = new GUIStyle(EditorStyles.textArea)
            {
                wordWrap = true,
                stretchHeight = true
            };

            GUILayout.TextArea(targetData.GetSummary(), summaryStyle, GUILayout.Height(60));
        }

        /// <summary>
        /// 绘制统计信息区域（带折叠功能）
        /// </summary>
        private void DrawStatisticsSection()
        {
            GUILayout.BeginVertical("box");
            _showStatistics = EditorGUILayout.Foldout(_showStatistics, "统计信息", true);
            if (_showStatistics)
            {
                var stats = _editorManager.GetStatistics();
                EditorGUILayout.LabelField($"总方块数: {stats.totalTileCount}");
                EditorGUILayout.LabelField($"颜色方块: {stats.colorBlockCount}");
                EditorGUILayout.LabelField($"宝石方块: {stats.gemBlockCount}");
                EditorGUILayout.LabelField($"特殊方块: {stats.advancedBlockCount}");
                EditorGUILayout.LabelField($"覆盖物: {stats.overlayCount}");
            }
            GUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制属性面板区域（带折叠功能）
        /// </summary>
        private void DrawPropertiesSection()
        {
            GUILayout.BeginVertical("box");
            _showProperties = EditorGUILayout.Foldout(_showProperties, "属性面板", true);
            if (_showProperties)
            {
                DrawPropertiesContent();
            }
            GUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制属性面板内容
        /// </summary>
        private void DrawPropertiesContent()
        {
            // 显示当前选择的信息
            GUILayout.Label("当前选择:", EditorStyles.boldLabel);

            if (_editorManager.SelectedColor != ColorType.None)
            {
                EditorGUILayout.LabelField($"颜色方块: {GetColorDisplayName(_editorManager.SelectedColor)}");
            }
            else if (_editorManager.SelectedGem != GemType.None)
            {
                EditorGUILayout.LabelField($"宝石方块: {_editorManager.SelectedGem}");
            }
            else if (_editorManager.SelectedAdvancedBlock != AdvancedBlockId.None)
            {
                EditorGUILayout.LabelField($"特殊方块: {GetAdvancedBlockDisplayName(_editorManager.SelectedAdvancedBlock)}");
            }
            else
            {
                EditorGUILayout.LabelField("未选择方块");
            }

            GUILayout.Space(5);

            // 显示当前工具信息
            GUILayout.Label("当前工具:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"工具: {GetToolDisplayName(_editorManager.CurrentTool)}");
            EditorGUILayout.LabelField($"显示模式: {_editorManager.DisplayMode}");

            GUILayout.Space(5);

            // 快捷键提示
            GUILayout.Label("快捷键:", EditorStyles.boldLabel);
            // EditorGUILayout.LabelField("B - 放置工具", EditorStyles.miniLabel);
            // EditorGUILayout.LabelField("E - 擦除工具", EditorStyles.miniLabel);
            // EditorGUILayout.LabelField("S - 选择工具", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("Ctrl+Z - 撤销", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("Ctrl+Y - 撤销恢复", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("Ctrl+S - 保存", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("Ctrl+N - 新建", EditorStyles.miniLabel);
            //EditorGUILayout.LabelField("1/2/3 - 显示模式", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("F1-F7 - 快速颜色选择", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("方块右键 - 弹出菜单", EditorStyles.miniLabel);

            GUILayout.Space(5);
        }



        /// <summary>
        /// 绘制分数关配置
        /// </summary>
        private void DrawScoreTargetConfig(LevelTargetData targetData)
        {
            // 使用框架包围分数配置
            GUILayout.BeginVertical("box");

            GUILayout.Label("分数目标", EditorStyles.boldLabel);
            GUILayout.Space(5);

            GUILayout.BeginHorizontal();
            GUILayout.Label("目标分数:", GUILayout.Width(70));
            var newScore = EditorGUILayout.IntField(targetData.targetScore);
            GUILayout.EndHorizontal();

            if (newScore != targetData.targetScore)
            {
                if (newScore > 0)
                {
                    targetData.targetScore = newScore;
                    _editorManager.SetDirty(true);
                }
                else
                {
                    EditorUtility.DisplayDialog("输入错误", "目标分数必须大于0", "确定");
                }
            }

            // 显示分数提示
            EditorGUILayout.HelpBox("玩家需要达到此分数才能通关", MessageType.Info);

            GUILayout.EndVertical();
        }

        /// <summary>
        /// 绘制收集关配置
        /// </summary>
        private void DrawCollectionTargetConfig(LevelTargetData targetData)
        {
            // 使用框架包围收集配置
            GUILayout.BeginVertical("box");

            GUILayout.Label("收集目标", EditorStyles.boldLabel);
            GUILayout.Space(5);

            // 显示当前目标列表
            if (targetData.targetItems.Count > 0)
            {
                for (int i = 0; i < targetData.targetItems.Count; i++)
                {
                    var item = targetData.targetItems[i];

                    GUILayout.BeginHorizontal("box");

                    // 显示目标类型名称
                    GUILayout.Label(item.DisplayName, GUILayout.Width(80));

                    // 数量输入
                    GUILayout.Label("x", GUILayout.Width(15));
                    var newCount = EditorGUILayout.IntField(item.count, GUILayout.Width(50));
                    if (newCount != item.count && newCount > 0)
                    {
                        item.count = newCount;
                        _editorManager.SetDirty(true);
                    }

                    // 删除按钮
                    if (GUILayout.Button("×", GUILayout.Width(25)))
                    {
                        targetData.RemoveTargetItem(item.type);
                        _editorManager.SetDirty(true);
                        break; // 跳出循环，因为列表已修改
                    }

                    GUILayout.EndHorizontal();
                }
            }
            else
            {
                EditorGUILayout.HelpBox("还没有设置收集目标", MessageType.Info);
            }

            GUILayout.Space(5);

            // 添加新目标
            if (targetData.targetItems.Count < 4)
            {
                if (GUILayout.Button(_showAddTargetPanel ? "取消添加" : "添加收集目标"))
                {
                    _showAddTargetPanel = !_showAddTargetPanel;
                }

                if (_showAddTargetPanel)
                {
                    DrawAddTargetPanel(targetData);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("最多只能设置4个收集目标", MessageType.Warning);
            }

            GUILayout.EndVertical();
        }

        // 目标选择相关的UI状态
        private static int _selectedTargetTypeIndex = 0;
        private static int _newTargetCount = 1;
        private static bool _showAddTargetPanel = false;

        /// <summary>
        /// 显示添加目标面板
        /// </summary>
        private void DrawAddTargetPanel(LevelTargetData targetData)
        {
            var availableTypes = TargetTypeDefinitions.GetAllTargetTypes()
                .Where(type => !targetData.targetItems.Any(item => item.type == type))
                .ToArray();

            if (availableTypes.Length == 0)
            {
                EditorUtility.DisplayDialog("提示", "所有目标类型都已添加", "确定");
                return;
            }

            // 创建选项数组
            var options = availableTypes.Select(type => TargetTypeDefinitions.GetTargetTypeName(type)).ToArray();

            // 确保索引在有效范围内
            if (_selectedTargetTypeIndex >= options.Length)
                _selectedTargetTypeIndex = 0;

            GUILayout.BeginVertical("box");
            GUILayout.Label("添加新的收集目标:", EditorStyles.boldLabel);

            // 目标类型选择
            _selectedTargetTypeIndex = EditorGUILayout.Popup("目标类型", _selectedTargetTypeIndex, options);

            // 目标数量输入
            _newTargetCount = EditorGUILayout.IntField("目标数量", _newTargetCount);
            if (_newTargetCount < 1) _newTargetCount = 1;

            GUILayout.BeginHorizontal();

            if (GUILayout.Button("添加"))
            {
                var selectedType = availableTypes[_selectedTargetTypeIndex];
                if (targetData.AddTargetItem(selectedType, _newTargetCount))
                {
                    _editorManager.SetDirty(true);
                    _newTargetCount = 1; // 重置数量
                    _showAddTargetPanel = false; // 关闭添加面板
                }
            }

            if (GUILayout.Button("取消"))
            {
                _showAddTargetPanel = false; // 关闭添加面板
            }

            GUILayout.EndHorizontal();
            GUILayout.EndVertical();
        }
    }
}

#endif
