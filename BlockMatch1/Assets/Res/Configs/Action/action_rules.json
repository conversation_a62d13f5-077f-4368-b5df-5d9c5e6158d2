[{"id": 1, "actionGroupName": "历史最高分=0", "actionGroupId": 101, "actionKey": "BestScore", "operator": "eq", "actionType": "number", "actionValue": "0"}, {"id": 2, "actionGroupName": "0＜历史最高分≤2500", "actionGroupId": 102, "actionKey": "BestScore", "operator": "gt", "actionType": "number", "actionValue": "0"}, {"id": 3, "actionGroupName": "0＜历史最高分≤2500", "actionGroupId": 102, "actionKey": "BestScore", "operator": "lte", "actionType": "number", "actionValue": "2500"}, {"id": 4, "actionGroupName": "2500＜历史最高分≤4000", "actionGroupId": 103, "actionKey": "BestScore", "operator": "gt", "actionType": "number", "actionValue": "2500"}, {"id": 5, "actionGroupName": "2500＜历史最高分≤4000", "actionGroupId": 103, "actionKey": "BestScore", "operator": "lte", "actionType": "number", "actionValue": "4000"}, {"id": 6, "actionGroupName": "历史最高分＞4000", "actionGroupId": 104, "actionKey": "BestScore", "operator": "gt", "actionType": "number", "actionValue": "4000"}]