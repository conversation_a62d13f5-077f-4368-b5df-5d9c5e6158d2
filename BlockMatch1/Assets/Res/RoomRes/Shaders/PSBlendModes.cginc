#ifndef PHOTOSHOP_BLENDMODES_INCLUDED
#define PHOTOSHOP_BLENDMODES_INCLUDED

float overlay( float s, float d )
{
    return (d < 0.5) ? 2.0*s*d : 1.0 - 2.0*(1.0 - s)*(1.0 - d);
}

half3 Overlay(half3 s, half3 d)
{
    half3 c;
    c.x = overlay(s.x, d.x);
    c.y = overlay(s.y, d.y);
    c.z = overlay(s.z, d.z);
    return c;
}

half3 LinearBurn(half3 s, half3 d){

   half3 c;
   c.rgb = s.rgb + d.rgb - half3(1.0,1.0,1.0);
   return c;
}


#endif