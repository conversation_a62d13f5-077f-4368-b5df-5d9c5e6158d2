{"tiles": [{"x": 0, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 1, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 2, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 2, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 5, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 5, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 6, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 3, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 5, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}], "targets": {"targetType": "collection", "targetScore": 0, "targetItems": [{"type": 401, "count": 40}, {"type": 302, "count": 6}]}}