{"tiles": [{"x": 0, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 0, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 0, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 0, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 1, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 1, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 2, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 4, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 6, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 4, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}], "targets": {"targetType": "collection", "targetScore": 0, "targetItems": [{"type": 401, "count": 36}, {"type": 302, "count": 8}]}}