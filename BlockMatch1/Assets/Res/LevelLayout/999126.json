{"tiles": [{"x": 0, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 0, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 1}, {"x": 0, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 1, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 2, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 3, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 3, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 3, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 1}, {"x": 3, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 4, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 4, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 4, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 1}, {"x": 4, "y": 6, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 5, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 302, "blockLayers": 4}, {"x": 6, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}, {"x": 7, "y": 0, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 7, "y": 1, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 2}, {"x": 7, "y": 2, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 301, "blockLayers": 1}, {"x": 7, "y": 7, "baseType": "other", "color": 7, "gem": 0, "advancedBlockId": 401, "isFixed": true}], "targets": {"targetType": "collection", "targetScore": 0, "targetItems": [{"type": 301, "count": 12}, {"type": 401, "count": 12}, {"type": 302, "count": 4}]}}