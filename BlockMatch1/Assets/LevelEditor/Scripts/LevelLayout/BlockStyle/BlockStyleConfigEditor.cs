#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;

namespace LevelEditor.Scripts.LevelLayout
{
    [CustomEditor(typeof(BlockStyleConfig))]
    public class BlockStyleConfigEditor : Editor
    {
        private BlockStyleConfig config;
        private Vector2 scrollPos;

        private void OnEnable()
        {
            config = (BlockStyleConfig)target;
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            EditorGUI.BeginChangeCheck();
            DrawColorStylesSection();
            DrawGemStylesSection();
            DrawQuickAddSection();

            serializedObject.ApplyModifiedProperties();
            if (EditorGUI.EndChangeCheck())
            {
                SaveChanges();
            }
        }
        
        
        private void SaveChanges()
        {
            EditorUtility.SetDirty(target);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void DrawStyleSection<T>(List<T> styles, string label, Action<T> onRemove, Func<T> createNewStyle,  Enum newType)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField(label, EditorStyles.boldLabel);

            for (int i = 0; i < styles.Count; i++)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                EditorGUILayout.BeginHorizontal();
                // 使用反射获取type字段并显示EnumPopup
                var typeField = typeof(T).GetField("type");
                var currentValue = (Enum)typeField.GetValue(styles[i]);
                var newValue = EditorGUILayout.EnumPopup("Type", currentValue);
                typeField.SetValue(styles[i], newValue);

                if (GUILayout.Button("×", GUILayout.Width(20)))
                {
                    onRemove(styles[i]);
                    SaveChanges();
                    break;
                }
                EditorGUILayout.EndHorizontal();

                // 使用反射获取icon字段并显示ObjectField
                var iconField = typeof(T).GetField("icon");
                var currentIcon = (Sprite)iconField.GetValue(styles[i]);
                var newIcon = (Sprite)EditorGUILayout.ObjectField("Icon", currentIcon, typeof(Sprite), false);
                iconField.SetValue(styles[i], newIcon);

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawColorStylesSection()
        {
            DrawStyleSection(config.colorStyles, "Color Block Styles", 
                style => config.RemoveColorStyle(style.type),
                () => new BlockStyleConfig.ColorBlockStyle { type = config.newColorType },
                 config.newColorType);
        }

        private void DrawGemStylesSection()
        {
            DrawStyleSection(config.gemStyles, "Gem Block Styles",
                style => config.RemoveGemStyle(style.type),
                () => new BlockStyleConfig.GemBlockStyle { type = config.newGemType },
                 config.newGemType);
        }

        private void DrawQuickAddSection()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Quick Add", EditorStyles.boldLabel);

            EditorGUILayout.BeginVertical(GUI.skin.box);

            // 添加颜色样式
            EditorGUILayout.BeginHorizontal();
            config.newColorType = (ColorType)EditorGUILayout.EnumPopup("New Color Type", config.newColorType);
            if (GUILayout.Button("Add Color", GUILayout.Width(100)))
            {
                var newStyle = new BlockStyleConfig.ColorBlockStyle
                {
                    type = config.newColorType,
                };
                config.AddColorStyle(newStyle);
            }

            EditorGUILayout.EndHorizontal();

            // 添加宝石样式
            EditorGUILayout.BeginHorizontal();
            config.newGemType = (GemType)EditorGUILayout.EnumPopup("New Gem Type", config.newGemType);
            if (GUILayout.Button("Add Gem", GUILayout.Width(100)))
            {
                var newStyle = new BlockStyleConfig.GemBlockStyle
                {
                    type = config.newGemType
                };
                config.AddGemStyle(newStyle);
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }
    }
}
#endif
