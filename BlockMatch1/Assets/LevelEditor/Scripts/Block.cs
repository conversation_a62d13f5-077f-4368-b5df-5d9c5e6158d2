using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class Block : MonoBehaviour
{
    // Start is called before the first frame update
    public Color BlockColor = Color.white;
    public Image BlockImg;

    public void InitBlock(Color color, Action<Color> callBack)
    {
        GetComponent<Image>().color = color;
        GetComponent<Button>().onClick.AddListener(() =>
        {
            callBack?.Invoke(color);
        });
    }


}
