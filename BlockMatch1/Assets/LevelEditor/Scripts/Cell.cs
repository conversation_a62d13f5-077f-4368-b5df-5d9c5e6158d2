using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

/// <summary>
/// 单元格组件类
/// </summary>
public class Cell : MonoBehaviour, IPointerEnterHandler, IPointerExitHandler, IPointerClickHandler
{
    // 组件引用
    private Image image;            // 单元格图像组件
    private Color originalColor;    // 原始颜色缓存

    /// <summary>
    /// 当前颜色属性
    /// </summary>
    public Color CurrentColor => image.color;

    private Action<Cell> OnClickHandler;
    private Action<Cell> OnEnterHandler;
    private Action OnExitHandler;
    /// <summary>
    /// 初始化单元格
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="defaultColor">默认颜色</param>
    public void Initialize(int x, int y, Color defaultColor, Action<Cell> enterCallBack, Action exitCallBack,Action<Cell> clickCallBack)
    {
        image = GetComponent<Image>();
        originalColor = defaultColor;
        image.color = defaultColor;
        OnEnterHandler = enterCallBack;
        OnExitHandler = exitCallBack;
        OnClickHandler = clickCallBack;
    }

    /// <summary>
    /// 设置单元格颜色
    /// </summary>
    /// <param name="newColor">新的颜色值</param>
    public void SetColor(Color newColor)
    {
        image.color = newColor;
        originalColor = newColor;
    }

    /// <summary>
    /// 显示/隐藏预览效果
    /// </summary>
    /// <param name="show">是否显示预览</param>
    /// <param name="previewColor">预览颜色（可选）</param>
    public void ShowPreview(bool show, Color? previewColor = null)
    {
        image.color = show ? 
            new Color(previewColor.Value.r, previewColor.Value.g, previewColor.Value.b, 0.5f) : 
            originalColor;
    }

    // 鼠标进入事件处理
    public void OnPointerEnter(PointerEventData eventData)
    {
         OnEnterHandler?.Invoke(this);
    }

    // 鼠标离开事件处理
    public void OnPointerExit(PointerEventData eventData)
    {
         OnExitHandler?.Invoke();
    }


    public void OnPointerClick(PointerEventData eventData)
    {
         OnClickHandler?.Invoke(this);
    }
}