/************************************************
 * Config class : TableReplaceRoomNode
 * This file is can not be modify !!!
 * If there is some problem, ask bin.guo.
 ************************************************/

using System;
using System.Collections.Generic;
using TMGame;


[System.Serializable]
public class TableReplaceRoomNode : TableBase
{   
    
    // 挂点ID
    public int id ;
    
    // 下一个挂点; 0:分支结束; -1：房间结束
    public int[] nextNode ;
    
    // 解锁此挂点的 ; 前置挂点ID
    public int[] preNode ;
    
    // 选中此挂点 隐藏其他挂点(三选一)
    public int[] choiceHideNodes ;
    
    // 房间ID
    public int roomId ;
    
    // 是否默认开启
    public bool isOpen ;
    
    // 是否是清扫节点
    public bool isClear ;
    
    // 是否1次清理动画
    public bool oneTimeClean ;
    
    // 解锁挂点需要货币类型
    public int unLockResType ;
    
    // 解锁挂点花费
    public int unLockPrice ;
    
    // 音效
    public int sound ;
    
    // 一次打扫动画音效
    public int oneTimeSound ;
    
    // 多语言名字KEY
    public string nameKey ;
    
    // 图标名字
    public string iconResName ;
    
    // 装修图片
    public string decoraResName ;
    
    // 是否RV广告或得
    public bool isRvGet ;
    
    // 选中此挂点显示的节点
    public string[] showNodes ;
    
    // 播放此挂点动画隐藏的元素
    public string[] hideNodes ;
    
    // 播放动画时开始层级
    public int orderLayer ;
    
    // 聚焦X
    public float focusX ;
    
    // 聚焦Y
    public float focusY ;
    
    // 节点缩放
    public float focusScale ;
    
    // 节点完成时播放星星音效ID，空则不播
    public int starSfx ;
    


    public override int GetID()
    {
        return id;
    }
}
