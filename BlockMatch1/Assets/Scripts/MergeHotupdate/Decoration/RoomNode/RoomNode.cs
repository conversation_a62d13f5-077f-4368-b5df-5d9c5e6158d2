using DecorationRom.Core;
using System;
using System.Collections.Generic;
using TMGame.Storage;
using UnityEngine;
using RoomItemId = System.String;
using RoomNodeId = System.Int64;

public class ControlNode
{
    public int roomNodeId;
    public string findObjName;
}
public class RoomNode
{
    enum ChangeType
    {
        LastAtalsReplace = 1, //将Item下的last的sprite替换为上一个家具的sprite
    }

    private TableRoomNode _config;
    private Home.Core.RoomNodeCfg _graphicConfig;


    private RoomItem _currentItem;
    private List<RoomItem> _itemList;
    private Dictionary<string, RoomItem> _itemMaping;
    private Room _room;
    private RoomNodeId _parentNodeId;
    private List<RoomItem> _cachedItemListWithOutOld =null;
    private List<ControlNode> _showNodes = new List<ControlNode>();
    private Dictionary<string, bool> hideNodesTag = new Dictionary<string, bool>();
    public Transform transform;
    public Room Room { get => _room; }
    public RoomNodeId Id { get => _config != null ? _config.id : _graphicConfig.id; }
    public RoomNodeId ParentId { get => _parentNodeId; set => _parentNodeId = value; }
    public int RenderQueue { get => _graphicConfig.baseNodeCfg.renderQueue + Room.QUEUE_BASE_ITEM; }
    public Vector3 ScreenPos { get => _currentItem != null ? _currentItem.ScreenPos : getDefaultScreenPos(); }
    public Vector3 WorldPos { get => _room.Camera.ScreenToWorldPoint(ScreenPos); }
    public Vector2 Offset { get => _currentItem != null ? _currentItem.Offset : getDefaultOffset(); }
    public TableRoomNode Config { get => _config; set => _config = value; }
    public RoomItem CurrentItem { get => _currentItem; set => _currentItem = value; }

    public List<ControlNode> ShowNodes
    {
        get => _showNodes;
    }
    public List<RoomItem> ItemListWithOutOld
    {
        get
        {
            if (_cachedItemListWithOutOld == null)
            {
                _cachedItemListWithOutOld = _itemList.FindAll(c => !c.IsOld);
            }
            return _cachedItemListWithOutOld;
        }
    }

    public RoomNode(Home.Core.RoomNodeCfg graphicConfig, TableRoomNode gameConfig, Room room)
    {
        _graphicConfig = graphicConfig;
        _room = room;
        _config = gameConfig;
        initNode();
        initShowNode();
    }

    private void initNode()
    {
        var obj = new GameObject($"Node_{_config.id}");
        transform = obj.transform;
        transform.SetParent(_room.transform);

        initItems();
    }

    private void initShowNode()
    {
        _showNodes.Clear();
        
        if(_config.showNodes == null || _config.showNodes.Length == 0)
            return;

        foreach (var config in _config.showNodes)
        {
            string[] spStr = config.Split('#');
            if(spStr == null || spStr.Length == 0)
                continue;

            ControlNode controlNode = new ControlNode();
            controlNode.roomNodeId = int.Parse(spStr[0]);
            controlNode.findObjName = spStr[1];
            
            _showNodes.Add(controlNode);
        }
    }
    private void initItems()
    {
        _itemList = new List<RoomItem>();
        _itemMaping = new Dictionary<string, RoomItem>();
        
        foreach (var config in _graphicConfig.ItemCfgs)
        {
            RoomItem roomItem = new RoomItem(config, this);
            _itemList.Add(roomItem);
            _itemMaping.Add(config.id, roomItem);
        }
    }

    public RoomItem GetItem(RoomItemId itemId)
    {
        if (!_itemMaping.ContainsKey(itemId))
            return null;

        return _itemMaping[itemId];
    }

    public RoomItem GetItem(int index)
    {
        if (index < 0 || index >= _itemList.Count)
            return null;

        return _itemList[index];
    }

    public void UpdateItemOffset()
    {
        SetItem(_currentItem.Id, false, false);
    }

    public void SetItem(RoomItemId itemId, bool change, bool playEffect)
    {
        if (string.IsNullOrEmpty(itemId) || itemId.Equals("0"))
        {
            ShowOldItem();
            return;
        }
        
        var offset = Vector2.zero;
        foreach (var item in _itemList)
        {
            if (!item.Id.Equals(itemId))
                continue;

            _currentItem?.ClearGraphic();
            _currentItem = item;
            _currentItem.UpdateSpriteAndOffset();

            if (change)
                _currentItem.OnChangeDifferentItem();
            else
            {
                _currentItem.OnNormal();
            }
            
            _currentItem.SetPlayEffect(playEffect);
            break;
        }

        //检查是否有子节点需要更新位置
        _room.UpdateNodeIfParentIs(this.Id);

        if (!change)
        {
            SetShowNodes(true, false);
            ReverseControlNodes();
        }
    }

    public void ShowOldItem()
    {
        var hasOldItem = false;
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            if (!item.oldFurItemCfg.isOld)
                continue;
            
            hasOldItem = true;
            SetItem(item.id, false, false);
        }

        if (hasOldItem)
            return;
        
        _currentItem?.ClearGraphic();
    }

    public void ShowNewItem()
    {
        var hasNewItem = false;
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            if (item.oldFurItemCfg.isOld)
                continue;
            
            hasNewItem = true;
            SetItem(item.id, false, false);
        }

        if (hasNewItem)
            return;
        
        _currentItem?.ClearGraphic();
    }

    private Vector3 getDefaultScreenPos()
    {
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            foreach (var offset in item.offsetItemCfg.pos)
            {
                return RoomItemGraphic.ConvertOffsetToScreenPos(_room.Camera, offset.value);
            }
        }

        return Vector3.zero;
    }

    private Vector2 getDefaultOffset()
    {
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            foreach (var offset in item.offsetItemCfg.pos)
            {
                return offset.value;
            }
        }

        return Vector3.zero;
    }

    public bool IsReady()
    {
        if (_currentItem != null)
        {
            return _currentItem.IsReady();
        }
        return true;
    }

    public void PlayCleanRoomAnim(Action animEnd = null)
    {
        RoomItem newItem = null;
        RoomItem oldItem = null;
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            if (!item.oldFurItemCfg.isOld)
            {
                SetItem(item.id, false, false);
                newItem = GetItem(item.id);
            }
            else
            {
                oldItem = GetItem(item.id);
            }
        }

        if (newItem != null)
        {
            oldItem.ClearGraphic();
            newItem.PlayCleanRoomAnim(animEnd);
        }
        else
        {
            oldItem.PlayCleanRoomAnim(animEnd);
        }
    }

    public void PlayEffect()
    {
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            if (item.oldFurItemCfg.isOld)
                continue;

            RoomItem roomItem = GetItem(item.id);
            if(roomItem == null)
                continue;
            
            roomItem.PlayEffect();
        }
    }
    
    public void StopEffect()
    {
        foreach (var item in _graphicConfig.ItemCfgs)
        {
            if (item.oldFurItemCfg.isOld)
                continue;

            RoomItem roomItem = GetItem(item.id);
            if(roomItem == null)
                continue;
            
            roomItem.StopEffect();
        }
    }
    
    
    public void ClearGraphic()
    {
        foreach (var kv in _itemMaping)
        {
            kv.Value.ClearGraphic();
        }
    }
    public void ResetDefault()
    {
        var room = RoomManager.Instance.CurrentInRoom;
        var defaultItemId = room.Data.GetNodeStorage(Id).SelectId;
        SetItem(defaultItemId.ToString(), false, false);
        GetItem(defaultItemId)?.OnDeSelect();
    }
    
    public bool TouchMe(Vector2 screenPos)
    {
        return _currentItem != null ? _currentItem.TouchMe(screenPos) : false;
    }

    public void ReverseControlNodes()
    {
        foreach (var roomNode in _room.AllNodesDic)
        {
            if(roomNode.Value.ShowNodes == null || roomNode.Value.ShowNodes.Count == 0)
                continue;

            foreach (var control in roomNode.Value.ShowNodes)
            {
                if(control.roomNodeId != Id)
                    continue;
                
                if(!string.IsNullOrEmpty(_room.Data.GetNodeStorage(roomNode.Value.Id).SelectId))
                    roomNode.Value.SetShowNodes(true, false);
                break;
            }
        }
    }
    public void SetShowNodes(bool isShow, bool playAnim = true)
    {
        if(_showNodes.Count == 0)
            return;

        foreach (var kv in _showNodes)
        {
            RoomNode roomNode = Room.GetNode(kv.roomNodeId);
            if(roomNode == null)
                continue;
            
           
            for(int i = 0; i < roomNode.transform.childCount; i++)
            {
                Transform child = roomNode.transform.GetChild(i);
                Transform findobj = child.Find(kv.findObjName);
                if(findobj == null)
                    continue;
            
                findobj.gameObject.SetActive(isShow);
                
                if (isShow && playAnim)
                {
                    Animator animator = findobj.GetComponent<Animator>();
                    if (animator != null)
                        animator.Play("appear");
                }
            }
        }
    }

    private Vector3 initPos = Vector3.zero;
    private Vector3 outScreen = new Vector3(10000, 10000, 0);
    public void SetChoiceNodes(bool isShow)
    {
        if(_config.choiceHideNodes == null || _config.choiceHideNodes.Length == 0)
            return;

        foreach (var nodeId in _config.choiceHideNodes)
        {
            RoomNode roomNode = Room.GetNode(nodeId);
            if(roomNode == null)
                continue;

            roomNode.transform.gameObject.transform.localPosition = isShow ? initPos : outScreen;
            
            StorageNode storageNode = Room.Data.GetNodeStorage(nodeId);
             if(storageNode != null && storageNode.Status == (int) RoomItem.Status.Received)
                roomNode.SetShowNodes(isShow, false);
        }
    }
    
    public void HideNodes()
    {
        if(_config == null)
            return;
        
        if(_config.hideNodes == null || _config.hideNodes.Length == 0)
            return;
        
        hideNodesTag.Clear();
        foreach (var name in _config.hideNodes)
        {
            for (int i = 0; i < transform.transform.childCount; i++)
            {
                Transform child = transform.GetChild(i);
                if(child.name.Contains("_old"))
                    continue;
                
                Transform findobj = child.Find(name);
                if (findobj == null)
                    continue;

                if (!hideNodesTag.ContainsKey(name))
                    hideNodesTag.Add(name, findobj.gameObject.activeSelf);

                findobj.gameObject.SetActive(false);
            }
        }
    }

    public void RecoverNodes()
    {
        if(_config == null)
            return;
        
        if(_config.hideNodes == null || _config.hideNodes.Length == 0)
            return;
        
        foreach (var name in _config.hideNodes)
        {
            for (int i = 0; i < transform.transform.childCount; i++)
            {
                Transform child = transform.GetChild(i);
                Transform findobj = child.Find(name);
                if (findobj == null)
                    continue;

                bool isShow = false;

                if (hideNodesTag.ContainsKey(name))
                    isShow = hideNodesTag[name];

                findobj.gameObject.SetActive(isShow);
            }
        }
    }
}