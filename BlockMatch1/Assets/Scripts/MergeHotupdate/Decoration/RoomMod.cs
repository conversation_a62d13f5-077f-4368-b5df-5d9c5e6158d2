using System;
using DecorationRom.Event;
using DragonPlus.Core;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using Gameplay.BI;
using TMGame;
using TMGame.Storage;

namespace DecorationRom
{
    public class RoomMod : LogicSys
    {
        public override void Init()
        {
        }

        public void OnLoginSuccess()
        {
            TableConfigManage.Instance.InitTableConfigs();
            RoomManager.Instance.UpdateRoomChapterStorage();
            RoomManager.Instance.UpdateRoomNodeStatus();
            RoomManager.Instance.ReplaceRoomNode(true);
        }

        private void OnUnlockNextRoom(OnUnlockNextRoomEvent roomEvent)
        {
            StorageRoomCommon storageHome = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            storageHome.RoomData[RoomManager.Instance.CurRoomId].IsGetAward = true;
            RoomManager.Instance.CurrentInRoom.FocusOff();
            RoomTimeLineManager.Instance.PlayRoomAnim(RoomManager.Instance.CurRoomId, () =>
            {
                storageHome.RoomData[RoomManager.Instance.CurRoomId].IsPlayAnim = true;
                GameUtils.SetEventSystemEnable(true);

                int lastUnlockedRoomId = RoomManager.Instance.GetLastUnLockRoomId();
                int nextRoomId = RoomManager.Instance.GetNextRoomId(lastUnlockedRoomId);
                if (nextRoomId < 0)
                {
                    return;
                }
                UIView_UnlockRoom.OpenData openData = new UIView_UnlockRoom.OpenData()
                {
                    nextRoomId = nextRoomId,
                    showUnlockView = true,
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_UnlockRoom, openData);

                // RoomBI.SendRoomCompleteThirdBI(RoomManager.Instance.GetCurrRoomIndex());
                // RoomBI.SendDecoEvent_complete_room(RoomManager.Instance.CurRoomId);

                // TODO FIX ERROR
                // RoomBI.SendHomeGameEvent(BiEventCooking.Types.GameEventType.GameEventHomeFinish, RoomManager.Instance.CurRoomId.ToString(), "0", decNum.ToString(), rvNum.ToString());
            });
        }

        public override void Start()
        {
            RegisterEvents();
        }

        private void RegisterEvents()
        {
            EventBus.Subscribe<OnUnlockNextRoomEvent>(OnUnlockNextRoom);
            EventBus.Subscribe<EvtDownloadOver>(OnDownloadOver);
        }

        private void OnDownloadOver(EvtDownloadOver evt)
        {
            try
            {
                if (!evt.isByDownload || !evt.isSucceed || !GameGlobal.GetMod<ModFsm>().CheckState<FsmState_Home>() || !evt.tag.Contains("Room"))
                {
                    return;
                }

                int downloadRoomId = int.Parse(evt.tag.Replace("Room", ""));
                if (RoomManager.Instance.CurRoomId == downloadRoomId || downloadRoomId != RoomManager.Instance.GetLastUnLockRoomId())
                {
                    return;
                }

                RoomResSystem.Instance.ChangeRoom(downloadRoomId);
            }
            catch (Exception e)
            {
                CLog.Error($"Error in OnDownloadOver: {e.Message}");
            }
        }



        public override void Update()
        {
        }


        public override void OnShutDown()
        {
            base.OnShutDown();
            UnregisterEvents();
        }

        private void UnregisterEvents()
        {
             EventBus.Unsubscribe<OnUnlockNextRoomEvent>(OnUnlockNextRoom);
             EventBus.Unsubscribe<EvtDownloadOver>(OnDownloadOver);
        }
    }
}
