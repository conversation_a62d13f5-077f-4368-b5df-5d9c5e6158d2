using System;
using DragonPlus;
using DragonPlus.Core;
using DragonPlus.Save;
using TMGame;
using TMGame.Storage;


public enum RoomAreaState: byte
{
    Lock = 0,
    UnLock= 1,
    Finish = 2,
}

public class ChooseAreaItem : ChooseAreaItemBase
{
    public RoomAreaState AreaState;
    public TableRoomChapter confChapter;
    public Action<int> OnClickArea;
    protected override void OnCreate()
    {
        base.OnCreate();
        UIBtn_ContinueButton.onClick.AddListener(OnClickContinue);
        UIBtn_ViewButton.onClick.AddListener(OnClickContinue);
    }

    private void OnClickContinue()
    {
        //跳转到room view
        OnClickArea?.Invoke(confChapter.id);
    }

    public void SetData(TableRoomChapter tableRoomList)
    { 
        confChapter = tableRoomList;
       CoreUtils.SetImg(UIImg_Icon,CoreUtils.GetSprite("RoomAreaAtlas",tableRoomList.buildBg,UIImg_Icon.gameObject));
       UITxt_TitleText.text =  CoreUtils.GetLocalization(confChapter.buildName) ;
       RefState();
    }

    private void RefState()
    {
        AreaState = RoomAreaState.Lock;
        if (RoomManager.Instance.IsRoomsFinished(confChapter.roomIds))
        {
            AreaState = RoomAreaState.Finish;
        }else if (RoomManager.Instance.IsRoomsFinished(confChapter.roomIds))
        {
            AreaState = RoomAreaState.UnLock;
        }
        else
        {
            AreaState = RoomAreaState.Lock;
        }
        UINode_FinishGroup.gameObject.SetActive(AreaState== RoomAreaState.Finish);
        UINode_LockGroup.gameObject.SetActive(AreaState== RoomAreaState.Lock);
        UINode_NormalGroup.gameObject.SetActive(AreaState== RoomAreaState.UnLock);
        
        UISlider_LockProgress.value = 0;
        int finishedCount = 0;
        int totalRoomCnt = confChapter.roomNumber;
        for (int i = 0; i < confChapter.roomNumber; i++)
        {
            int roomId = confChapter.roomIds[i];
            if (RoomManager.Instance.IsRoomFinish(roomId))
            {
                finishedCount++;
            }
        }
        UISlider_Progress.value = (finishedCount * 1.00f/ totalRoomCnt);
        UITxt_ProgressNormalLabel.text = $"{finishedCount}/{totalRoomCnt}";
        UITxt_ProgressLabel.text = $"{finishedCount}/{totalRoomCnt}";
    }
}
