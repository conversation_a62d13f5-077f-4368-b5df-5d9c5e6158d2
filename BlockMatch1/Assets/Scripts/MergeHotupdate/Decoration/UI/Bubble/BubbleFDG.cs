using System;
using System.Collections.Generic;
using System.Net;
using DG.Tweening;
using DragonU3DSDK;
using EpForceDirectedGraph.cs;
using Framework;
using Gameplay;
using UnityEngine;

public class BubbleFDG {
    /// <summary>
    /// 使用 力导向图（force directed graph） 算法的变体 模拟泡泡的排挤运动
    /// </summary>
    private Graph _fdgGraph = new Graph();

    private ForceDirected2D _fdgforceDirected2D;
    private FDGRenderer _fdgRenderer;

    public float fdgRepulsion = 900000.0f; // 电荷斥力
    public float fdgDamping = 0.2f; // 摩擦系数
    public float fdgThreadhold = 0.1f; // 总动量低于这个值，则不再模拟

    private float fdgStiffness = 81.76f; // 弹簧系数，弹簧没有用到
    private float fdgMovableNodeMass = 3f; // 泡泡质量

    private void _UpdateFDG(int maxCount, float deltaTimePerCount) {
        for (int i = 0; i < maxCount - 1; i++) {
            if (_fdgforceDirected2D != null) {
                _fdgRenderer?.Draw(deltaTimePerCount);

                if (_fdgforceDirected2D.WithinThreashold) {
                    _fdgforceDirected2D = null;
                }
            } else {
                break;
            }
        }

        if (_fdgforceDirected2D != null && _fdgRenderer != null) {
            _fdgRenderer.forceRender = true;
            _fdgRenderer?.Draw(deltaTimePerCount);
            _fdgforceDirected2D = null;
        }
    }

    public void StartSimulatePos(List<IFDGNode> uiFDGNodes, Action<int, AbstractVector> onSetPosition) {
        try {
            _fdgGraph.Clear();

            // 为每个泡泡建立力导向图节点（电荷）， 这个力导向图没用到边（弹簧）

            if (uiFDGNodes != null) {
                for (int i = 0; i < uiFDGNodes.Count; i++) {
                    var uiFDGNode = uiFDGNodes[i];
                    var name = string.Format("bubble_{0}", uiFDGNode.Id.ToString());
                    var pos = uiFDGNode.GetPosition(Space.Self);
                    NodeData data;

                    data = new NodeData();
                    data.mass = fdgMovableNodeMass;
                    data.label = name;
                    data.initialPostion = new FDGVector2(pos.x, pos.y);
                    var movableNode = new FDGNode(data.label, data);
                    _fdgGraph.AddNode(movableNode);
                    movableNode.size = uiFDGNode.Size;
                    movableNode.id = uiFDGNode.Id;
                    movableNode.Pinned = uiFDGNode.IsPin;
                }
            }

            // 设置 边的弹性系数， 电荷的斥力，摩擦系数
            _fdgforceDirected2D = new ForceDirected2D(_fdgGraph, fdgStiffness, fdgRepulsion, fdgDamping);

            // 自定义 电荷的斥力算法        
            _fdgforceDirected2D.customApplyCoulombsLaw = _CustomApplyCoulombsLaw;
            _fdgforceDirected2D.AttractToCenter = false;
            _fdgforceDirected2D.Threadshold = fdgThreadhold;
            _fdgRenderer = new FDGRenderer(_fdgforceDirected2D, onSetPosition);
            _UpdateFDG(50, 0.03f);
            _fdgRenderer = null;
        } catch (Exception e) {
            CLog.Error(e);
        }
    }

    class FDGRenderer : AbstractRenderer {
        private GameObject _parent;
        public bool forceRender = false;
        private Action<int, AbstractVector> _onSetPosition;

        public FDGRenderer(IForceDirected iForceDirected, Action<int, AbstractVector> onSetPosition) : base(iForceDirected) {
            _onSetPosition = onSetPosition;
        }

        public override void Clear() {
            // Clear previous drawing if needed
            // will be called when AbstractRenderer:Draw is called
        }

        protected override void drawEdge(EpForceDirectedGraph.cs.Edge iEdge, AbstractVector iPosition1, AbstractVector iPosition2)
        {
        }



        protected override void drawNode(Node iNode, AbstractVector iPosition) {
            try {
                if (iNode.Pinned) {
                    return;
                }

                if (forceDirected.WithinThreashold || forceRender) {
                    var fdgNode = iNode as FDGNode;
                    if (fdgNode != null) {
                        _onSetPosition(fdgNode.id, iPosition);
                    }
                }
            } catch (Exception e) {
                CLog.Error(e);
            }
        }
    }

    class FDGNode : Node {
        public FDGNode(string iId, NodeData iData = null) : base(iId, iData) { }

        public float size = 0;
        public int id;
    }

    private void _CustomApplyCoulombsLaw(Node n1, Node n2, Point point1, Point point2) {
        try {
            AbstractVector d = point1.position - point2.position;
            float distance = d.Magnitude() + 0.1f;

            var newNode1 = n1 as FDGNode;
            var newNode2 = n2 as FDGNode;
            if (newNode1 != null && newNode2 != null) {
                // 电荷超出半径不再受力
                if ((newNode1.size + newNode2.size) / 2 < distance) {
                    return;
                }
            } else {
                return;
            }

            AbstractVector direction = d.Normalize();
            if (n1.Pinned && n2.Pinned) {
                point1.ApplyForce(direction * 0.0f);
                point2.ApplyForce(direction * 0.0f);
            } else if (n1.Pinned) {
                point1.ApplyForce(direction * 0.0f);
                point2.ApplyForce((direction * _fdgforceDirected2D.Repulsion) / (distance * -1.0f));
            } else if (n2.Pinned) {
                point1.ApplyForce((direction * _fdgforceDirected2D.Repulsion) / (distance));
                point2.ApplyForce(direction * 0.0f);
            } else {
                point1.ApplyForce((direction * _fdgforceDirected2D.Repulsion) / (distance * 0.5f));
                point2.ApplyForce((direction * _fdgforceDirected2D.Repulsion) / (distance * -0.5f));
            }
        } catch (Exception e) {
            CLog.Error(e);
        }
    }
}