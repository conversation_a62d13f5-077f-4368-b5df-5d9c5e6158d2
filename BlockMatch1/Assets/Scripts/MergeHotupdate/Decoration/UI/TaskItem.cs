using System;
using DecorationRom.Event;
using DG.Tweening;
using DragonPlus;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine;
using UnityEngine.UI;

public class TaskItem : TaskItemBase
{
    private TableRoomNode _tableRoomNode;

    public Transform PlayGuide;
    protected override void OnCreate()
    {
        base.OnCreate();
        UIBtn_ClaimButton.onClick.AddListener(OnClickClaim);
        PlayGuide = UIBtn_ClaimButton.transform;
    }

    private void OnClickClaim()
    {
        //Game.GetMod<GuideSys>().FinishCurrent(GuideTargetType.DecTaskItemPlay);
        // if (!GameGlobal.GetMod<ModBag>()
        //     .CanAfford((EItemType)_tableRoomNode.unLockResType, _tableRoomNode.unLockPrice))
        // {
        //     var asmrInfo = ASMR.Model.Instance.TryOpenAsmr();
        //     if (asmrInfo == null)
        //     {
        //         UIView_PopupNoMoney.ShowUI();   
        //     }
        //     else
        //     {
        //         Game.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_TipsWindow, asmrInfo);
        //     }
        //     return;
        // }
        // EventBus.Dispatch<TryOpenRoomEvent>(new TryOpenRoomEvent(_tableRoomNode.id));
        // Game.GetMgr<UIMgr>().Close(UIViewName.UIView_AreaTaskMain);
        
        
    }

    public int ReVindex;
    protected int Index;
    public void Init(TableRoomNode tableRoomNode,int index= 0)
    {
        _tableRoomNode = tableRoomNode;
        Index = index;
        var room = RoomManager.Instance.CurrentInRoom;
        var _itemList = room.GetNode(tableRoomNode.id).ItemListWithOutOld;
        if (_itemList!=null && _itemList.Count >= 1)
        {
            UIImg_Icon.sprite = _itemList[0].Icon;
        }
        else  if (!string.IsNullOrEmpty(_tableRoomNode.iconResName))
        {
            CoreUtils.SetImg(UIImg_Icon, CoreUtils.GetSprite("RoomItemAtlas", _tableRoomNode.iconResName, UIImg_Icon.gameObject));
        }

        bool canOford = false;
        // bool canOford = GameGlobal.GetMod<ModBag>()
        //     .CanAfford((EItemType)_tableRoomNode.unLockResType, _tableRoomNode.unLockPrice);
        string color = canOford ? "white" : "red";
        string price = $"<color={color}>{tableRoomNode.unLockPrice}</color>";
        UITxt_TaskName.text =  CoreUtils.GetLocalization(_tableRoomNode.nameKey);
        UITxt_NumText.text = price;
        CoreUtils.SetItemImg(UIImg_StarIcon,tableRoomNode.unLockResType);
        UIImg_FinishIcon.gameObject.SetActive(false);
        UIBtn_ClaimButton.gameObject.SetActive(true);
      
    }

    private Sequence _tween;
    
    public void PlayCompAnim(Action playEndCallBack)
    {
        UIImg_FinishIcon.gameObject.SetActive(true);
        UIBtn_ClaimButton.gameObject.SetActive(false);
        _tween?.Kill();
        DOTween.Kill(UIImg_FinishIcon.transform);
        UIImg_FinishIcon.transform.localScale = Vector3.zero;
        _tween = DOTween.Sequence();
        _tween.Append(UIImg_Icon.transform.DOScale(0.8f, 0.4f)).Append(UIImg_FinishIcon.transform.DOScale(1, 0.25f));
        _tween.AppendInterval(0.4f);
        _tween.Append(GO.transform.DOScale(0, 0.35f));
        _tween.OnComplete(() =>
        {
            CloseUIWidget(this,true);
            playEndCallBack?.Invoke();
        });
        _tween.Play();
    }

    protected override void OnClose()
    {
        base.OnClose();
        _tween?.Kill();
        _tween = null;
    }

    public void PlayMoveAnim(int i)
    {
        if (Index < ReVindex)
        {
            return;
        }
        GO.transform.transform.DOLocalMoveY(GO.transform.transform.localPosition.y + 108f,0.35f);
        //GO.transform.transform.localPosition = new Vector3(318, -54, 0);
        // GO.transform.DOLocalMoveY(108f, 0.5f);
    }
}
