// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/27/14:42
// Ver : 1.0.0
// Description : SoundMgr.cs
// ChangeLog :
// **********************************************

using System.Collections.Generic;
using DG.Tweening;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using UnityEngine;

namespace TMGame
{
    public class SoundMgr : IManager
    {
        protected AudioSource bgAudioSource;
        protected string bgAudioAddress;
        protected static GameObject soundContainer;
        protected float musicMaxVolume = 1.0f;
        protected Dictionary<string, AudioClip> soundAssetsDict;
        protected List<AudioSource> sfxAudioSourceList = new List<AudioSource>();

        public class SaveSpot
        {
            public string musicName;
            public float progress;
        }

        protected Dictionary<string, SaveSpot> musicSaveSpotList;

        public void Init()
        {
            soundContainer = GameObject.Find("SoundContainer");

            if (soundContainer == null)
            {
                soundContainer = new GameObject("SoundContainer");
            }

            soundAssetsDict = new Dictionary<string, AudioClip>();

            musicSaveSpotList = new Dictionary<string, SaveSpot>();
        }
        public void Start()
        {
        }
        public void Update()
        {
        }

        public void Dispose()
        {
            Clear();
        }

        public void PlayButtonClick()
        {
            
        }

        public void PlayBgMusic(string address, bool startFromBegin = false)
        {
            PlayBgMusicInternal(address, startFromBegin);
        }

        protected void PlayBgMusicInternal(string address, bool startFromBegin = false)
        {
            if (bgAudioAddress == address)
            {
                if (bgAudioSource != null && !bgAudioSource.isPlaying)
                {
                    bgAudioSource.UnPause();
                    return;
                }
            }

            if (!soundAssetsDict.ContainsKey(address))
            {
                var audioClip = GameGlobal.GetMgr<ResMgr>().GetRes<AudioClip>(address).GetInstance(GameGlobal.DontDestoryRoot);
                if (audioClip != null)
                    soundAssetsDict[address] = audioClip;
            }

            if (soundAssetsDict[address] != null)
            {
                if (musicSaveSpotList.ContainsKey(address))
                {
                    PlayBgMusic(soundAssetsDict[address], address, musicSaveSpotList[address].progress);
                }
                else
                {
                    PlayBgMusic(soundAssetsDict[address], address, 0);
                }
            }
            else
            {
                Log.Error("Load Audio Clip Failed:" + address);
            }
        }

       
        protected void PlayBgMusic(AudioClip clip, string clipAddress, float progress = 0)
        {
            if (bgAudioSource == null)
            {
                bgAudioSource = soundContainer.AddComponent<AudioSource>();
                bgAudioSource.volume = musicMaxVolume;
            }

            if (bgAudioSource.isPlaying)
            {
                bgAudioSource.Stop();
            }

            bgAudioAddress = clipAddress;
            bgAudioSource.clip = clip;
            bgAudioSource.loop = true;
            bgAudioSource.time = progress;
            bgAudioSource.Play();
            bgAudioSource.DOKill();
        }
       
        public void PlaySfxOneShot(string address)
        {
            PlayOneShotAudio(address);
        }

        private void PlayOneShotAudio(string address)
        {
            if (!string.IsNullOrEmpty(address) && !CheckAudioIsPlaying(address))
            {
                PlaySfx(address);
            }
        }

        public float PlaySfx(int id, bool loop = false)
        {
            var conf = GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Sound>(id);
            if (null == conf)
            {
                return 0;
            }

            return PlaySfx(conf.Sound_name, loop);
        }
        
        public float PlaySfx(string address, bool loop = false)
        {
            if (!string.IsNullOrEmpty(address))
                return PlaySfxInternal(address, loop);

            return 0;
        }

        public float GetSfxDuration(string address)
        {
            if (!string.IsNullOrEmpty(address))
            {
                if (soundAssetsDict.ContainsKey(address))
                {
                    AudioSource source = GetAudioSource();
                    source.clip = soundAssetsDict[address];
                    if (source.clip) return source.clip.length;
                    return source.time;
                }
                else
                {
                    var audioClip = GameGlobal.GetMgr<ResMgr>().GetRes<AudioClip>(address).GetInstance(GameGlobal.DontDestoryRoot);
                    if (audioClip == null)
                    {
                        return 0;
                    }
                    AudioSource source = GetAudioSource();
                    source.clip = audioClip;
                    soundAssetsDict.Add(address, audioClip);
                    if (audioClip) return audioClip.length;
                    return source.time;
                }
            }

            return 0;
        }

        protected float PlaySfxInternal(string address, bool loop = false)
        {
            if (TMGame.GameGlobal.GetMod<ConfigSys>().SoundClose)
                return 0;

            if (soundAssetsDict.ContainsKey(address))
            {
                AudioSource source = GetAudioSource();
                source.clip = soundAssetsDict[address];
                source.loop = loop;
                source.Play();
                if (source.clip) return source.clip.length;
                return source.time;
            }
            else
            {
                var audioClip = GameGlobal.GetMgr<ResMgr>().GetRes<AudioClip>(address).GetInstance(GameGlobal.DontDestoryRoot);
                if (audioClip == null)
                {
                    return 0;
                }
                AudioSource source = GetAudioSource();
                source.loop = loop;
                source.clip = audioClip;
                source.Play();
                soundAssetsDict.Add(address, audioClip);
                if (audioClip) return audioClip.length;
                return source.time;
            }
        }

        public void StopSfx(string address)
        {
            StopSfxInternal(address);
        }

        public void StopSfx(int id)
        {
            var conf = GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Sound>(id);
            if (null == conf)
            {
                return;
            }

            StopSfx(conf.Sound_name);
        }
        
        public void StopSfxInternal(string address)
        {
            var audioSource = GetAudioSourceBySfx(address);

            if (audioSource != null)
            {
                audioSource.Stop();
                audioSource.clip = null;
            }
        }

        public AudioSource GetAudioSourceBySfx(string address)
        {
            for (int i = 0; i < sfxAudioSourceList.Count; i++)
            {
                //一般不会有名字包含的情况。
                if (sfxAudioSourceList[i].clip && sfxAudioSourceList[i].clip.name.Contains(address))
                {
                    return sfxAudioSourceList[i];
                }
            }

            return null;
        }

        
        private AudioSource GetAudioSource()
        {
            for (int i = 0; i < sfxAudioSourceList.Count; i++)
            {
                if (!sfxAudioSourceList[i].isPlaying)
                {
                    return sfxAudioSourceList[i];
                }
            }

            var source = soundContainer.AddComponent<AudioSource>();
            sfxAudioSourceList.Add(source);
            return source;
        }

        private bool CheckHasAudioIsPlaying(string audioAddress)
        {
            for (int i = 0; i < sfxAudioSourceList.Count; i++)
            {
                if (sfxAudioSourceList[i].isPlaying && sfxAudioSourceList[i].clip.name == audioAddress)
                {
                    return true;
                }
            }

            return false;
        }

        public bool CheckAudioIsPlaying(string audioAddress)
        {
            return CheckHasAudioIsPlaying(audioAddress);
        }

        public void Clear()
        {
            if (bgAudioSource != null && bgAudioSource.isPlaying)
            {
                bgAudioSource.Stop();
            }

            musicSaveSpotList.Clear();

            for (int i = sfxAudioSourceList.Count - 1; i >= 0; i--)
            {
                UnityEngine.Object.Destroy(sfxAudioSourceList[i]);
                sfxAudioSourceList.RemoveAt(i);
            }

            sfxAudioSourceList.Clear();

            if (bgAudioSource)
                UnityEngine.Object.Destroy(bgAudioSource);

            soundAssetsDict.Clear();
        }

        public void OnMusicStateChanged(bool enable)
        {
            if (!enable)
            {
                musicMaxVolume = 0.00001f;

                if (bgAudioSource)
                {
                    bgAudioSource.volume = musicMaxVolume;
                }
            }
            else
            {
                musicMaxVolume = 1.0f;

                if (bgAudioSource)
                {
                    bgAudioSource.volume = musicMaxVolume;
                }
            }
        }

        public void OnSoundStateChanged(bool enable)
        {
            if (!enable)
            {
                foreach (var audioSource in sfxAudioSourceList)
                {
                    audioSource.Stop();
                }
            }
        }

        public void PauseAllSounds()
        {
            foreach (var audioSource in sfxAudioSourceList)
            {
                if (audioSource.clip != null)
                    audioSource.Pause();
            }
        }

        public void UnPauseAllSounds()
        {
            foreach (var audioSource in sfxAudioSourceList)
            {
                if (audioSource.clip != null)
                    audioSource.UnPause();
            }
        }

        public void StopAllSound()
        {
            foreach (var audioSource in sfxAudioSourceList)
            {
                if (audioSource.clip != null)
                {
                    audioSource.Stop();
                    audioSource.clip = null;
                }
            }
        }

        public void PauseBGMusic()
        {
            if (bgAudioSource != null && bgAudioSource.clip != null)
            {
                bgAudioSource.Pause();
            }
        }

        public void UnPauseBGMusic()
        {
            if (bgAudioSource != null && bgAudioSource.clip != null)
            {
                bgAudioSource.UnPause();
            }
        }
    }
}