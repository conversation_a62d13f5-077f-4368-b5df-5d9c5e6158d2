// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/27/10:56
// Ver : 1.0.0
// Description : ObjectPool.cs
// ChangeLog :
// **********************************************

using System;
using System.Collections.Generic;
using DragonPlus.Core;
using UnityEngine;

namespace TMGame
{
    public class PoolMgr : Singleton<PoolMgr>, IManager
    {
        protected Dictionary<string, PoolBase> pools = new Dictionary<string, PoolBase>();

        private const int updateLimit = 5;
        private float updateTime = 0f;

        public void Init()
        {
        }
        public void Start()
        {
    
        }

        public void Dispose()
        {
            foreach (var kv in pools)
            {
                kv.Value.OnShutDown();
            }

            pools.Clear();
        }


        public void CreatePool<T>(string poolName, int poolMaxCount) where T : PoolBase
        {
            if (pools.ContainsKey(poolName))
            {
                return;
            }

            pools[poolName] = Activator.CreateInstance<T>();
            pools[poolName].InitPool(poolName, poolMaxCount);
        }

        public void Update()
        {
            updateTime += Time.deltaTime;
            if (updateTime < updateLimit)
                return;

            updateTime -= updateLimit;

            foreach (KeyValuePair<string, PoolBase> kv in pools)
            {
                kv.Value.Update();
            }
        }


        public T Spawn<T>(string poolName) where T : class
        {
            var pool = GetPool<PoolBase<T>>(poolName);
            if (pool == null)
                return null;

            return pool.Spawn();
        }

        public void DeSpawn<T>(string poolName, T poolObj) where T : class
        {
            if (poolObj == null)
                return;

            var pool = GetPool<PoolBase<T>>(poolName);
            if (pool == null)
                return;

            pool.DeSpawn(poolObj);
        }

        private T GetPool<T>(string poolName) where T : PoolBase
        {
            if (!pools.ContainsKey(poolName))
                return null;

            return (T) pools[poolName];
        }
    }
}