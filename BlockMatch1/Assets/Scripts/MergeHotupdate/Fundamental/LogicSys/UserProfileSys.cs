using System.Collections.Generic;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonPlus.Save;
using TMGame.Storage;
using UnityEngine;
using DragonU3DSDK.Network.API.Protocol;

namespace TMGame
{
    public class UserProfileSys : LogicSys
    {
        private StorageGlobal _storageGlobal;

        public override void Init()
        {
            base.Init();

            _storageGlobal = SDK<IStorage>.Instance.Get<StorageGlobal>();
        }

        public override void SubscribeEvents()
        {
            base.SubscribeEvents();
        }

        #region Buff

        public long GetBuffLeftTime(EItemType eItemType)
        {
            long leftTime = 0;
            if (_storageGlobal.Buff != null && _storageGlobal.Buff.ContainsKey((int)eItemType))
            {
                leftTime = _storageGlobal.Buff[(int)eItemType] * 1000 - TMUtility.GetTimeStamp();
            }

            return leftTime > 0 ? leftTime : 0;
        }

        public long GetBuffLeftTimeSec(EItemType eItemType)
        {
            long leftTime = 0;
            if (_storageGlobal.Buff != null && _storageGlobal.Buff.ContainsKey((int)eItemType))
            {
                leftTime = _storageGlobal.Buff[(int)eItemType] * 1000 - TMUtility.GetTimeStamp();
            }

            return leftTime > 0 ? leftTime / 1000 : 0;
        }

        public bool InBuffTime(EItemType eItemType)
        {
            return GetBuffLeftTime(eItemType) > 0;
        }

        public void SetBuffTime(EItemType eItemType, long timeMillSecond)
        {
            _storageGlobal.Buff[(int)eItemType] = timeMillSecond;
        }

        public void AddBuffTime(EItemType eItemType, long timeMillSecond, BIHelper.ItemChangeReasonArgs reason)
        {
            var leftTime = GetBuffLeftTime(eItemType);
            if (_storageGlobal.Buff.ContainsKey((int)eItemType))
            {
                if (leftTime > 0)
                {
                    _storageGlobal.Buff[(int)eItemType] += (long)(timeMillSecond * 0.001);
                }
                else
                {
                    _storageGlobal.Buff[(int)eItemType] =
                        (long)((leftTime + timeMillSecond + TMUtility.GetTimeStamp()) * 0.001);
                }
            }
            else
            {
                _storageGlobal.Buff.Add((int)eItemType,
                    (long)((leftTime + timeMillSecond + TMUtility.GetTimeStamp()) * 0.001));
            }
            long totalNum = GetBuffLeftTimeSec(eItemType);
            BIHelper.SendItemChangeEvent(BIHelper.GetBIItemByLogicItemType(eItemType),(int)(timeMillSecond * 0.001) , (ulong)totalNum, reason);
        }

        #endregion

        private string GetCurrencyKey(EItemType resId)
        {
            return "currency_" + (int)resId;
        }

        #region Settle Item

        public void SettleReward(Table_Global_Item tableGameItem, BIHelper.ItemChangeReasonArgs reason)
        {
            // var cfg = GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Item>(tableGameItem.ItemId);
            switch (tableGameItem.GetItemType())
            {
                case EItemType.Key:
                    AddItem(EItemType.Key, tableGameItem.Amount, reason,false);
                    break;

                case EItemType.Coin:
                    AddItem(EItemType.Coin, tableGameItem.Amount, reason,false);
                    break;

                case EItemType.Star:
                    AddItem(EItemType.Star, tableGameItem.Amount, reason);
                    break;

                case EItemType.Diamond:
                    AddItem(EItemType.Diamond, tableGameItem.Amount, reason);
                    break;

                case EItemType.MergeBox:
                case EItemType.Magnet:
                case EItemType.Rotate:
                case EItemType.Bomb:
                case EItemType.Convert:
                case EItemType.Lightning:
                case EItemType.Clock:
                    AddItem(tableGameItem.GetItemType(), tableGameItem.Amount, reason);
                    break;

                case EItemType.EnergyInfinity:
                case EItemType.WeeklyChallengeBuff:
                    AddBuffTime(tableGameItem.GetItemType(), tableGameItem.Amount * 1000, reason);
                    break;

                case EItemType.Lightning_Infinity:
                case EItemType.Clock_Infinity:
                    GameGlobal.GetMod<ModBag>().AddItem(tableGameItem.GetItemType(), tableGameItem.Amount, reason);
                    break;
            }
        }

        public void SettleRewards(List<DragonPlus.Config.Global.Table_Global_Item> items,
            BIHelper.ItemChangeReasonArgs reason)
        {
            foreach (var item in items)
            {
                SettleReward(item, reason);
            }
        }

        #endregion

        public int GetItemCount(EItemType eItemType)
        {
            string key = GetCurrencyKey(eItemType);
            if (_storageGlobal.Currency.TryGetValue(key, out var number))
            {
                return number.GetCurrencyValue();
            }

            return 0;
        }

        public bool CanAfford(EItemType eItemType, int itemCount)
        {
            string key = GetCurrencyKey(eItemType);
            if (_storageGlobal.Currency.ContainsKey(key))
            {
                return _storageGlobal.Currency[key].GetCurrencyValue() >= itemCount;
            }

            return false;
        }

        public int GetNeedValue(EItemType eItemType, int itemCount)
        {
            string key = GetCurrencyKey(eItemType);
            if (_storageGlobal.Currency.ContainsKey(key))
            {
                return itemCount - _storageGlobal.Currency[key].GetCurrencyValue();
            }

            return 0;
        }

        public bool ConsumeItem(EItemType eItemType, int itemCount, BIHelper.ItemChangeReasonArgs changeReason)
        {
            if (CanAfford(eItemType, itemCount))
            {
                string key = GetCurrencyKey(eItemType);
                if (_storageGlobal.Currency.ContainsKey(key))
                {
                    var currentValue = _storageGlobal.Currency[key].GetCurrencyValue();
                    _storageGlobal.Currency[key].SetCurrencyValue(currentValue - itemCount);
                    EventBus.Dispatch(new EventCurrencyChange(eItemType, true, -itemCount));
                    SendItemChange(eItemType, -itemCount, changeReason);
                    return true;
                }
            }

            return false;
        }

        public void AddItem(EItemType eItemType, int itemCount, BIHelper.ItemChangeReasonArgs changeReason,
            bool isRefreshInfo = true)
        {
            string key = GetCurrencyKey(eItemType);
            if (_storageGlobal.Currency.ContainsKey(key))
            {
                var newCount = _storageGlobal.Currency[key].GetCurrencyValue() + itemCount;
                newCount = Mathf.Max(0, newCount);
                _storageGlobal.Currency[key].SetCurrencyValue(newCount);
            }
            else
            {
                StorageCurrency storageCoin = new StorageCurrency();
                storageCoin.SetCurrencyValue(itemCount);
                _storageGlobal.Currency.Add(key, storageCoin);
            }

            if (isRefreshInfo)
                EventBus.Dispatch(new EventCurrencyChange(eItemType, true, itemCount));
            SendItemChange(eItemType, itemCount, changeReason);
        }

        //经济系统版本升级，玩家持有金币乘x10
        public void FixEconomic()
        {
            string key = GetCurrencyKey(EItemType.Coin);
            if (_storageGlobal.Currency.ContainsKey(key))
            {
                var newCount = _storageGlobal.Currency[key].GetCurrencyValue();
                newCount = Mathf.Max(0, newCount * 10);
                _storageGlobal.Currency[key].SetCurrencyValue(newCount);
            }
        }

        public void SendItemChange(EItemType itemType, int itemCount, BIHelper.ItemChangeReasonArgs changeReason)
        {
            var cfg = GameGlobal.GetMgr<ConfigMgr>().GetConfig<Table_Global_Item>((int)itemType);
            long totalNum = cfg.Infinity
                ? GameGlobal.GetMod<ModBag>().GetInfinityLeftTime(itemType)
                : GameGlobal.GetMod<ModBag>().GetItemCount(itemType);
            BIHelper.SendItemChangeEvent(BIHelper.GetBIItemByLogicItemType(itemType), itemCount, (ulong)totalNum, changeReason);
        }
        
       
        #region Star Chest

        public int GetStarChestTotalCount()
        {
            return _storageGlobal.StarChest.TotalStars;
        }

        public int GetStarChestCurIndex()
        {
            return _storageGlobal.StarChest.CurIndex;
        }

        public void ClaimStarChest(int delta)
        {
            _storageGlobal.StarChest.TotalStars -= delta;
            _storageGlobal.StarChest.CurIndex += 1;
        }

        public void AddStarChest(int delta)
        {
            _storageGlobal.StarChest.TotalStars += delta;
        }

        #endregion

        #region Level Chest

        public int GetLevelChestTotalCount()
        {
            return _storageGlobal.LevelChest.TotalLevel;
        }

        public int GetLevelChestCurIndex()
        {
            return _storageGlobal.LevelChest.CurIndex;
        }

        public void ClaimLevelChest(int delta)
        {
            _storageGlobal.LevelChest.TotalLevel -= delta;
            _storageGlobal.LevelChest.CurIndex += 1;
            _storageGlobal.LevelChest.LastShowedLevel = 0;
        }

        public void AddLevelChest(int delta)
        {
            _storageGlobal.LevelChest.TotalLevel += delta;
        }

        #endregion
    }
}