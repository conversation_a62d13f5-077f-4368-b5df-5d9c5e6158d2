using Cysharp.Threading.Tasks;
using DecorationRom.Event;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;

namespace MergeHotupdate.Game
{
    public class HomeMod:LogicSys
    {
        public static bool ShowGuideing = true;
        public override void Init()
        {
            base.Init();
        }

        public override void Start()
        {
            base.Start();
            EventBus.Subscribe<ExitBlockPlayEvent>(OnExitBlockPlayGame);

        }

        public override void OnShutDown()
        {
            base.OnShutDown();
            EventBus.Unsubscribe<ExitBlockPlayEvent>(OnExitBlockPlayGame);
        }

        private void OnExitBlockPlayGame(ExitBlockPlayEvent evt)
        { 
            GameGlobal.GetMod<AdSys>().ResetGameWinDoubleAd();
            if (evt.Type == 0)
            {
                return;
            }
            if (evt.Type == 1)
            {
                var guide = GameGlobal.GetMod<GuideSys>();
                if (!guide.IsFinished("GUIDE_106"))
                {
                    guide.Trigger(GuideTrigger.OnClickDecoration, "1");
                }
            }
            EnterHome().Forget();
        }

        public bool firstEnter = true;

        public async UniTaskVoid EnterHome()
        {
            RoomManager.Instance.Show();
            // 打开主界面
            ModTip.CheckTipView();
            var uiCurrency = GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_CurrencyGroup);
            var uiBase = GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_HomeMain);
        
            //await ShowTopGuide();
            // -----开始 主界面效果
            GameUtils.SetEventSystemEnable(false);

            // 飞道具
            await GameGlobal.GetMod<ModFly>().CheckToFlyItemDict();
            GameGlobal.GetMod<ModFly>().ClearToFlyItemDict();
       
            GameUtils.SetEventSystemEnable(true);
            // 弹窗逻辑
            await GameGlobal.GetMod<ModPopup>().CheckPopup();

            if (firstEnter)
            {
                // GameUtils.SetEventSystemEnable(false);
                // GameGlobal.GetMod<ModCoroutine>().StartCoroutine(RoomUtility.RoomDecorationBoxLogic((end) =>
                // {
                //
                // }, false));
               // RoomDecorationBoxLogic();
            }
            firstEnter = false;

        }

        private void RoomDecorationBoxLogic()
        {
            if (RoomManager.Instance.CurrentInRoom == null)
            {
                return;
            }

            int roomId = RoomManager.Instance.CurRoomId;
            if (!RoomManager.Instance.IsRoomFinish(roomId))
            {
                return;
            }
       
            StorageRoomCommon storageHome = SDK<IStorage>.Instance.Get<StorageRoomCommon>();
            if (!storageHome.RoomData.ContainsKey(roomId))
            {
                return;
            }
            bool getReward =  storageHome.RoomData[RoomManager.Instance.CurRoomId].IsGetAward;
            bool isPlayAnim = storageHome.RoomData[roomId].IsPlayAnim;
            CLog.Error($"=========roomId:{roomId} isPlayAnim:{isPlayAnim } getReward:{getReward}=============");
            if (getReward)
            {
                RoomManager.Instance.CurrentInRoom.FocusOff();
                RoomManager.Instance.CanUnLockNewRoom();
            }
            else
            {
                GameUtils.SetEventSystemEnable(false);
                RoomManager.Instance.CurrentInRoom.FocusOff();
                RoomTimeLineManager.Instance.PlayRoomAnim(RoomManager.Instance.CurRoomId, () =>
                {
                    storageHome.RoomData[RoomManager.Instance.CurRoomId].IsPlayAnim = true;
                    EventBus.Dispatch<RoomAnimationEndEvent>(new RoomAnimationEndEvent(roomId));
                    CLog.Error($"=========roomId:{roomId}  play anim end=============");
                    GameUtils.SetEventSystemEnable(true);
                });
            }

        }


    }
}