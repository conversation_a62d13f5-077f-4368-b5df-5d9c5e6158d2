/*
 * @file ELocale
 * 设置 - 枚举类 - 语言类型
 * <AUTHOR>
 */

namespace DragonPlus
{
    public static class Locale
    {
        public const string NONE = "none";
        public const string ENGLISH = "en";
        public const string FRENCH = "fr";
        public const string GERMAN = "de";
        public const string PORTUGUESE = "pt";
        public const string SPANISH = "es";
        public const string ITALIAN = "it";
        public const string INDONESIAN = "id";
        public const string RUSSIAN = "ru";
        public const string VIETNAMESE = "vi";
        public const string TURKISH = "tr";
        public const string THAI = "th";
        public const string JAPANESE = "jp";
        public const string KOREA = "kr";
        public const string CHINESE_SIMPLIFIED = "zh";
        public const string CHINESE_TRADITION = "zht";
        public const string HINDI = "hi";
        public const string DUTCH = "nl"; // 荷兰
        public const string MALAYSIA = "ms"; // 马来西亚
        public const string ARABIC = "ar"; // 阿拉伯
        public const string Dutch = "nl";// 荷兰
    }
}