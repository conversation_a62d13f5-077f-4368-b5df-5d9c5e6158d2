// <PERSON>Sharper disable CommentTypo
// <PERSON>Sharper disable String<PERSON>iteralTypo
// ReSharper disable MemberCanBePrivate.Global
/************************************************
 * Dynamic ConfigHub Manager class : DynamicConfig
 * This file is can not be modify !!!
 * If there is some problem, ask y<PERSON><PERSON>.<EMAIL>
 ************************************************/

using System;
using System.Reflection;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using TMGame;
using DragonPlus.Config;
using DragonPlus.Core;

namespace DragonPlus.ConfigHub.Dynamic
{
    public class DynamicConfig : IConfig
    {
        public static DynamicConfig Instance
        {
            get
            {
                return SDK<IConfigHub>.Instance.Get<DynamicConfig>();
            }
        }

        public string Guid => "config_dynamic";
        public int VersionMinIOS => 1;
        public int VersionMinAndroid => 1;

        public MetaData MetaData { get; set; }

        public IConfigCacheHandler CacheHandler { get; set; }

        protected List<string> SubModules => new List<string>
        {
            "Mapping",
            "Group",
        };
        private readonly Dictionary<Type, string> typeToEnum = new Dictionary<Type, string>
        {
            [typeof(Mapping)] = "Mapping",
            [typeof(Group)] = "Group",
        };
        private List<Mapping> MappingList;
        private List<Group> GroupList;

        public bool IsRemote { get; set; }
        public bool IsLoaded { get; set; }

        public List<T> GetConfig<T>(CacheOperate cacheOp = CacheOperate.None, long cacheDuration = -1)
        {
            if (!IsLoaded)
                SDK<IConfigHub>.Instance.LoadConfig(Guid);

            CacheHandler.ProcessCache(this, cacheOp, cacheDuration);

            List<T> cfg;
            var subModule = typeToEnum[typeof(T)];
            switch (subModule)
            {
                case "Mapping":
                    cfg = MappingList as List<T>;
                    break;

                case "Group":
                    cfg = GroupList as List<T>;
                    break;

                default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
            }
            return cfg;
        }

        protected bool CheckTable(Hashtable table)
        {
            if (!table.ContainsKey("mapping")) return false;
            if (!table.ContainsKey("group")) return false;
            return true;
        }

        private bool TryParseJsonData(string configJson)
        {
            try
            {
                if (string.IsNullOrEmpty(configJson))
                    return false;
                var table = JsonConvert.DeserializeObject<Hashtable>(configJson);
                if (table == null || !CheckTable(table))
                    return false;
                foreach (var subModule in SubModules)
                {
                    switch (subModule)
                    {
                        case "Mapping":
                            MappingList = JsonConvert.DeserializeObject<List<Mapping>>(JsonConvert.SerializeObject(table["mapping"]));
                            break;

                        case "Group":
                            GroupList = JsonConvert.DeserializeObject<List<Group>>(JsonConvert.SerializeObject(table["group"]));
                            break;

                        default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
                    }
                }

                return true;
            }
            catch (Exception e)
            {
                Log.Error("Error when parse json:{e}");
                return false;
            }
        }

        public void InitConfig(MetaData metaData, string jsonData = null)
        {
            IsRemote = true;
            if (metaData == null || !TryParseJsonData(jsonData))
            {
                var ta = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>("usergroup_dynamic").GetInstance(GameGlobal.DontDestoryRoot);
                if (!TryParseJsonData(ta.text))
                {
                    Log.Error("Load Configs/UserGroup_dynamic error!");
                    return;
                }
                IsRemote = false;
                metaData = CacheHandler.GetMetaDataCached(this);
            }

            MetaData = metaData;

            PropertyInfo pInfo;
            foreach (var subModule in SubModules)
            {
                if (IsRemote)
                    continue;

                switch (subModule)
                {
                    case "Mapping":
                        pInfo = typeof(Mapping).GetProperty("UserGroup");
                        if (pInfo != null && pInfo.PropertyType == typeof(int))
                            MappingList = MappingList.FindAll(cfg => (int)pInfo.GetValue(cfg) == metaData.GroupId);
                        break;

                    case "Group":
                        pInfo = typeof(Group).GetProperty("UserGroup");
                        if (pInfo != null && pInfo.PropertyType == typeof(int))
                            GroupList = GroupList.FindAll(cfg => (int)pInfo.GetValue(cfg) == metaData.GroupId);
                        break;

                    default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
                }
            }

            IsLoaded = true;
#if DEVELOPMENT_BUILD
            Log.Info($"InitConfig:{CacheHandler.GetDebugString(this,false)}");
#endif
        }

        private List<Rules> RulesList;

        public bool HasGroup(int groupId)
        {
            if (RulesList == null || RulesList.Count == 0)
            {
                var ta = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>("usergroup_dynamic").GetInstance(GameGlobal.DontDestoryRoot);
                if (string.IsNullOrEmpty(ta.text))
                {
                    Log.Error("Load Configs/UserGroup/dynamic error!");
                    return false;
                }
                var table = JsonConvert.DeserializeObject<Hashtable>(ta.text);
                RulesList = JsonConvert.DeserializeObject<List<Rules>>(JsonConvert.SerializeObject(table["rules"]));
            }
            return RulesList.Exists(r => r.GroupId == groupId);
        }
    }
}