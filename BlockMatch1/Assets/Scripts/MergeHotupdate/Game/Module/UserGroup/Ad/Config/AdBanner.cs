// ReS<PERSON>per disable CommentTypo
// ReS<PERSON>per disable String<PERSON>iteralTypo
// ReSharper disable MemberCanBePrivate.Global
/************************************************
 * ConfigHub class : AdBanner
 * This file is can not be modify !!!
 * If there is some problem, ask yun<PERSON>.<EMAIL>
 ************************************************/

using System.Collections.Generic;

namespace DragonPlus.ConfigHub.Ad
{
    public class AdBanner
    {   
        
        /// <summary>
        /// #
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 广告位
        /// </summary>
        public int PlaceId { get; set; }
        
        /// <summary>
        /// BANNER广告组; 100=显示; 200=无
        /// </summary>
        public int GroupId { get; set; }
        
        /// <summary>
        /// 通关XX关后解锁广告
        /// </summary>
        public int UnlockLevel { get; set; }
        
        /// <summary>
        /// 达到XX分后解锁广告
        /// </summary>
        public int UnlockScore { get; set; }
        
    }
}