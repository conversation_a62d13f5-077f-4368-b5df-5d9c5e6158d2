/************************************************
 * Game Config Manager class : GameConfigManager
 * This file is can not be modify !!!
 * If there is some problem, ask <EMAIL>
 ************************************************/

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using DragonU3DSDK;
using DragonPlus.Core;
using TMGame;

namespace DragonPlus.Config.Global
{
    [Obsolete("废弃")]
    public partial class GameConfigManager
    {   
        
        public Table_Global_Global TableGameGlobalConfig => getConfig<Table_Global_Global>()[0];
        public List<Table_Global_NotificationInfo> NotificationInfoList => getConfig<Table_Global_NotificationInfo>();
        public List<Table_Global_Item> ItemList => getConfig<Table_Global_Item>();
        //public List<Table_Global_DailyBonus> DailyBonusList => getConfig<Table_Global_DailyBonus>();
        public List<Table_Global_Shop> ShopList => getConfig<Table_Global_Shop>();
        //public List<Table_Global_LevelChest> LevelChestList => getConfig<Table_Global_LevelChest>();
        //public List<Table_Global_WeeklyChallenge> WeeklyChallengeList => getConfig<Table_Global_WeeklyChallenge>();
        //public List<Table_Global_WeeklyChallengeReward> WeeklyChallengeRewardList => getConfig<Table_Global_WeeklyChallengeReward>();
        public List<Table_Global_PushNotification> PushNotificationList => getConfig<Table_Global_PushNotification>();
        
        private List<Table_Global_Global> globalList;
        private List<Table_Global_NotificationInfo> notificationinfoList;
        private List<Table_Global_Item> itemList;
        private List<Table_Global_Shop> shopList;
        private List<Table_Global_PushNotification> pushnotificationList;
        
        private readonly Dictionary<Type, string> typeToEnum = new Dictionary<Type,string> { 
            [typeof(Table_Global_Global)] = "global",
            [typeof(Table_Global_NotificationInfo)] = "notificationinfo",
            [typeof(Table_Global_Item)] = "item",
            [typeof(Table_Global_Shop)] = "shop",
            [typeof(Table_Global_PushNotification)] = "pushnotification",
            
        };
        private void tryLoad(string subModule)
        {
            switch (subModule)
            { 
                case "global": if (globalList != null) return; break;
                case "notificationinfo": if (notificationinfoList != null) return; break;
                case "item": if (itemList != null) return; break;
                case "shop": if (shopList != null) return; break;
                case "pushnotification": if (pushnotificationList != null) return; break;
                
                default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
            }
            var path = $"global_{subModule}";
            var ta = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>(path).GetInstance(GameGlobal.DontDestoryRoot);
            if (string.IsNullOrEmpty(ta.text))
            {
                Log.Error($"Load {path} error!");
                return;
            }
            switch (subModule)
            { 
                case "global": globalList = JsonConvert.DeserializeObject<List<Table_Global_Global>>(ta.text); break;
                case "notificationinfo": notificationinfoList = JsonConvert.DeserializeObject<List<Table_Global_NotificationInfo>>(ta.text); break;
                case "item": itemList = JsonConvert.DeserializeObject<List<Table_Global_Item>>(ta.text); break;
                case "shop": shopList = JsonConvert.DeserializeObject<List<Table_Global_Shop>>(ta.text); break;
                case "pushnotification": pushnotificationList = JsonConvert.DeserializeObject<List<Table_Global_PushNotification>>(ta.text); break;
                
                default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
            }
        }
        private List<T> getConfig<T>()
        {
            var subModule = typeToEnum[typeof(T)];
            tryLoad(subModule);
            switch (subModule)
            { 
                case "global": return globalList as List<T>;
                case "notificationinfo": return notificationinfoList as List<T>;
                case "item": return itemList as List<T>;
                case "shop": return shopList as List<T>;
                case "pushnotification": return pushnotificationList as List<T>;
                
                default: throw new ArgumentOutOfRangeException(nameof(subModule), subModule, null);
            }
        }
    }
}