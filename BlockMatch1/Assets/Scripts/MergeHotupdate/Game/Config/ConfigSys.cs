// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/06/29/10:19
// Ver : 1.0.0
// Description : ConfigSys.cs
// ChangeLog :
// **********************************************

using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Threading;
using Cysharp.Threading.Tasks;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using DragonPlus.Save;
using Newtonsoft.Json;
using TMGame.Storage;
using UnityEngine;

namespace TMGame
{
    public class ConfigSys : LogicSys
    {
        public override int Priority => 500;

        private IList<string> CensoredWords;

        #region LoadConfigs

        public override void Start()
        {
            base.Start();

            LoadCensorWordConfig();

            TMUtility.EnableVibrate(!VibrateClose);
            GameGlobal.GetMgr<SoundMgr>().OnMusicStateChanged(!MusicClose);
            GameGlobal.GetMgr<SoundMgr>().OnSoundStateChanged(!SoundClose);
        }

        private async UniTask LoadCensorWordConfig()
        {
            GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>("forbidden", dto =>
            {
                var badWords = dto.GetInstance(GameGlobal.DontDestoryRoot);
                CensoredWords = new List<string>(Regex.Split(badWords.ToString(), "\r\n|\r|\n"));
            });
        }

        #endregion

        /// <summary>
        /// 审查是否存在敏感词
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public bool CensorText(string text)
        {
            string textToLower = text.ToLower();
            foreach (string censoredWord in CensoredWords)
            {
                if (textToLower.Contains(censoredWord))
                {
                    return true;
                }
            }

            return false;
        }

        public bool MusicClose
        {
            get
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                return storageCook.MusicClose;
            }
            set
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                storageCook.MusicClose = value;
                GameGlobal.GetMgr<SoundMgr>().OnMusicStateChanged(!value);
            }
        }

        public bool SoundClose
        {
            get
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                return storageCook.SoundEffectClose;
            }
            set
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                storageCook.SoundEffectClose = value;
                GameGlobal.GetMgr<SoundMgr>().OnSoundStateChanged(!value);
            }
        }

        public bool VibrateClose
        {
            get
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                return storageCook.VibrateClose;
            }
            set
            {
                var storageCook = SDK<IStorage>.Instance.Get<StorageGlobal>();
                storageCook.VibrateClose = value;
                TMUtility.EnableVibrate(!value);
            }
        }
    }
}