#if DEBUG || DEVELOPMENT_BUILD
namespace SRDebugger.UI
{
    using Scripts;
    using Services;
    using SRF;
    using SRF.Service;
    using UnityEngine;

    public class DebugPanelRoot : SRMonoBehaviourEx
    {
        [RequiredField] public Canvas Canvas;

        [RequiredField] public CanvasGroup CanvasGroup;

        [RequiredField] public DebuggerTabController TabController;

        public void Close()
        {
            SRServiceManager.GetService<IDebugService>().HideDebugPanel();
        }

        public void CloseAndDestroy()
        {
            SRServiceManager.GetService<IDebugService>().DestroyDebugPanel();
        }
    }
}

#endif