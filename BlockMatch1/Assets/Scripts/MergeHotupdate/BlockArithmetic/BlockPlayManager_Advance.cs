using Framework;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using TMGame;
using System.Threading.Tasks;

public partial class BlockPlayManager
{

    ulong curGridLayout_det = 0;        // 8x8布局的64位的整数--表达盘面格子信息--行列式
    ulong curGridLayout_col = 0;        // 8x8布局的64位的整数--表达盘面格子信息--列行式
    List<int> modifyRowEdges = new List<int>(); //需要刷新横向边
    List<int> modifyColEdges = new List<int>(); //需要刷新纵向边

    public List<Edge>[] edgeBucketesAdvance = new List<Edge>[BLOCK_BOARD_SIZE * 2 + 2];
    public List<ulong> rowColMask = new List<ulong>(BLOCK_BOARD_SIZE * 2);

    int boardGridCount = 0;
    int rowColVariability = 0;
    int edgeCountAdvance = 0;
    private bool bNeedSyncEdgeCount = false;

    public ulong GetBoardInfo()
    {
        return curGridLayout_det;
    }

    public int EdgeCountAdvance
    {
        get
        {
            if (bNeedSyncEdgeCount)
            {
                bNeedSyncEdgeCount = false;
                RefreshEdge();
            }
            return edgeCountAdvance;
        }
    }

    public int RowColVariability
    {
        get
        {
            if (bNeedSyncEdgeCount)
            {
                bNeedSyncEdgeCount = false;
                RefreshEdge();
            }
            return rowColVariability;
        }
    }

    public void ClearBoardLayout()
    {
        boardGridCount = 0;
        curGridLayout_det = 0;
        curGridLayout_col = 0;
    }

    void HandleBoardLayout(int row, int col, bool show)
    {
        var gridIndex = col + row * boardWidth;
        if (gridIndex < 0 || gridIndex >= 64)
        {
            CLog.Exception($"HandleBoardLayout--设置盘面信息发现异常，偏移量[{gridIndex}]有误");
            return;
        }
        #region 计算列行式坐标
        //顺时针旋转90度
        var theRow = col;
        var theCol = boardHeight - row - 1;
        //-----------------------------------

        //左右翻转180度
        theCol = boardWidth - theCol - 1;
        //-----------------------------------
        #endregion
        var tempIndex = theCol + theRow * boardWidth;
        if (tempIndex < 0 || tempIndex >= 64)
        {
            CLog.Exception($"HandleBoardLayout--设置盘面信息发现异常，偏移量[{tempIndex}]有误--列行式");
            return;
        }

        var preLayOut = curGridLayout_det;
        if (show)
        {
            curGridLayout_det |= ((ulong)1 << gridIndex);
            curGridLayout_col |= ((ulong)1 << tempIndex);

            if (preLayOut != curGridLayout_det) boardGridCount++;
        }
        else
        {
            curGridLayout_det &= ~((ulong)1 << gridIndex);
            curGridLayout_col &= ~((ulong)1 << tempIndex);

            if (preLayOut != curGridLayout_det) boardGridCount--;
        }

        var theEdge = row;
        if (modifyRowEdges.Contains(theEdge) == false) modifyRowEdges.Add(theEdge);
        theEdge = row + 1;
        if (modifyRowEdges.Contains(theEdge) == false) modifyRowEdges.Add(theEdge);

        theEdge = col;
        if (modifyColEdges.Contains(theEdge) == false) modifyColEdges.Add(theEdge);
        theEdge = col + 1;
        if (modifyColEdges.Contains(theEdge) == false) modifyColEdges.Add(theEdge);

        bNeedSyncConnectedComponentsCount = true;
        bNeedSyncEdgeCount = true;
    }

    public int EdgeBucketOffset(int pos, bool isRow = true)
    {
        if (!isRow)
        {
            return pos;
        }
        else
        {
            return pos + BlockPlayManager.BLOCK_BOARD_SIZE + 1;
        }
    }

    void RefreshEdgeForRow(int edgeRow)
    {
        if (edgeRow <= 0 || edgeRow >= boardHeight) return;


        {
            var gridRow1 = edgeRow - 1;
            var gridRow2 = edgeRow;
            var row1Value = gridRow1 >= 0 ? (byte)(curGridLayout_det >> (boardWidth * gridRow1)) : (byte)0;
            var tempValue = (byte)(row1Value ^ (curGridLayout_det >> (boardWidth * gridRow2)));
            var edgePos = edgeRow;
            var bOffset = EdgeBucketOffset(edgePos);
            if (bOffset >= edgeBucketesAdvance.Length)
            {
                CLog.Exception($"RefreshEdgeForRow 出现异常, bOffset:{bOffset}有误");
                return;
            }

            var tempList = edgeBucketesAdvance[bOffset];
            var theCount = tempList.Count;
            var theRowColCount = 0;
            if (theCount > 0)
            {
                theRowColCount = tempList.Sum(ele => ele.rowColValue);
                tempList.Clear();
            }

            var theNewCount = 0;
            var theNewRowColCount = 0;
            if (tempValue > 0)
            {
                int begin = -1;
                EnumEdgeOrientation theOrientation = EnumEdgeOrientation.EDO_None;
                for (var i = 0; i < boardWidth; i++)
                {

                    var theValue = tempValue & (1 << i);
                    if (theValue != 0)
                    {
                        if (i > 0)
                        {
                            var curValue = (row1Value >> i) & 1;
                            var preHave = (row1Value >> (i - 1)) & 1;
                            if (curValue != preHave)
                            {
                                if (begin >= 0)
                                {
                                    var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = i, Y2 = edgePos, orientation = theOrientation };
                                    theEdge.rowColValue = i - begin;
                                    theNewRowColCount += theEdge.rowColValue;
                                    tempList.Add(theEdge);
                                    begin = -1;
                                }
                            }
                        }

                        if (begin < 0)
                        {
                            var curValue = (row1Value >> i) & 1;
                            theOrientation = curValue == 1 ? EnumEdgeOrientation.EDO_Bottom : EnumEdgeOrientation.EDO_Top;
                            begin = i;
                        }
                    }
                    else
                    {
                        if (begin >= 0)
                        {
                            var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = i, Y2 = edgePos, orientation = theOrientation };
                            theEdge.rowColValue = i - begin;
                            theNewRowColCount += theEdge.rowColValue;
                            tempList.Add(theEdge);
                        }

                        begin = -1;
                    }
                }

                if (begin >= 0)
                {
                    var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = boardWidth, Y2 = edgePos, orientation = theOrientation };
                    theEdge.rowColValue = boardWidth - begin;
                    theNewRowColCount += theEdge.rowColValue;
                    tempList.Add(theEdge);
                }

                theNewCount = tempList.Count;
            }

            edgeCountAdvance += (theNewCount - theCount);
            rowColVariability += (theNewRowColCount - theRowColCount);
        }
    }

    void RefreshEdgeForCol(int edgeCol)
    {
        if (edgeCol <= 0 || edgeCol >= boardHeight) return;

        {
            var gridCol1 = edgeCol - 1;
            var gridCol2 = edgeCol;
            var col1Value = gridCol1 >= 0 ? (byte)(curGridLayout_col >> (boardHeight * gridCol1)) : (byte)0;
            var tempValue = (byte)(col1Value ^ (curGridLayout_col >> (boardHeight * gridCol2)));
            var edgePos = edgeCol;
            var bOffset = EdgeBucketOffset(edgePos, false);
            if (bOffset >= edgeBucketesAdvance.Length)
            {
                CLog.Exception($"RefreshEdgeForCol 出现异常, bOffset:{bOffset}有误");
                return;
            }

            var tempList = edgeBucketesAdvance[bOffset];
            var theCount = tempList.Count;
            var theRowColCount = 0;
            if (theCount > 0)
            {
                theRowColCount = tempList.Sum(ele => ele.rowColValue);
                tempList.Clear();
            }

            var theNewCount = 0;
            var theNewRowColCount = 0;
            if (tempValue > 0)
            {
                int begin = -1;
                EnumEdgeOrientation theOrientation = EnumEdgeOrientation.EDO_None;
                for (var i = 0; i < boardHeight; i++)
                {
                    var theValue = tempValue & (1 << i);
                    if (theValue != 0)
                    {
                        if (i > 0)
                        {
                            var curValue = (col1Value >> i) & 1;
                            var preHave = (col1Value >> (i - 1)) & 1;
                            if (curValue != preHave)
                            {
                                if (begin >= 0)
                                {
                                    var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = i, orientation = theOrientation };
                                    theEdge.rowColValue = i - begin;
                                    theNewRowColCount += theEdge.rowColValue;
                                    tempList.Add(theEdge);
                                    begin = -1;
                                }
                            }
                        }
                        if (begin < 0)
                        {
                            var curValue = (col1Value >> i) & 1;
                            theOrientation = curValue == 1 ? EnumEdgeOrientation.EDO_Right : EnumEdgeOrientation.EDO_Left;
                            begin = i;
                        }
                    }
                    else
                    {
                        if (begin >= 0)
                        {
                            var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = i, orientation = theOrientation };
                            theEdge.rowColValue = i - begin;
                            theNewRowColCount += theEdge.rowColValue;
                            tempList.Add(theEdge);
                        }

                        begin = -1;
                    }
                }

                if (begin >= 0)
                {
                    var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = boardHeight, orientation = theOrientation };
                    theEdge.rowColValue = boardHeight - begin;
                    theNewRowColCount += theEdge.rowColValue;
                    tempList.Add(theEdge);
                }

                theNewCount = tempList.Count;
            }

            edgeCountAdvance += (theNewCount - theCount);
            rowColVariability += (theNewRowColCount - theRowColCount);
        }
    }

    void RefreshEdge()
    {
#if UNITY_EDITOR
        for (var i = 0; i < boardHeight; i++)
        {
            var theRow = i;
            var thePreRow = -1;
            if (i > 0) thePreRow = i - 1;
            if (thePreRow < 0 || theRow - thePreRow > 1) RefreshEdgeForRow(theRow);
            RefreshEdgeForRow(theRow + 1);
        }

        for (var i = 0; i < boardWidth; i++)
        {
            var theCol = i;
            var thePreCol = -1;
            if (i > 0) thePreCol = i - 1;
            if (thePreCol < 0 || theCol - thePreCol > 1) RefreshEdgeForCol(theCol);
            RefreshEdgeForCol(theCol + 1);
        }

        int theEdge = edgeCountAdvance;
#endif

        foreach (var edge in modifyRowEdges)
        {
            RefreshEdgeForRow(edge);
        }
        modifyRowEdges.Clear();

        foreach (var edge in modifyColEdges)
        {
            RefreshEdgeForCol(edge);
        }
        modifyColEdges.Clear();

#if UNITY_EDITOR
        int theEdge2 = edgeCountAdvance;
        if (theEdge != theEdge2)
        {
            Debug.LogError($"RefreshEdge is Error, {theEdge},{theEdge2}");
        }
#endif    
    }

    private void matrixBlockMatchForFillVacancy(BlockMatchMatrix matchInfo, ref List<Vector2Int> matchPosList)
    {
        var theRowNum = matchInfo.rowNum;
        var theColNum = matchInfo.colNum;
        if (theRowNum <= 0 || theColNum <= 0) return;
        //if (matchInfo.haveEmpty) return;


        {//必须同步边数
            var tempEdge = EdgeCountAdvance;
        }
        var bucketCount = edgeBucketesAdvance.Length;
        var theRowDiff = boardHeight - theRowNum;
        var theColDiff = boardWidth - theColNum;

        var theMask = (ulong)(1 << theColNum) - 1;
        for (int rowIndex = 0; rowIndex < (theRowDiff + 1); rowIndex++)
        {
            var tempValueR = curGridLayout_det;
            for (int colIndex = 0; colIndex < (theColDiff + 1); colIndex++)
            {


                ulong tempMatchValue = 0;
                //for (var i = 0; i < theRowNum; i++)
                //{
                //    var tempRow = rowIndex + i;
                //    var tempValue = tempValueR >> (boardWidth * tempRow);
                //    tempValue = tempValue >> colIndex;
                //    tempValue &= theMask;
                //    tempMatchValue = tempMatchValue | (tempValue << (i * theColNum));
                //}
                //tempMatchValue = ~tempMatchValue;

                //var isMatch = (tempMatchValue & matchInfo.curLayout) == matchInfo.curLayout;
                //if (isMatch)
                //{
                //    matchPosList.Add(new Vector2Int(colIndex, rowIndex));
                //}

                tempMatchValue = ~tempValueR;
                var blockLayout = colIndex < matchInfo.curLayouts.Count ? matchInfo.curLayouts[colIndex] : 0;
                blockLayout = blockLayout << (boardWidth * rowIndex);
                var isMatch1 = (tempMatchValue & blockLayout) == blockLayout;

                if (isMatch1)
                {
                    {
                        var rowEdgeBucketBegin = BlockPlayManager.BLOCK_BOARD_SIZE + 1;
                        var tempContact = true;
                        {
                            {//横向接触检测
                                var offset = colIndex;
                                var theEdgeCount = matchInfo.colEdges.Count;
                                for (int i = 0; i < theEdgeCount; i++)
                                {
                                    var theEdge = matchInfo.colEdges[i];
                                    var theBucketIndex = offset + theEdge.X1;
                                    if (theBucketIndex > 0 && theBucketIndex < boardWidth && theBucketIndex < bucketCount)
                                    {
                                        var theList = edgeBucketesAdvance[theBucketIndex];
                                        if (theList.Count > 0)
                                        {
                                            var edgeY1 = rowIndex + theEdge.Y1;
                                            var edgeY2 = rowIndex + theEdge.Y2;

                                            var theIndex = theList.FindIndex(ele => (edgeY1 == ele.Y1 && ele.Y2 == edgeY2));
                                            if (theIndex < 0)
                                            {
                                                theIndex = theList.FindIndex(ele => ele.orientation == theEdge.orientation && (ele.Y1 == edgeY2 || ele.Y2 == edgeY1)
                                                || theEdge.orientation != ele.orientation && ((ele.Y1 == edgeY1 && edgeY2 <= ele.Y2) || (ele.Y2 == edgeY2 && edgeY1 >= ele.Y1)));
                                            }
                                            tempContact &= theIndex >= 0;
                                        }
                                        else
                                        {
                                            tempContact = false;
                                        }

                                        if (!tempContact) break;
                                    }
                                }

                                if (!tempContact) continue;
                            }

                            {//纵向接触检测
                                var offset = rowIndex + rowEdgeBucketBegin;
                                var theEdgeCount = matchInfo.rowEdges.Count;
                                for (int i = 0; i < theEdgeCount; i++)
                                {
                                    var theEdge = matchInfo.rowEdges[i];
                                    var theBucketIndex = offset + theEdge.Y1;
                                    if (theBucketIndex > rowEdgeBucketBegin && theBucketIndex < bucketCount - 1)
                                    {
                                        var theList = edgeBucketesAdvance[theBucketIndex];
                                        if (theList.Count > 0)
                                        {
                                            var edgeX1 = colIndex + theEdge.X1;
                                            var edgeX2 = colIndex + theEdge.X2;

                                            var theIndex = theList.FindIndex(ele => (edgeX1 == ele.X1 && ele.X2 == edgeX2));
                                            if (theIndex < 0)
                                            {
                                                theIndex = theList.FindIndex(ele => theEdge.orientation == ele.orientation && (ele.X1 == edgeX2 || ele.X2 == edgeX1)
                                                || theEdge.orientation != ele.orientation && ((ele.X1 == edgeX1 && edgeX2 <= ele.X2) || (ele.X2 == edgeX2 && edgeX1 >= ele.X1)));
                                            }
                                            tempContact &= theIndex >= 0;
                                        }
                                        else
                                        {
                                            tempContact = false;
                                        }

                                        if (!tempContact) break;
                                    }
                                }

                                if (!tempContact) continue;
                            }
                        }
                    }

                    matchPosList.Add(new Vector2Int(colIndex, rowIndex));
                }
                //if (isMatch != isMatch1)
                //{
                //    CLog.Error($"matrixBlockMatchEx is Error");
                //}
            }
        }

    }

    private void matrixBlockMatchEx(BlockMatchMatrix matchInfo, ref List<Vector2Int> matchPosList, bool bNeedOne = false, bool bNeedFocus = false)
    {
        var theRowNum = matchInfo.rowNum;
        var theColNum = matchInfo.colNum;
        if (theRowNum <= 0 || theColNum <= 0) return;

        if (bNeedFocus)
        {//必须同步边数
            var tempEdge = EdgeCountAdvance;
        }
        var bucketCount = edgeBucketesAdvance.Length;
        var theRowDiff = boardHeight - theRowNum;
        var theColDiff = boardWidth - theColNum;

        var theMask = (ulong)(1 << theColNum) - 1;
        for (int rowIndex = 0; rowIndex < (theRowDiff + 1); rowIndex++)
        {
            var tempValueR = curGridLayout_det;
            for (int colIndex = 0; colIndex < (theColDiff + 1); colIndex++)
            {


                ulong tempMatchValue = 0;
                //for (var i = 0; i < theRowNum; i++)
                //{
                //    var tempRow = rowIndex + i;
                //    var tempValue = tempValueR >> (boardWidth * tempRow);
                //    tempValue = tempValue >> colIndex;
                //    tempValue &= theMask;
                //    tempMatchValue = tempMatchValue | (tempValue << (i * theColNum));
                //}
                //tempMatchValue = ~tempMatchValue;

                //var isMatch = (tempMatchValue & matchInfo.curLayout) == matchInfo.curLayout;
                //if (isMatch)
                //{
                //    matchPosList.Add(new Vector2Int(colIndex, rowIndex));
                //}

                tempMatchValue = ~tempValueR;
                var blockLayout = colIndex < matchInfo.curLayouts.Count ? matchInfo.curLayouts[colIndex] : 0;
                blockLayout = blockLayout << (boardWidth * rowIndex);
                var isMatch1 = (tempMatchValue & blockLayout) == blockLayout;

                if (isMatch1)
                {
                    if (bNeedFocus)
                    {
                        var tempContact = false;
                        {
                            {//横向接触检测
                                var tempY1 = rowIndex;
                                var tempY2 = tempY1 + theRowNum;

                                var leftIndex = colIndex;
                                if (leftIndex > 0 && leftIndex < bucketCount)
                                {
                                    var theList = edgeBucketesAdvance[leftIndex];
                                    if (theList.Count > 0)
                                    {
                                        var theIndex = theList.FindIndex(ele => ((tempY1 >= ele.Y2 || ele.Y1 >= tempY2) == false));
                                        tempContact = theIndex >= 0;
                                    }
                                }

                                if (tempContact == false)
                                {
                                    var rightIndex = leftIndex + theColNum;
                                    if (rightIndex < boardWidth && rightIndex < bucketCount)
                                    {
                                        var theList = edgeBucketesAdvance[rightIndex];
                                        if (theList.Count > 0)
                                        {
                                            var theIndex = theList.FindIndex(ele => ((tempY1 >= ele.Y2 || ele.Y1 >= tempY2) == false));
                                            tempContact = theIndex >= 0;
                                        }
                                    }

                                    if (false == tempContact && matchInfo.haveEmpty)
                                    {//检测夹层
                                        for (var bucketIndex = leftIndex + 1; bucketIndex < rightIndex; bucketIndex++)
                                        {
                                            var theList = edgeBucketesAdvance[bucketIndex];
                                            if (theList.Count > 0)
                                            {
                                                var theIndex = theList.FindIndex(ele => ((tempY1 >= ele.Y2 || ele.Y1 >= tempY2) == false));
                                                tempContact = theIndex >= 0;
                                                if (tempContact) break;
                                            }
                                        }
                                    }
                                }


                            }

                            if (tempContact == false)
                            {//纵向接触检测
                                var tempX1 = colIndex;
                                var tempX2 = tempX1 + theColNum;

                                var upIndex = rowIndex + BlockPlayManager.BLOCK_BOARD_SIZE + 1;
                                if (upIndex > 0 && upIndex < bucketCount)
                                {
                                    var theList = edgeBucketesAdvance[upIndex];
                                    if (theList.Count > 0)
                                    {
                                        var theIndex = theList.FindIndex(ele => ((tempX1 >= ele.X2 || ele.X1 >= tempX2) == false));
                                        tempContact = theIndex >= 0;
                                    }
                                }

                                if (tempContact == false)
                                {
                                    var downIndex = upIndex + theRowNum;
                                    if (downIndex < bucketCount)
                                    {
                                        var theList = edgeBucketesAdvance[downIndex];
                                        if (theList.Count > 0)
                                        {
                                            var theIndex = theList.FindIndex(ele => ((tempX1 >= ele.X2 || ele.X1 >= tempX2) == false));
                                            tempContact = theIndex >= 0;
                                        }
                                    }

                                    if (false == tempContact && matchInfo.haveEmpty)
                                    {//检测夹层
                                        for (var bucketIndex = upIndex + 1; bucketIndex < downIndex; bucketIndex++)
                                        {
                                            var theList = edgeBucketesAdvance[bucketIndex];
                                            if (theList.Count > 0)
                                            {
                                                var theIndex = theList.FindIndex(ele => ((tempX1 >= ele.X2 || ele.X1 >= tempX2) == false));
                                                tempContact = theIndex >= 0;
                                                if (tempContact) break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (tempContact == false) continue;
                    }

                    matchPosList.Add(new Vector2Int(colIndex, rowIndex));
                    if (bNeedOne) break;
                }
                //if (isMatch != isMatch1)
                //{
                //    CLog.Error($"matrixBlockMatchEx is Error");
                //}
                if (bNeedOne && matchPosList.Count == 1)
                {
                    break;
                }
            }

            if (bNeedOne && matchPosList.Count == 1)
            {
                break;
            }
        }

    }

    private void CollectClearInfo(BlockMatchMatrix blockInfo, Vector2Int pos, List<int> curMatchRow, List<int> curMatchCol)
    {
        if (blockInfo.curLayout == 0) return;

        var theMask = (byte)((1 << boardWidth) - 1);
        for (var y = 0; y < blockInfo.rowNum; y++)
        {
            var theRow = y + pos.y;
            if (theRow >= 0 && !curMatchRow.Contains(theRow))
            {
                bool matchRow = matchRow = (byte)(curGridLayout_det >> (theRow * boardWidth)) == theMask;
                if (matchRow) curMatchRow.Add(theRow);
            }

            for (var x = 0; x < blockInfo.colNum; x++)
            {
                var theCol = x + pos.x;
                if (theCol >= 0 && !curMatchCol.Contains(theCol))
                {
                    bool matchCol = (byte)(curGridLayout_col >> (theCol * boardWidth)) == theMask;
                    if (matchCol) curMatchCol.Add(theCol);
                }
            }
        }
    }

    void HandleMatchRowAndCol(List<int> cols, List<int> rows, bool isAdd, bool syncComplex = true)
    {
        if (rows?.Count > 0)
        {
            foreach (var theRow in rows)
            {
                var theMask = rowColMask[theRow];
                var theCol = boardHeight - theRow - 1;
                theCol = boardWidth - theCol - 1;
                var theMask_col = rowColMask[BLOCK_BOARD_SIZE + theCol];
                if (isAdd)
                {
                    curGridLayout_det |= theMask;
                    curGridLayout_col |= theMask_col;
                }
                else
                {
                    curGridLayout_det &= ~theMask;
                    curGridLayout_col &= ~theMask_col;
                }

                if (syncComplex)
                {
                    var theEdge = theRow;
                    if (!modifyRowEdges.Contains(theEdge))
                    {
                        modifyRowEdges.Add(theRow);
                    }
                    theEdge = theRow + 1;
                    if (!modifyRowEdges.Contains(theEdge))
                    {
                        modifyRowEdges.Add(theEdge);
                    }
                }
            }

            if (syncComplex)
            {
                bNeedSyncEdgeCount = true;
                bNeedSyncConnectedComponentsCount = true;
            }
        }

        if (cols?.Count > 0)
        {
            foreach (var theCol in cols)
            {
                var theMask = rowColMask[BLOCK_BOARD_SIZE + theCol];
                var theRow = theCol;
                var theMask_col = rowColMask[theRow];
                if (isAdd)
                {
                    curGridLayout_det |= theMask;
                    curGridLayout_col |= theMask_col;
                }
                else
                {
                    curGridLayout_det &= ~theMask;
                    curGridLayout_col &= ~theMask_col;
                }

                if (syncComplex)
                {
                    var theEdge = theCol;
                    if (!modifyColEdges.Contains(theEdge))
                    {
                        modifyColEdges.Add(theEdge);
                    }
                    theEdge = theCol + 1;
                    if (!modifyColEdges.Contains(theEdge))
                    {
                        modifyColEdges.Add(theEdge);
                    }
                }
            }

            if (syncComplex)
            {
                bNeedSyncEdgeCount = true;
                bNeedSyncConnectedComponentsCount = true;
            }
        }
    }

    private bool CheckPutDownBlock(BlockMatchMatrix blockInfo, Vector2Int pos)
    {
        if (blockInfo.curLayout == 0) return false;

        var colIndex = pos.x;
        var rowIndex = pos.y;
        var blockLayout = colIndex < blockInfo.curLayouts.Count ? blockInfo.curLayouts[colIndex] : 0;
        blockLayout = blockLayout << (boardWidth * rowIndex);

        var tempMatchValue = ~curGridLayout_det;
        var isMatch = (tempMatchValue & blockLayout) == blockLayout;
        return isMatch;
    }

    public int GetPlaceBlockMatchCount(BlockMatchMatrix blockInfo, Vector2Int pos)
    {
        if (blockInfo.curLayout == 0) { return 0; }
        tempMatchRow.Clear();
        tempMatchCol.Clear();

        PlaceBlock(blockInfo, pos, false);

        CollectClearInfo(blockInfo, pos, tempMatchRow, tempMatchCol);
        var theMatchCount = tempMatchCol.Count + tempMatchRow.Count;

        RevertPlacedBlock(blockInfo, pos, false);

        return theMatchCount;
    }

    public void HandlePlaceBlock(BlockMatchMatrix blockInfo, Vector2Int pos)
    {
        PlaceBlock(blockInfo, pos, true);
    }

    private void PlaceBlock(BlockMatchMatrix blockInfo, Vector2Int pos, bool syncComplex = true)
    {
        if (blockInfo.curLayout == 0) return;
        var colIndex = pos.x;
        var rowIndex = pos.y;


        var blockLayout = colIndex < blockInfo.curLayouts.Count ? blockInfo.curLayouts[colIndex] : 0;
        blockLayout = blockLayout << (boardWidth * rowIndex);
        //var tempMatchValue = ~curGridLayout_det;
        //var isMatch = (tempMatchValue & blockLayout) == blockLayout;
        //if (!isMatch) return;
        curGridLayout_det |= blockLayout;

        //顺时针旋转90度
        var rowIndex1 = colIndex;
        var colIndex1 = boardHeight - rowIndex - 1;
        //左右翻转180度
        colIndex1 = boardWidth - colIndex1 - 1;


        var blockLayout1 = colIndex1 < blockInfo.curLayouts_col.Count ? blockInfo.curLayouts_col[colIndex1] : 0;
        blockLayout1 = blockLayout1 << (boardHeight * rowIndex1);
        curGridLayout_col |= blockLayout1;
        //var tempMatchValue1 = ~curGridLayout_col;
        //var isMatch1 = (tempMatchValue1 & blockLayout1) == blockLayout1;
        boardGridCount += blockInfo.gridCount;

        if (syncComplex == false) return;

        foreach (var edge in blockInfo.checkRowEdges)
        {
            var theRowEdge = pos.y + edge;
            if (!modifyRowEdges.Contains(theRowEdge))
            {
                modifyRowEdges.Add(theRowEdge);
            }
        }

        foreach (var edge in blockInfo.checkColEdges)
        {
            var theColEdge = pos.x + edge;
            if (!modifyColEdges.Contains(theColEdge))
            {
                modifyColEdges.Add(theColEdge);
            }
        }

        bNeedSyncEdgeCount = true;
        bNeedSyncConnectedComponentsCount = true;
    }

    private void RevertPlacedBlock(BlockMatchMatrix blockInfo, Vector2Int pos, bool syncComplex = true)
    {
        if (blockInfo.curLayout == 0) return;
        var colIndex = pos.x;
        var rowIndex = pos.y;
        var blockLayout = colIndex < blockInfo.curLayouts.Count ? blockInfo.curLayouts[colIndex] : 0;
        blockLayout = blockLayout << (boardWidth * rowIndex);
        curGridLayout_det &= (~blockLayout);

        //顺时针旋转90度
        var rowIndex1 = colIndex;
        var colIndex1 = boardHeight - rowIndex - 1;
        //左右翻转180度
        colIndex1 = boardWidth - colIndex1 - 1;
        var blockLayout1 = colIndex1 < blockInfo.curLayouts_col.Count ? blockInfo.curLayouts_col[colIndex1] : 0;
        blockLayout1 = blockLayout1 << (boardHeight * rowIndex1);
        curGridLayout_col &= (~blockLayout1);

        boardGridCount -= blockInfo.gridCount;

        if (syncComplex == false) return;

        foreach (var edge in blockInfo.checkRowEdges)
        {
            var theRowEdge = pos.y + edge;
            if (!modifyRowEdges.Contains(theRowEdge))
            {
                modifyRowEdges.Add(theRowEdge);
            }
        }

        foreach (var edge in blockInfo.checkColEdges)
        {
            var theColEdge = pos.x + edge;
            if (!modifyColEdges.Contains(theColEdge))
            {
                modifyColEdges.Add(theColEdge);
            }
        }

        bNeedSyncEdgeCount = true;
        bNeedSyncConnectedComponentsCount = true;
    }

    public async Task<EnumBlockProductType> GenerateBlockAfterPlaceBlocks(List<BlockPutInfo> putList, EnumBlockProductType pType, int milliSecond)
    {       
        var theRealType = pType;
        List<int> tempRows = null;
        List<int> tempCols = null;
        var theEdge = EdgeCountAdvance;
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        var theCount = putList?.Count ?? 0;
        for (int deep = 0; deep < theCount; deep++)
        {
            var thePutInfo = putList[deep];
            var pos = thePutInfo.targetPos;
            
            var tempDeep = gameSys.GenBlockCount - deep - 1;
            if (tempDeep == 0)
            {
                tempRows = tempMatchRow;
                tempCols = tempMatchCol;
            }
            else if (tempDeep == 1)
            {
                tempRows = tempMatchRow1;
                tempCols = tempMatchCol1;
            }
            else if (tempDeep == 2)
            {
                tempRows = tempMatchRow2;
                tempCols = tempMatchCol2;
            }

            tempRows.Clear();
            tempCols.Clear();

            var theInfo = gameSys.GetBlockMatchMatrix(thePutInfo.blockConfig);
            PlaceBlock(theInfo, pos);
            CollectClearInfo(theInfo, pos, tempRows, tempCols);
            HandleMatchRowAndCol(tempCols, tempRows, false);
        }
        theEdge = EdgeCountAdvance;

        var theTask = gameSys.GenerateBlockExec(pType, milliSecond);
        await theTask;
        theRealType = theTask.Result;
        theEdge = EdgeCountAdvance;

        for (int deep = theCount-1; deep >= 0; deep--)
        {
            var thePutInfo = putList[deep];
            var pos = thePutInfo.targetPos;

            var tempDeep = gameSys.GenBlockCount - deep - 1;
            if (tempDeep == 0)
            {
                tempRows = tempMatchRow;
                tempCols = tempMatchCol;
            }
            else if (tempDeep == 1)
            {
                tempRows = tempMatchRow1;
                tempCols = tempMatchCol1;
            }
            else if (tempDeep == 2)
            {
                tempRows = tempMatchRow2;
                tempCols = tempMatchCol2;
            }

            var theInfo = gameSys.GetBlockMatchMatrix(thePutInfo.blockConfig);
            HandleMatchRowAndCol(tempCols, tempRows, true);
            RevertPlacedBlock(theInfo, pos);
        }
        theEdge = EdgeCountAdvance;

        return theRealType;
    }

}