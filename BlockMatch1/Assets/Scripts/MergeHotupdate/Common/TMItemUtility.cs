using System;
using System.Collections.Generic;
using System.Reflection;
using DragonPlus;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using TMPro;
using UnityEngine;
using UnityEngine.U2D;
using UnityEngine.UI;

namespace TMGame
{
    public static class TMItemUtility
    {

        public static string ItemCountString(this Table_Global_Item item)
        {
            long amount = Mathf.Max(1, item.Amount);
            int itemType = item.ItemType;
            if (item.Infinity)
            {
                return TMItemUtility.FormatPropItemTime(amount * 1000);
            }

            if(itemType == (int)EItemType.Coin || itemType == (int)EItemType.Diamond || itemType == (int)EItemType.Key )
            {
                return item.Amount.ToString();
            }

            return "x"+ item.Amount;
        }

        public static DragonPlus.Config.Global.Table_Global_Item GenerateDummyItem(int itemId, int count)
        {
            var item = GetDummyItem(itemId);
            item.Amount = count;
            return item;
        }
        
        /// <summary>
        /// 下发奖励
        /// </summary>
        public static void DispenseReward(List<Table_Global_Item> items,BIHelper.ItemChangeReasonArgs changeReason)
        {
            if (items == null || items.Count <= 0) return;
            TMGame.GameGlobal.GetMod<UserProfileSys>().SettleRewards(items, changeReason);
        }

        /// <summary>
        /// 手动创建item展示一个假数据，因为是根据ItemType寻找（存在寻找错误的情况），只允许在Triple Match失败中使用，其他人禁止使用
        /// </summary>
        /// <param name="eItemType"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static DragonPlus.Config.Global.Table_Global_Item GenerateDummyItem(EItemType eItemType, int count)
        {
            var item = GetDummyItem(eItemType);
            item.Amount = count;
            return item;
        }
        private static DragonPlus.Config.Global.Table_Global_Item GetDummyItem(EItemType eItemType)
        {
            var items = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Global_Item>();
            var item = items.Find(x => x.GetItemType() == eItemType);
            var newItem = DeepCopyByReflect<DragonPlus.Config.Global.Table_Global_Item>(item);
            return newItem;
        }

        private static DragonPlus.Config.Global.Table_Global_Item GetDummyItem(int itemId)
        {
            var items = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_Global_Item>();
            var item = items.Find(x => x.Id == itemId);
            var newItem = DeepCopyByReflect<DragonPlus.Config.Global.Table_Global_Item>(item);
            return newItem;
        }

        private static T DeepCopyByReflect<T>(T obj)
        {
            //如果是字符串或值类型则直接返回
            if (obj == null || (obj is string) || (obj.GetType().IsValueType)) return obj;

            object retval = Activator.CreateInstance(obj.GetType());
            FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
            foreach (FieldInfo field in fields)
            {
                try
                {
                    field.SetValue(retval, DeepCopyByReflect(field.GetValue(obj)));
                }
                catch
                {
                }
            }
            return (T)retval;
        }

        public static Table_Global_Item GetItem(List<Table_Global_Item> items, EItemType eItemType)
        {
            for (var i = 0; i < items.Count; i++)
            {
                if (items[i].ItemType == (int)eItemType)
                {
                    return items[i];
                }
            }

            return null;
        }

        /// <summary>
        /// 道具类型的时间显示格式统一处理
        /// </summary>
        /// <param name="l"></param>
        /// <returns></returns>
        public static string FormatPropItemTime(long l)
        {
            int hour = 0;
            int minute = 0;
            int second = 0;
            int day = 0;

            l = l < 0 ? 0 : l;
            second = (int)(l / 1000);

            if (second >= 60)
            {
                minute = second / 60;
                second = second % 60;
            }

            if (minute >= 60)
            {
                hour = minute / 60;
                minute = minute % 60;
            }

            if (hour >= 24)
            {
                day = hour / 24;
                hour = hour % 24;
            }

            var d = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_time_d");
            var h = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_time_h");
            var m = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_time_m");
            var s = LocalizationManager.Instance.GetLocalizedString("&key.UI_common_time_s");

            if (day > 0)
            {
                if (hour > 0)
                {
                    return $"{day}{d} {hour}{h}";
                }
                else if (minute >= 30)
                {
                    return $"{day}{d} 1{h}";
                }
                else
                {
                    return $"{day}{d}";
                }
            }
            else
            {
                if (hour >= 1)
                {
                    if (minute > 0)
                    {
                        return $"{hour}{h} {minute}{m}";
                    }
                    else
                    {
                        return $"{hour}{h}";
                    }
                }

                if (second > 0)
                {
                    return $"{minute}{m} {second}{s}";
                }
                else
                {
                    return $"{minute}{m}";
                }
            }
        }
    }
}