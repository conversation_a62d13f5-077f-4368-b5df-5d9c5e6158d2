using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using DG.Tweening;
using DragonPlus.Config.InGame;
using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Newtonsoft.Json;
using DragonPlus.Network;
using DragonPlus.Save;
using MergeHotupdate.Common;
using System.Linq;
using System.Text;
using Prediction;

public partial class ModGame : LogicSys
{
    public static int Block1x1Config = 101;
    public static int Block3x3Config = 901;
    public static Vector2Int InvaildPos = new Vector2Int(-1, -1);
    public static Vector2 InvaildMatchPos = new Vector2(-1000000, -1000000);
    public const int genBlockCount = 3;//每次生成的block数量
    static Dictionary<int, int> bockDic = new Dictionary<int, int>()
    {
        { 1, 1 },
        { 2, 4 },
        { 3, 8 },
        { 4, 19 },
        { 5, 6 },
        { 6, 2 },
        { 9, 1 },
    };//块数对应组合数量

    public static Dictionary<int, int> BockDic => bockDic;

    static float predictChurnValue;//每一次新的无尽模式开局时，如果当前流失预测值大于等于该配置值，则以下三个表（COMPLEXTIYUPALGORITHM、COMPLEXTIYDOWNALGORITHM、OPTIMALCONFIGURATION）启用组ID=2的配置
    static int firstClearScreenBlockInterval; //每局重开游戏，初始触发清屏出块的轮次间隔数
    static int firstClearScreenBestScore;       //首次清屏的出块间隔启用条件为历史最高分数大于等于该值
    
    static int endlessSimplyTriggerInterval = -1; //无尽爽关触发间隔，-1表示首次触发
    const int ENDLESS_SIMPLY_INTERVAL = 3; //触发间隔为3
    
    static List<int> GuaranteeBlockList = new List<int>() { Block1x1Config, 201, 202 };
   
    static List<int> firstRoundFilter = new List<int>() { 101, 202, 403 };//首轮出块禁用列表
    static List<int> randomFilter = new List<int>() { 101 };//随机出块禁用列表

    static List<int> blockConfig = new List<int>();
    static List<int> origainBlockConfig = new List<int>();
    static Dictionary<string, BlockMatchMatrix> blockNameToMatchMatrix = new Dictionary<string, BlockMatchMatrix>();
    static List<RaycastResult> tempRaycastResults = new List<RaycastResult>();
    static Dictionary<RectTransform, List<BlockGrid>> BlockToGrides = new Dictionary<RectTransform, List<BlockGrid>>();
    static List<int> genList = new List<int>(genBlockCount);
    public static List<int> GenList => genList;

    public static List<int> BlockConfig => blockConfig;
    public static List<int> OrigainBlockConfig => origainBlockConfig;
    


    
    #region 属性定义
    public static bool PlayingClearScreen = false;

    RectTransform curBlockPool;
    RectTransform curGridContainer;
    Transform curTipPlayNode;

    Rect boardRect;
    Vector2 gridSize;
    public int boardWidth = BlockPlayManager.BLOCK_BOARD_SIZE, boardHeight = BlockPlayManager.BLOCK_BOARD_SIZE;
    public Grid[] grids;

    private BaseEventData _baseEventData = null;
    private bool isDraging = false;
    GameObject firstEventGo;
    GameObject firstDragGo;
    List<RectTransform> dragAreas;
    Vector3 dragOffset = Vector3.zero;
    int dragBlockNodeIndex = -1;

    Vector3 blockContainerScale = Vector3.one;

    List<RectTransform> blockContainers;
    /// <summary>
    /// 常驻列表,不能清理只能修改元素
    /// </summary>
    List<Vector3> blockContainerPos;
    /// <summary>
    /// 常驻列表,不能清理只能修改元素
    /// </summary>
    List<RectTransform> blocks;
    /// <summary>
    /// 常驻列表,不能清理只能修改元素
    /// </summary>
    List<EnumGridColor> blockColors;
    int putdownCount = 0;   //当前轮落定了几个Block
    int genRoundIndex = 0;  //第几轮(生成block)
    private int lastClearScreenRound = -1; //上次清屏的轮
    private int lastMClearRound = -1; //上次清屏的轮
    private int lastClearHave1X1Round = -1;//上次消除出块-出过1X1的轮索引

    List<int> curBlockConfig = new List<int>();
    public List<int> CurBlockConfig => curBlockConfig;
    int theBestConfig;

    List<int> tempAlphaIndex = new List<int>();
    List<int> tempMatchIndex = new List<int>();
    List<int> tempMatchRow = new List<int>();
    List<int> tempMatchCol = new List<int>();

    int curLevelId = 0;
    Table_InGame_Level curLevelConfig;

    int maxMatchClearCount = 0; //最大消除行列之和数

    int matchClearSum = 0;      //本局消除行列数总和
    int matchClearTime = 0;      //本局消除次数

    int clearScreenTime = 0;    //本局清屏次数
    int maxComboNumber = 0;     //本局最大连击数
    int curComboSum = 0;        //本局累计连击次数

    private bool clearScreenIntervalForNewGame = false; //开局清屏cd是否生效

    #endregion

    #region 游戏玩法初始化
    void InitAllBlockMatrix()
    {
        foreach (var theEle in blockConfig)
        {
            var theName = GetBlockName(theEle);
            CreateOrGetBlock(theName);
        }
    }

    protected async Task GenerateBestBlock(CancellationToken token)
    {
#if DEBUG || DEVELOPMENT_BUILD
        var watch = new System.Diagnostics.Stopwatch();
        watch.Start();
#endif
        var theMinEdgeComplex = 99999;
        theBestConfig = -1;
        Vector2Int matchBlockPos = default;

        await Task.Run(() =>
        {
            foreach (var theEle in curBlockConfig)
            {
                if (token.IsCancellationRequested)
                {
                    CLog.Exception("GenerateBestBlock 操作超时");
                    break;
                }

                var theIndex = genList.FindIndex(ele => ele == theEle);
                if (theIndex >= 0) continue;//已经生成了

                var theName = GetBlockName(theEle);
                if (!blockNameToMatchMatrix.ContainsKey(theName))
                {
                    CLog.Exception($"GenerateBestBlock 出错, {theName} 对应的 MatchMatrix 不存在");
                    continue;
                }

                var bmMatrix = blockNameToMatchMatrix[theName];
                var theKv1 = Vector2Int.zero;
                var tempCount = 0;
                var theBestEdge1 = 0;
                bool bRes = BlockPlayManager.Instance.MatrixBlockMatchBest(bmMatrix, ref theKv1, ref theBestEdge1, out tempCount);
                if (!bRes) continue;
                var curEdgeComplex = theBestEdge1 - tempCount;
                if (curEdgeComplex <= theMinEdgeComplex)
                {
                    theMinEdgeComplex = curEdgeComplex;
                    theBestConfig = theEle;
                    matchBlockPos = theKv1;
                }
            }
        });

#if DEBUG || DEVELOPMENT_BUILD
        watch.Stop();
        CLog.Error($"GenerateBestBlock---花费时间:{watch.ElapsedMilliseconds}毫秒, blockName:{theBestConfig.ToString()}, matchBlockPos:{matchBlockPos.ToString()}");
#endif
    }

    void ClearBlocks()
    {
        for (int i = 0; i < blocks.Count; i++)
        {
            blockColors[i] = EnumGridColor.EGC_None;
            var block = blocks[i];
            if (block == null) continue;
            block.gameObject.SetActive(false);
            block.SetParent(curBlockPool);
            blocks[i] = null;
        }
    }

    
    private Vector2Int ParsePos(ulong posId)
    {
        int id = (int)posId;
        return new Vector2Int(id % boardWidth, id / boardWidth);
    }

    public async Task<EnumBlockProductType> GenerateBlockExec(EnumBlockProductType pType, int milliSecond)
    {
        var theRealType = pType;
        var blockProductUseNative = curBlockProductIsNative;//clientGroup == EABTestGroup.Group1;
       
        //if (blockProductUseNative)
        //{
            var theTask = GenerateBlockConfigsInnerNative(pType);
            await theTask;
            theRealType = theTask.Result;
        //}
        /*
        else
        
            await GenerateBlockConfigsInnerOrigin(pType, milliSecond);
        }
        */
        return theRealType;
    }

    private bool needSetDragAreaSize = false;
    public void RestDragAreas()
    {
        needSetDragAreaSize = false;
        for (int i = 0; i < dragAreas.Count; i++)
        {
            dragAreas[i].sizeDelta = new Vector2(231, 260f);
        }
    }

    public void ClearDragAreaSize()
    {
        if (!needSetDragAreaSize)
        {
            return;
        }
        dragAreas[1].sizeDelta = Vector2.zero;
        dragAreas[2].sizeDelta =  dragAreas[0].sizeDelta = new Vector2(460f, 260f);
    }

    async Task GenerateBlockConfigs(GenerateParam gp)
    {
        if (isGenerating) return;
        isGenerating = true;
        curHaveBlockCount = 0;
        theListForClearScreen.Clear();
        theMClearBlockId = 0;       
        putdownCount = 0;
        RestDragAreas();
        ClearBlocks();

        var pType = gp.pType;
        var theRealType = pType;
#if DEBUG || DEVELOPMENT_BUILD
        if (pType == EnumBlockProductType.EBPT_PreBack)
        {
            genList.Clear();
            genList.AddRange(pregGenList);
            isGenerating = false;
            return;
        }
#endif
        curProductTypeOri = pType;
        try
        {
            var blockProductUseNative = curBlockProductIsNative;//clientGroup == EABTestGroup.Group1;

            if (pType == EnumBlockProductType.EBPT_LoadStorage)
            {
                var theStorageList = GetBlockGenListFromStorage();
                var theStorageSize = theStorageList?.Count ?? 0;
                if (theStorageSize > 0 && theStorageSize == genBlockCount)
                {
                    genList.Clear();
                    for ( var i = 0; i < genBlockCount; i++)
                    {
                        genList.Add(theStorageList[i].blockConfig);
                    }
                    return;
                }
                else
                {
                    gp.pType = EnumBlockProductType.EBPT_Normal;
                }
            }
            else if(pType == EnumBlockProductType.EBPT_NoNeed)
            {
                if (genList.Count == genBlockCount)
                {
                    return;
                }
                else
                {
                    gp.pType = EnumBlockProductType.EBPT_Normal;
                }
            }

            genList.Clear();
            int milliSecond = gp.execMilliSecond;
            //CLog.Info($"GenerateBlockConfigs, curScore:{curScore}, EnumBlockProductType:{pType}");

            bool bClearSreenEnable = true;
            if(clearScreenIntervalForNewGame)
            {        
                if (genRoundIndex < firstClearScreenBlockInterval) bClearSreenEnable = false;
                CLog.Info($"<color=red>开局清屏CD生效, 当前出块轮数:{genRoundIndex + 1},清屏总开关:{bClearSreenEnable}</color>");
            }

            if (bClearSreenEnable)
            {
                var isTriggerClearScreenBlock = TriggerClearScreenBlock;
                var isSmallScore = CurScore <= 5000;
                if (isTriggerClearScreenBlock || isSmallScore)
                {
                    var theValue = InGameConfigManager.GetGlobalConfig<int>("ClearScreenTriggerEdge");
                    var theEdgeCount = BlockPlayManager.Instance.EdgeCountAdvance;
                    if (theEdgeCount > 0 && theEdgeCount <= theValue && pType != EnumBlockProductType.EBPT_ClearScreen)
                    {
                        var tempCount = 0;
                        var targetType = EnumBlockProductType.EBPT_ClearScreen;
                        //if (blockProductUseNative)
                        //{
                            var theTask = GenerateBlockConfigsInnerNative(targetType, isAuto: true, listForClearScreen: theListForClearScreen);
                            await theTask;
                            var tempType = theTask.Result;
                            tempCount = theListForClearScreen.Count;
                            if (tempType == targetType && tempCount > 0)
                            {
                                theRealType = targetType;
                            }
                        //}
                        /*
                        else
                        {
                            if (isSmallScore && isTriggerClearScreenBlock == false)
                            {
                                await GenerateBlockConfigsInnerOrigin(targetType, milliSecond,
                                    listForClearScreen: theListForClearScreen, curMaxDeepForClearScreen: 0);
                            }
                            else
                            {
                                await GenerateBlockConfigsInnerOrigin(targetType, milliSecond, listForClearScreen: theListForClearScreen);
                            }

                            tempCount = theListForClearScreen.Count;
                            if (tempCount > 0)
                            {
                                theRealType = targetType;
                            }
                        }*/

                        CLog.Info($"GenerateBlockConfigs, TriggerClearScreenBlock:{isTriggerClearScreenBlock}, {tempCount}");
                        if (isTriggerClearScreenBlock == false)
                        {
                            bool bAbandon = true;
                            if (tempCount == 1)
                            {
                                var thePutInfo = theListForClearScreen[0];
                                var theBlockInfo = GetBlockMatchMatrix(thePutInfo.blockConfig);
                                var theMatchCount = BlockPlayManager.Instance.GetPlaceBlockMatchCount(
                                    theBlockInfo, thePutInfo.targetPos);
                                if (theMatchCount > 3) bAbandon = false;
                            }
                            if (bAbandon)
                            {
                                theListForClearScreen.Clear();
                                theRealType = pType;
                            }
                        }
                    }
                }
            }

            var theClearScreenCount = theListForClearScreen.Count;
            bool isClearScreenAll = theRealType == EnumBlockProductType.EBPT_ClearScreen && theClearScreenCount == genBlockCount;

            if (isClearScreenAll == false)
            {
                if (theClearScreenCount > 0)
                {
                    curHaveBlockCount = theClearScreenCount;
                    var theTask = BlockPlayManager.Instance.GenerateBlockAfterPlaceBlocks(theListForClearScreen, pType, milliSecond);
                    await theTask;
                }
                else
                {
                    var theTask = GenerateBlockExec(pType, milliSecond);
                    await theTask;
                    theRealType = theTask.Result;
                }

                //CLog.Info($"GenerateBlockConfigs Done");

                bool bFail = genList.Count + theClearScreenCount < genBlockCount;
                if (bFail == false)
                {
                    if (theClearScreenCount > 0)
                    {
                        for (int i = theClearScreenCount - 1; i >= 0; i--)
                        {
                            genList.Insert(0, theListForClearScreen[i].blockConfig);
                        }                    
                    }

                    if (theRealType == EnumBlockProductType.EBPT_Hard)
                    {
                        OnHardProduct();
                    }
                }
                else
                {
                    var theSecondType = gp.pType1;
                    theRealType = theSecondType;
                    if (pType != theSecondType)
                    {
                        if (theClearScreenCount > 0)
                        {
                            curHaveBlockCount = theClearScreenCount;
                            var theTask = BlockPlayManager.Instance.GenerateBlockAfterPlaceBlocks(theListForClearScreen, theSecondType, milliSecond);
                            await theTask;
                            theRealType = theTask.Result;
                        }
                        else
                        {
                            var theTask = GenerateBlockExec(theSecondType, milliSecond);
                            await theTask;
                            theRealType = theTask.Result;
                        }
                    }

                    bFail = genList.Count + theClearScreenCount < genBlockCount;
                    if (theClearScreenCount <= 0) theRealType = bFail ? EnumBlockProductType.EBPT_Guarantee : theRealType;
                    if (bFail)
                    {
                        genList.Clear();
                        if (theClearScreenCount > 0)
                        {
                            foreach (var theInfo in theListForClearScreen)
                            {
                                genList.Add(theInfo.blockConfig);
                            }
                            
                            for (int i = theClearScreenCount; i < genBlockCount; i++)
                            {
                                var theId = GuaranteeBlockList.GetRandomValue();
                                genList.Add(theId);
                            }
                        }
                        else
                        {
                            genList.AddRange(GuaranteeBlockList);
                        }
                    }
                    else
                    {
                        if (theClearScreenCount > 0)
                        {
                            for (int i = theClearScreenCount - 1; i >= 0; i--)
                            {
                                genList.Insert(0, theListForClearScreen[i].blockConfig);
                            }
                        }
                    }
                }
            }
            else
            {
                genList.Clear();
                foreach (var theInfo in theListForClearScreen)
                {
                    genList.Add(theInfo.blockConfig);
                }
            }

            if (genList.Count != genBlockCount)
            {
                CLog.Exception($"GenerateBlockConfigs--出块数目有误:{genList.Count}");
            }

            var curGenRound = genRoundIndex + 1;
            if (theRealType == EnumBlockProductType.EBPT_ComplexityDown)
            {
                if (genList.Contains(Block1x1Config))
                {
                    lastClearHave1X1Round = curGenRound;
                }
            }

            curProductType = theRealType;
            if (theRealType == EnumBlockProductType.EBPT_Hard) curHardProductCount++;

        }
        catch (Exception ex)
        {
            CLog.Exception($"GenerateBlockConfigs--ex:{ex}");
            var bFail = genList.Count < 3;
            if (bFail)
            {
                genList.Clear();
                genList.AddRange(GuaranteeBlockList);
            }

            theRealType = curProductType = EnumBlockProductType.EBPT_Guarantee;
        }
        finally
        {
            if (theMClearBlockId > 0) theRealType = EnumBlockProductType.EBPT_MultiClear;
            EventBus.Dispatch(new EventProductBlockInfo(pType, theRealType));
            CLog.Info($"GenerateBlockConfigs---{pType}->{theRealType}");

            CheckBlockGrid();
            isGenerating = false;
        }
        isGenerating = false;
    }

    public void CheckBlockGrid()
    {
        if (grids == null) return;

        bool bRes = true;
        if(curGridLayout != BlockPlayManager.Instance.GetBoardInfo())
            bRes = false;
        
        if(!bRes)
        {
            curGridLayout = 0;
            BlockPlayManager.Instance.ClearBoardLayout();
            foreach (var grid in grids)
            {
                if (grid == null) continue;
                if (grid.ShowState == EnumGridState.EGS_Show)
                {
                    if (BlockPlayManager.Instance.HaveBlock(grid.yIndex, grid.xIndex) == false)
                    {
                        HandleBoardLayout(grid.yIndex * boardWidth + grid.xIndex, true);
                        BlockPlayManager.Instance.AddBlock(grid.yIndex, grid.xIndex);
                    }
                }
                else if (grid.ShowState == EnumGridState.EGS_Hide)
                {
                    if (BlockPlayManager.Instance.HaveBlock(grid.yIndex, grid.xIndex))
                    {
                        HandleBoardLayout(grid.yIndex * boardWidth + grid.xIndex, false);
                        BlockPlayManager.Instance.RemoveBlock(grid.yIndex, grid.xIndex);
                    }
                }
            }

            CLog.Error($"CheckBlockGrid--bRes:{bRes}");
        }
    }

    private List<int> lastBornBlockConfigs = new List<int>();
    private GenerateParam nextFixGp = GenerateParam.invaildP; 
    /// <summary>
    /// 创建Block
    /// </summary>
    public async void GenerateBlockGo(GenerateParam gp)
    {
        GenerateParam theParam = gp;
        if ((nextFixGp.pType == EnumBlockProductType.EBPT_None && nextFixGp.execMilliSecond == 0) == false)
        {
            theParam = nextFixGp;
        }
        await GenerateBlockConfigs(theParam);

        var theCount1 = blockContainers?.Count ?? 0;
        var theCount2 = blocks?.Count ?? 0;
        var theCount3 = blockColors?.Count ?? 0;
        if (theCount1 <= 0 || theCount2 <= 0 || theCount3 <= 0)
        {
#if DEVELOPMENT_BUILD
            CLog.Exception($"创建Block出错, 数据异常 [{theCount1},{theCount2},{theCount3}] ");
#endif
            return;
        }

        bool bFromStorage = gp.pType == EnumBlockProductType.EBPT_LoadStorage;
        List<GenBlockInfo> theStorageList = null;
        var theStorageSize = 0;
        if (bFromStorage)
        {
            theStorageList = GetBlockGenListFromStorage();
            theStorageSize = theStorageList?.Count ?? 0;
        }
        else if(gp.pType != EnumBlockProductType.EBPT_PreBack)
        {
            if (curBlockIsNewOrder && genList.Count == genBlockCount)
            {
                var theThrid = genList[2];
                genList.RemoveAt(2);
                genList.Insert(0, theThrid);
            }
        }

        var theNoShowList = new List<int>();
        bool bAllSame = true;
        for (int i = 0; i < genList.Count; i++)
        {
            var theEle = genList[i];
            if (theEle == -1)
            {//关卡存储时候有可能为-1
                theNoShowList.Add(i);
                putdownCount++;
                bAllSame = false;
                continue;
            }

            if (i >= theCount1 || i >= theCount2 || i >= theCount3)
            {
#if DEVELOPMENT_BUILD
                CLog.Exception($"创建Block[{i}]出错, 数据异常 [{theCount1},{theCount2},{theCount3}] ");
#endif
                break;
            }

            var theLastId = i < lastBornBlockConfigs.Count ? lastBornBlockConfigs[i] : 0;
            bAllSame &= (theLastId == theEle);

            var theColor = UnityEngine.Random.Range((int)EnumGridColor.EGC_Red, (int)EnumGridColor.EGC_End + 1);
            var theStorageBlockInfo = GenBlockInfo.defaultInfo;
            if (theStorageSize > 0 && i < theStorageSize)
            {
                theStorageBlockInfo = theStorageList[i];
                theColor = theStorageBlockInfo.blockColor;
            }

            var theParent = blockContainers[i];

            var theAssetName = GetBlockName(theEle);
            RectTransform theBlock = CreateOrGetBlock(theAssetName, theParent, (EnumGridColor)theColor, theStorageBlockInfo);
            blocks[i] = theBlock;
            blockColors[i] = (EnumGridColor)theColor;

            if (theBlock == null)          
            {
                CLog.Exception($"创建Block[{theAssetName}]出错, 预设资源[{theAssetName}] 生成失败 ");
                continue;
            }
        }

        if (bAllSame)
        {
            // nextFixGp = GenerateParam.defaultP;
            // CLog.Warning($"GenerateBlockGo--本轮{genRoundIndex}block与上轮一样，下轮产出block强制走random");
        }
        else
        {
            nextFixGp = GenerateParam.invaildP;
        }
        
        lastBornBlockConfigs.Clear();
        lastBornBlockConfigs.AddRange(genList);

     
        
        // 播动画
        {
            if (CurGameType == EnumBlockGameType.BlockGame_Stage)
            {
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                theView.PlayBlockBornEffect(theNoShowList);
                theView.TryTriggerClearGuide(CurProductType,theListForClearScreen,blocks);
            }
            else if (CurGameType == EnumBlockGameType.BlockGame_Endless)
            {
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
                theView.PlayBlockBornEffect(theNoShowList);
                theView.TryTriggerClearGuide(CurProductType,theListForClearScreen,blocks);
            }
        }


        CurLayoutStore();

        genRoundIndex++;
    }


    void ClearGameCatchInfo()
    {
        BlockPlayManager.Instance.HandleReset();

        if (_nexPlayCoroutine != null)
        {
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_nexPlayCoroutine);
            _nexPlayCoroutine = null;
        }

        if (tipPlayDic.Count > 0)
        {
            var tempList = tipPlayDic.ToList();
            foreach (var item in tempList)
            {
                item.Value.Complete();
            }
            tipPlayDic.Clear();
        }
        blockTipInfos.Clear();

        curBlockPool = null;
        curGridContainer = null;
        curTipPlayNode = null;
        _baseEventData = null;
        firstEventGo = null;
        firstDragGo = null;
        genList?.Clear();
        
        ClearBlocks();
        isDraging = false;
        clearScreenIntervalForNewGame = false;
        putdownCount = 0;
        genRoundIndex = 0;  //第几轮(生成block)
        tempAlphaIndex.Clear();
        tempMatchIndex.Clear();
        tempMatchRow.Clear();
        tempMatchCol.Clear();

        lastBornBlockConfigs.Clear();
        nextFixGp = GenerateParam.invaildP;
        lastClearScreenRound = -1;
        lastMClearRound = -1;
        lastClearHave1X1Round = -1;
        levelLayoutGridList.Clear();
        ClearTempMatchPool(-1);

        curMoveHomeSequence?.Kill();
        curMoveHomeSequence = null;
        curPdSequence?.Kill();
        curPdSequence = null;
        dragOffset = Vector3.zero;
        blockContainerScale = Vector3.one;
        ChoosedDragBlockContainer = null;

        bombMatchList.Clear();
        curBlockBestMatchPos.Clear();
        curHaveClearMatchPos.Clear();
        lastDragIndex = -1;
        lastDragMatchPos = InvaildMatchPos;
        lastDragMatchPosLU = InvaildPos;
        
        comboNum = 0;
        consecutiveNonMatchCount = 0;
        isRewarded = false;
        curScore = 0;
        curLife = 0;
        maxMatchClearCount = 0;
        matchClearSum = 0;
        matchClearTime = 0;
        CurGameType = EnumBlockGameType.BlockGame_None;
        clearScreenTime = 0;    //本局清屏次数
        maxComboNumber = 0;     //本局最大连击数
        curComboSum = 0;        //本局累计连击次数
#if DEBUG || DEVELOPMENT_BUILD
        preLayout = 0;
#endif
    }

    public void HandleGameDispose(EnumGameResultType type = EnumGameResultType.EFST_NoResult)
    {
        HandleGameReward(type);

        ClearGameCatchInfo();

        dragAreas?.Clear();
        blockContainers?.Clear();
        blockContainerPos?.Clear();
        BlockToGrides?.Clear();
        grids = null;
    }

    private long startPlayTime;
    public void OnOpenGame(OpenGameInfo gameInfo)
    {
#if DEBUG || DEVELOPMENT_BUILD
        var watch = new System.Diagnostics.Stopwatch();
        watch.Start();
#endif
        ClearGameCatchInfo();
        EABTestGroup clientGroup =  GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestNativeAlgorithm);
        
        // 关卡模式下强制使用Native算法
        if (gameInfo.enumBlockGameType == EnumBlockGameType.BlockGame_Stage)
        {
            curBlockProductIsNative = true;
            CLog.Info($"OnOpenGame 关卡模式强制使用Native算法，curBlockProductIsNative:{curBlockProductIsNative}");
        }
        else
        {
            curBlockProductIsNative = clientGroup == EABTestGroup.Group2;
            // 无尽模式难题ABTest
            var endlessDifficultTiming = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.abTestEndlessDifficultTiming);
            abTestEndlessDifficultTiming = endlessDifficultTiming == EABTestGroup.Group2;
            // 无尽模式爽关ABTest
            curABtestEndlessSimply = false;
            var abTestEndlessSimplify = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.abTestEndlessSimplify);
            if (abTestEndlessSimplify == EABTestGroup.Group2)
            {
                //20%概率触发爽关模式
                if(UnityEngine.Random.value < 0.2f)
                {
                    // 首次触发（-1）或间隔结束（0）时才触发
                    if (endlessSimplyTriggerInterval <= 0)
                    {
                        curABtestEndlessSimply = true;
                        endlessSimplyTriggerInterval = ENDLESS_SIMPLY_INTERVAL;
                        CLog.Info("触发爽关模式");
                    }
                    else
                    {
                        CLog.Info($"爽关模式间隔计数：{endlessSimplyTriggerInterval}/{ENDLESS_SIMPLY_INTERVAL}");
                    }
                }
                // 每次调用都递减间隔计数器
                if (endlessSimplyTriggerInterval > 0)
                {
                    endlessSimplyTriggerInterval--;
                }
            }
        }
        clientGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.AbTestRandomWeight);
        curABTestInGame = clientGroup;
        CLog.Info($"OnOpenGame eABTestInGame:{curABTestInGame}");
        clientGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestBlockSorting);
        curBlockIsNewOrder = clientGroup == EABTestGroup.Group2;
        CLog.Info($"OnOpenGame curBlockIsNewOrder:{curBlockIsNewOrder}");

        {
            predictChurnGroup = 1;
            var map = SDK<IStorage>.Instance.Get<StorageGlobal>().PredictionInfo;
            if (null != map && map.TryGetValue(PredictionManager.PredictionTypes.churn.ToString(), out var predictionData))
            {
                if (predictionData.Prediction >= predictChurnValue) predictChurnGroup = 2;
                CLog.Info($"OnOpenGame predictChurnGroup:{predictChurnGroup}, Prediction:{predictionData.Prediction}");
            }
        }
        


        CurGameType = gameInfo.enumBlockGameType;
        curBlockPool = gameInfo.blockPool;
        curGridContainer = gameInfo.gridContainer;
        curTipPlayNode = gameInfo.tipPlay;
        CurGameTargetType = gameInfo.enumTargetType;
        curLevelId = gameInfo.levelId;
        startPlayTime = SDK<INetwork>.Instance.GetServerTime();
        if(blockContainers?.Count > 0 && blockContainers[0] != null)
        {
            blockContainerScale = blockContainers[0].localScale;
        }

        boardRect = curGridContainer.rect;
        gridSize.x = boardRect.width / boardWidth;
        gridSize.y = boardRect.height / boardHeight;
        var theGridNum = grids?.Length ?? 0;
        if (theGridNum <= 0)
        {
            InitGrids(boardWidth, boardHeight);
        }
        else
        {
            foreach (var curGrid in grids)
            {
                curGrid.ShowState = EnumGridState.EGS_Hide;
                
                // 立即强制清理所有覆盖物GameObject，避免延迟销毁导致的状态残留
                var overlayBlock = curGrid.RectTf?.transform.Find("overlayBlock");
                if (overlayBlock != null)
                {
                    GameObject.DestroyImmediate(overlayBlock.gameObject);
                }
                
                // 立即强制清理多层方块GameObject
                var multiBlock = curGrid.RectTf?.transform.Find("multiBlock");
                if (multiBlock != null)
                {
                    GameObject.DestroyImmediate(multiBlock.gameObject);
                }
                
                // 立即强制清理高级方块GameObject
                var advancedBlock = curGrid.RectTf?.transform.Find("advancedBlock");
                if (advancedBlock != null)
                {
                    GameObject.DestroyImmediate(advancedBlock.gameObject);
                }
                
                // 立即强制清理固定方块GameObject
                var fixedBlock = curGrid.RectTf?.transform.Find("fixedBlock");
                if (fixedBlock != null)
                {
                    GameObject.DestroyImmediate(fixedBlock.gameObject);
                }
                
                // 重置所有特殊方块属性
                curGrid.advancedBlockRemainingLayers = 0;
                curGrid.AdvancedBlockId = 0; // 这会触发Refresh2x2Block
                curGrid.advancedBlockRoot = null;
                curGrid.IsMultiBlock = false;
                curGrid.MultiBlockClearTime = 0;
                curGrid.OverlayLayer = 0;
                curGrid.OverlayBlockId = 0; // 这会触发RefresOverlayBlockState
                curGrid.LeafOverlayLayers = 0;
                curGrid.IceOverlayLayers = 0;
                curGrid.IsFixedBlock = false;
            }
        }

        OnInitActionRecord();
        InitAllBlockMatrix();

        bool tempClearScreenCheck1 = StorageExtension.GameEndlssStorage.CurFirstScore >= firstClearScreenBestScore;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            if (tempClearScreenCheck1)
            {
                clearScreenIntervalForNewGame = true;
            }

            var theEnergySys = GameGlobal.GetMod<EnergySys>();
            InInfintyEnergyState = theEnergySys.IsInfiniteEnergy();
            if (!InInfintyEnergyState)
            {
                var theItemChangeBiArg = new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.PlayLevel);
                theItemChangeBiArg.data1 = curLevelId.ToString();
                // 扣体力
                GameGlobal.GetMod<EnergySys>().CostEnergy(1, theItemChangeBiArg);
            }
            curScore = StorageExtension.GameBlockLevelStorage.AchiveScore;                      
            HandleLevelLayout();
        }
        else if (CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            if (tempClearScreenCheck1 && StorageExtension.GameEndlssStorage.GameResult != (int)EnumGameResultType.EFST_NoResult)
            {
                clearScreenIntervalForNewGame = true;
            }

            HandleEndlessLayout();
        }

        InitFeelInfo();

#if DEBUG || DEVELOPMENT_BUILD
        watch.Stop();
        CLog.Info($"ModGame.OnOpenGame,花费时间{watch.ElapsedMilliseconds}毫秒");
#endif
    }


    private List<ExperienceFeelInfo> experienceFeelList = new List<ExperienceFeelInfo>();
    void InitFeelInfo()
    {
        var theScoreGroup = 1;
        var theBestScore = StorageExtension.GameEndlssStorage.CurFirstScore;
        var tempConfig = InGameConfigManager.GetBestScoreGroupConfig(theBestScore);
        if (tempConfig != null) theScoreGroup = tempConfig.Id;
        var theList = InGameConfigManager.GetComplextiyVariableList(theScoreGroup);
        if (theList?.Count <= 0)
        {
            CLog.Error($"InitFeelInfo--执行出错, theScoreGroup:{theScoreGroup}对应的配置不存在");
            return;
        }
        experienceFeelList.Clear();
        if(CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            if (StorageExtension.GameEndlssStorage.TodayPlayCount > 0)
            {
                var theBegin = 0;
                for (int i = 0; i < theList.Count; i++)
                {
                    var theConfig = theList[i];
                    var theCount = theConfig.ScoreRandomRange?.Count;
                    var theEnd = int.MaxValue;
                    if (theCount == 2)
                    {
                        theEnd = theBegin + theConfig.ScoreBaseRange + UnityEngine.Random.Range(theConfig.ScoreRandomRange[0],
                            theConfig.ScoreRandomRange[1]);
                        
                    }
                    var theKv = new KeyValuePair<int, int>(theBegin, theEnd);
                    experienceFeelList.Add(new ExperienceFeelInfo() { 
                        ScoreRange = theKv, feelType = theConfig.ExperienceType}
                    );
                    theBegin = theEnd;
                    CLog.Warning($"局间分数段信息-theEnd:{theEnd}, configId:{theConfig.Id}");
                }
            }
            else
            {
                for (int i = 0; i < theList.Count; i++)
                {
                    var theConfig = theList[i];
                    if (theConfig.ScoreFirstRange.Count == 2)
                    {
                        var theKv = new KeyValuePair<int, int>(theConfig.ScoreFirstRange[0],
                            theConfig.ScoreFirstRange[1]);
                        experienceFeelList.Add(new ExperienceFeelInfo() {
                            ScoreRange = theKv,
                            feelType = theConfig.ExperienceType}
                        );
                        CLog.Warning($"局间分数段信息-theEnd:{theKv.Value}, configId:{theConfig.Id}");
                    }
                }
            }
        }

    }

    void InitGrids(int width, int height)
    {
        grids = new Grid[width * height];
        for (int r = 0; r < height; r++)
        {
            for (int c = 0; c < width; c++)
            {
                int index = r * width + c;
                grids[index] = new Grid();
                var theName = $"Image ({index + 1})";
                var theNode = curGridContainer.Find(theName);
                if (theNode == null)
                {
                    CLog.Error($"UINode_Content下节点{theName}不存在");
                    continue;
                }
                var theImage = theNode.GetComponent<Image>();
                grids[index].RectTf = theNode.GetComponent<RectTransform>();
                grids[index].Image = theImage;
                grids[index].xIndex = index % boardWidth;
                grids[index].yIndex = index / boardWidth;
                grids[index].ShowState = EnumGridState.EGS_Hide;
            }
        }
    }

    #endregion

    #region 关卡模式

    public void HandleQuiteGame(EnumGameResultType pType)
    {
        if (pType != EnumGameResultType.EFST_NoResult)
        {
            return;
        }

        if(CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            var theStorage = StorageExtension.GameBlockLevelStorage;
            theStorage.HandleReset();
            if (false)
            {//新需求--暂且不保存进度
                var theLevelInfo = theStorage.CurLevelInfo;
                theLevelInfo.levelId = curLevelId;
                theLevelInfo.gameResult = (int)EnumGameResultType.EFST_NoResult;
                theStorage.CurLevelInfo = theLevelInfo;
                theStorage.AchiveScore = curScore;
                theStorage.BlockGridInfos.Clear();
                foreach (var grid in grids)
                {
                    if (grid.ShowState == EnumGridState.EGS_Show)
                    {
                        BlockGridInfo theInfo = new BlockGridInfo();
                        theInfo.y = grid.yIndex;
                        theInfo.x = grid.xIndex;
                        theInfo.gridGem = grid.GemType;
                        theInfo.gridColor = grid.ColorState;
                        theStorage.BlockGridInfos.Add(theInfo);
                    }
                }

                theStorage.GenBlockInfos.Clear();
                for (int i = 0; i < genList.Count; i++)
                {
                    if (blocks[i] == null)
                    {
                        theStorage.GenBlockInfos.Add(GenBlockInfo.defaultInfo);
                    }
                    else
                    {
                        var theBlock = blocks[i];
                        EnumGridColor theColor = blockColors[i];
                        var theInfo = new GenBlockInfo(genList[i]);
                        if (BlockToGrides.ContainsKey(theBlock))
                        {
                            theInfo.gemList = new List<int>();
                            theInfo.gemPosList = new List<int>();
                            var blockGrides = BlockToGrides[theBlock];
                            for (int j = 0; j < blockGrides.Count; j++)
                            {
                                var theGrid = blockGrides[j];
                                if (theGrid.gemTs)
                                {
                                    theInfo.gemList.Add((int)theGrid.GemType);
                                    theInfo.gemPosList.Add(j);
                                }
                            }
                        }
                        theInfo.blockColor = (int)theColor;
                        theStorage.GenBlockInfos.Add(theInfo);
                    }
                }


                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                if (CurGameType == EnumBlockGameType.BlockGame_Stage
                    && HasTargetType(EnumTargetType.ETT_Gem) && theView != null)
                {
                    var theDic = theStorage.CollectGemDic;
                    theView.HandleStorageGemInfo(ref theDic);
                }
            }
        }
        else if (CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            var theStorage = StorageExtension.GameEndlssStorage;
            theStorage.HandleReset();
            theStorage.AchiveScore = curScore;
            theStorage.BlockGridInfos.Clear();
            foreach (var grid in grids)
            {
                if (grid.ShowState == EnumGridState.EGS_Show)
                {
                    BlockGridInfo theInfo = new BlockGridInfo();
                    theInfo.y = grid.yIndex;
                    theInfo.x = grid.xIndex;
                    theInfo.gridGem = grid.GemType;
                    theInfo.gridColor = grid.ColorState;
                    theStorage.BlockGridInfos.Add(theInfo);
                }
            }

            theStorage.GenBlockInfos.Clear();
            for (int i = 0; i < genList.Count; i++)
            {
                if (blocks[i] == null)
                {
                    theStorage.GenBlockInfos.Add(GenBlockInfo.defaultInfo);
                }
                else
                {
                    var theBlock = blocks[i];
                    EnumGridColor theColor = blockColors[i];
                    var theInfo = new GenBlockInfo(genList[i]);
                    if (BlockToGrides.ContainsKey(theBlock))
                    {
                        theInfo.gemList = new List<int>();
                        theInfo.gemPosList = new List<int>();
                        var blockGrides = BlockToGrides[theBlock];
                        for (int j = 0; j < blockGrides.Count; j++)
                        {
                            var theGrid = blockGrides[j];
                            if (theGrid.gemTs)
                            {
                                theInfo.gemList.Add((int)theGrid.GemType);
                                theInfo.gemPosList.Add(j);
                            }
                        }
                    }
                    theInfo.blockColor = (int)theColor;
                    theStorage.GenBlockInfos.Add(theInfo);
                }
            }
        }

        

    }

    List<GenBlockInfo> GetBlockGenListFromStorage()
    {
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            var theNew = IsNewLevel(curLevelId);
            if (theNew == false)
            {
                var theStorage = StorageExtension.GameBlockLevelStorage;
                if (theStorage.GenBlockInfos.Count != genBlockCount
                    || theStorage.GenBlockInfos.FindIndex(ele => ele.blockConfig >= 0) == -1)
                {
                    theNew = true;
                }
                else
                {
                    return theStorage.GenBlockInfos;
                }
            }
        }
        else if (CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            var theStorage = StorageExtension.GameEndlssStorage;
            if (theStorage.GameResult == (int)EnumGameResultType.EFST_NoResult)
            {
                if (theStorage.GenBlockInfos.Count != genBlockCount
                    || theStorage.GenBlockInfos.FindIndex(ele => ele.blockConfig >= 0) == -1)
                {
                    
                }
                else
                {
                    return theStorage.GenBlockInfos;
                }
            }
        }
        
        return null;
    }

    bool IsNewLevel(int levelId)
    {
        var theStorage = StorageExtension.GameBlockLevelStorage;
        var theLevelId = theStorage.CurLevelInfo.levelId;
        bool isNew = false;
        if (theLevelId <= 0 || theLevelId != levelId)
        {
            isNew = true;
        }
        else
        {
            if (theStorage.CurLevelInfo.gameResult == (int)EnumGameResultType.EFST_Fail
                || theStorage.CurLevelInfo.gameResult == (int)EnumGameResultType.EFST_None)
            {//失败了重新来过
                isNew = true;
            }
            else if (theStorage.CurLevelInfo.gameResult == (int)EnumGameResultType.EFST_Victory)
            {//成功了，下一关
                CLog.Exception("成功了还重新来过?");
                isNew = true;
            }
            else if (theStorage.CurLevelInfo.gameResult == (int)EnumGameResultType.EFST_NoResult)
            {//新需求--暂且不保存进度
                isNew = true;
            }
        }
        return isNew;
    }

    List<int> levelLayoutGridList = new List<int>();
    // HandleLevelLayout 方法已移动到 ModGame_Block.cs 部分类中

    void HandleEndlessLayout()
    {       
        var theStorage = StorageExtension.GameEndlssStorage;
        if (theStorage.GameResult != (int)EnumGameResultType.EFST_NoResult)
        {            
            return;
        }

        curScore = theStorage.AchiveScore;
        var theList = theStorage.BlockGridInfos.ToListEx();
        foreach (var item in theList)
        {
            var theIndex = item.y * boardWidth + item.x;
            var theGrid = grids[theIndex];

            if (item.y != theGrid.yIndex || item.x != theGrid.xIndex)
            {
                break;
            }

            theGrid.ShowState = EnumGridState.EGS_Show;
            theGrid.GemType = item.gridGem;
            theGrid.ColorState = item.gridColor;

            HandleBoardLayout(theGrid.yIndex * boardWidth + theGrid.xIndex, true);
            BlockPlayManager.Instance.AddBlock(theGrid.yIndex, theGrid.xIndex);
        }
        CLog.Error($"HandleEndlessLayout--{BlockPlayManager.Instance.EdgeCountAdvance}");
    }


    public void HandleEndlessBlockForGuide(int step)
    {
        if (step == 0)
        {
            {//生成block
                ClearBlocks();
                var theBlockId = 403;
                var thePos = 1;
                var theAssetName = GetBlockName(theBlockId);
                var theColor = EnumGridColor.EGC_SkyBlue;
                blockColors[thePos] = theColor;
                var theBlock = CreateOrGetBlock(theAssetName, blockContainers[thePos], theColor);
                blocks[thePos] = theBlock;

                genList.Clear();
                genList.Add(-1);
                genList.Add(theBlockId);
                genList.Add(-1);
                putdownCount = 3;
                genRoundIndex++;


                var theNoShowList = new List<int>();
                theNoShowList.Add(0);
                theNoShowList.Add(2);

                // 根据当前游戏类型查找对应的视图
                if (CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    var theEndlessView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
                    theEndlessView?.PlayBlockBornEffect(theNoShowList);
                }
                else
                {
                    var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                    theView?.PlayBlockBornEffect(theNoShowList);
                }
            }
        }
        else if (step == 1)
        {           
            {//生成block
                ClearBlocks();
                var theBlockId = 401;
                var thePos = 1;
                var theAssetName = GetBlockName(theBlockId);
                var theColor = EnumGridColor.EGC_Green;
                blockColors[thePos] = theColor;
                var theBlock = CreateOrGetBlock(theAssetName, blockContainers[thePos], theColor);
                blocks[thePos] = theBlock;

                genList.Clear();
                genList.Add(-1);
                genList.Add(theBlockId);
                genList.Add(-1);
                putdownCount = 3;
                genRoundIndex++;

                var theNoShowList = new List<int>();
                theNoShowList.Add(0);
                theNoShowList.Add(2);

                // 根据当前游戏类型查找对应的视图
                if (CurGameType == EnumBlockGameType.BlockGame_Endless)
                {
                    var theEndlessView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
                    theEndlessView?.PlayBlockBornEffect(theNoShowList);
                }
                else
                {
                    var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                    theView?.PlayBlockBornEffect(theNoShowList);
                }
            }

        }
    }

    public void HandleEndlessLayoutForGuide(int step)
    {
        List<BlockGridInfo> theList = null;
        if (step == 0)
        {
            var theJsonName = "guide_1";
            TextAsset jsonAsset = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>(theJsonName).GetInstance(curBlockPool.gameObject);
            if (jsonAsset == null)
            {
                CLog.Exception($"HandleEndlessLayout--初始布局json文件[{theJsonName}]不存在");
                return;
            }
            var theInstance = JsonConvert.DeserializeObject<levelLayout>(jsonAsset.text);
            theList = theInstance.tiles.ToListEx();
        }
        else if (step == 1)
        {
            var theJsonName = "guide_2";
            TextAsset jsonAsset = GameGlobal.GetMgr<ResMgr>().GetRes<TextAsset>(theJsonName).GetInstance(curBlockPool.gameObject);
            if (jsonAsset == null)
            {
                CLog.Exception($"HandleEndlessLayout--初始布局json文件[{theJsonName}]不存在");
                return;
            }
            var theInstance = JsonConvert.DeserializeObject<levelLayout>(jsonAsset.text);
            theList = theInstance.tiles.ToListEx();
        }
        
        if (theList == null) return;

        for (int i = 0; i < grids.Length; i++)
        {
            var theGrid = grids[i];
            if (theGrid == null) continue;

            BlockGridInfo item = theList.Find(ele => (ele.y * boardWidth + ele.x) == i);
            if (item == null)
            {
                if(theGrid.ShowState == EnumGridState.EGS_Show)
                {
                    theGrid.ShowState = EnumGridState.EGS_Hide;
                    theGrid.GemType = GemType.None;

                    HandleBoardLayout(i, false);
                    BlockPlayManager.Instance.RemoveBlock(theGrid.yIndex, theGrid.xIndex);
                }
                
            }
            else
            {
                if (theGrid.ShowState == EnumGridState.EGS_Hide)
                {
                    theGrid.ShowState = EnumGridState.EGS_Show;
                    theGrid.GemType = item.gridGem;
                    theGrid.ColorState = item.gridColor;

                    HandleBoardLayout(i, true);
                    BlockPlayManager.Instance.AddBlock(theGrid.yIndex, theGrid.xIndex);
                }
            }
        }

    }


    public void HandleEndlessBlockForGuide301()
    {
        {//生成block
            ClearBlocks();
            genList.Clear();
            var theList = new List<int>() { 414, 404, 302 };
            var theColor = EnumGridColor.EGC_Purple;
            for (int i = 0; i < theList.Count; i++)
            {
                var theBlockId = theList[i];
                var theAssetName = GetBlockName(theBlockId);
                blockColors[i] = theColor;
                var theBlock = CreateOrGetBlock(theAssetName, blockContainers[i], theColor);
                blocks[i] = theBlock;
                genList.Add(theBlockId);
            }
            putdownCount = 0;
        }
    }

    #endregion

    #region 拖动区域相关

    GameObject GetNextEventObject(PointerEventData pointerEventData)
    {
        GameObject go = null;
        EventSystem.current.RaycastAll(pointerEventData, tempRaycastResults);
        GameObject current = pointerEventData.pointerCurrentRaycast.gameObject;
        for (int i = 0; i < tempRaycastResults.Count; i++)
        {
            if (current != tempRaycastResults[i].gameObject)
            {
                go = tempRaycastResults[i].gameObject;
                break;
            }
        }

        tempRaycastResults.Clear();
        return go;
    }

    protected float uinodeContentY = 0;
    protected float contentLength;
    protected float firstPointX;
    public void OnPointerDown(BaseEventData baseEventData, Transform uiNodeRefer,Transform uiNodeContent = null)
    {
        _baseEventData = baseEventData;
        uinodeContentY = uiNodeContent.position.y;
        PointerEventData pointerEventData = baseEventData as PointerEventData;
        contentLength = Mathf.Abs(uiNodeContent.position.y - pointerEventData.pointerCurrentRaycast.worldPosition.y);

        if (firstEventGo != null)
        {
            return;
        }

        firstEventGo = GetNextEventObject(pointerEventData);
        if (IsDragNode(firstEventGo))
        {
            MoveHomeBlockImmediately();
            OnPointerDownDrageArea(pointerEventData, uiNodeRefer);
        }
        else
        {
            CLog.Warning("OnPointerDown-----DragNode is Null");
            ExecuteEvents.Execute(firstEventGo, pointerEventData, ExecuteEvents.pointerDownHandler);
        }
    }

    void OnPointerUpBoard(PointerEventData pointerEventData, ref bool isPutDown)
    {
        isDraging = false;
        var theBlock = ChoosedBlockReality;
        if (theBlock == null)
        {
            HandlePutDownBlock(false);
            return;
        }

        {//引导处理
            if(CurGameType == EnumBlockGameType.BlockGame_Stage)
            {
                Vector2Int guidePos = InvaildPos;
                UIView_Game.GetGuidePutPos(ref guidePos);
                if (guidePos != InvaildPos)
                {
                    if (guidePos != lastDragMatchPosLU)
                    {
                        HandlePutDownBlock(false);
                        return;
                    }
                }
            }
            else if(CurGameType == EnumBlockGameType.BlockGame_Endless)
            {
                Vector2Int guidePos = InvaildPos;
                UIView_GameEndless.GetGuidePutPos(ref guidePos);
                if (guidePos != InvaildPos)
                {
                    if (guidePos != lastDragMatchPosLU)
                    {
                        HandlePutDownBlock(false);
                        return;
                    }
                }
            }
        }
        
        if (tempAlphaIndex.Count == 0 || tempMatchIndex.Count == 0) // 棋盘外没匹配到
        {
            HandlePutDownBlock(false);
        }
        else
        {
            var isUsingBomb = IsUsingBomb;
            var thePutBlockId = genList[dragBlockNodeIndex];
            putdownCount++;
            isPutDown = true;
            HandlePutDownBlock();
            CLog.Info("投放成功");
            lastDragIndex = -1;
            lastDragMatchPos = InvaildMatchPos;
            lastDragMatchPosLU = InvaildPos;
            curBlockBestMatchPos.Clear();
            curHaveClearMatchPos.Clear();
            HandleActionRecord(thePutBlockId);
            HandleBlockProduct();

            if (isUsingBomb == false && putdownCount == 1 && dragBlockNodeIndex ==1)
            {
                needSetDragAreaSize = true;
                ClearDragAreaSize();
            }
        }
    }

    public void OnPointerUp(BaseEventData baseEventData, ref bool isPutDown)
    {
        CLog.Warning("OnPointerUp-----");
        if (firstEventGo == null)
        {
            ChoosedDragBlockContainer = null;
            _baseEventData = null;
            isDraging = false;
            return;
        }

        ClearTempMatchPool(-1);
        PointerEventData pointerEventData = baseEventData as PointerEventData;

        if (IsDragNode(firstEventGo))
        {
            OnPointerUpBoard(pointerEventData, ref isPutDown);//to do
        }
        else
        {
            ExecuteEvents.Execute(firstEventGo, pointerEventData, ExecuteEvents.pointerUpHandler);
            if (firstDragGo != null)
            {
                if (_baseEventData != null)
                {
                    OnEndDrag(_baseEventData);
                    _baseEventData = null;
                }
            }

            if (GetNextEventObject(pointerEventData) == firstEventGo && firstDragGo == null)
            {
                ExecuteEvents.Execute(firstEventGo, pointerEventData, ExecuteEvents.pointerClickHandler);
            }
        }

        _baseEventData = null;
        firstEventGo = null;
        isDraging = false;
    }

    void OnDragBlockNode(PointerEventData pointerEventData)
    {
        isDraging = true;
        var thePos = pointerEventData.pointerCurrentRaycast.worldPosition;

        var off = 1 - Mathf.Abs(uinodeContentY - thePos.y)/contentLength;
        var xoff = Mathf.Abs( firstPointX - thePos.x) * 2/contentLength;
       // CLog.Info($"OnDragBlockNode dragOffset:{thePos}    dragOffset:{dragOffset}");
       var finalOffset = Vector3.zero;
        //CLog.Info($"OnDragBlockNode dragOffset:{uinodeContentY}  contentLength:{contentLength}   dragOffset:{finalOffset}");
        if (thePos.x < firstPointX)
        {
            finalOffset = dragOffset + new Vector3(Mathf.Lerp(0, -1.1f, xoff), Mathf.Lerp(0, 1.3f, off), 0);
        }
        else
        {
            finalOffset = dragOffset + new Vector3(Mathf.Lerp(0, 1.1f, xoff), Mathf.Lerp(0, 1.3f, off), 0);
        }
        var theTargetPos = thePos + finalOffset;
        var theBlockContainer = ChoosedDragBlockContainer;
        if (theBlockContainer) theBlockContainer.position = theTargetPos;
    }
    public void OnDrag(BaseEventData baseEventData)
    {
        _baseEventData = baseEventData;
        PointerEventData pointerEventData = baseEventData as PointerEventData;
        if (IsDragNode(firstDragGo))
        {
            if (curPdSequence != null)
            {
                curPdSequence.Complete();
                //CLog.Exception("IsChoosedBlock 不应该为空");
                return;
            }
            OnDragBlockNode(pointerEventData);


            bool bRes = TryMatchBlock(pointerEventData);
            if (bRes == false)
            {
                tempMatchCol.Clear();
                tempMatchRow.Clear();
            }
            //to do
        }
        else
        {
            ExecuteEvents.ExecuteHierarchy(firstDragGo, pointerEventData, ExecuteEvents.dragHandler);
        }
    }

    public void OnBeginDrag(BaseEventData baseEventData)
    {
        if (firstEventGo == null || firstDragGo != null)
        {
            return;
        }
        _baseEventData = baseEventData;
        firstDragGo = firstEventGo;
        lastDragIndex = -1;
        lastDragMatchPos = InvaildMatchPos;
        lastDragMatchPosLU = InvaildPos;
        ExecuteEvents.ExecuteHierarchy(firstDragGo, baseEventData, ExecuteEvents.beginDragHandler);
    }

    public void OnEndDrag(BaseEventData baseEventData)
    {
        CLog.Warning("OnEndDrag-----");
        if (firstEventGo != null || firstDragGo == null)
        {
            return;
        }

        lastDragIndex = -1;
        lastDragMatchPos = InvaildMatchPos;
        lastDragMatchPosLU = InvaildPos;
        curBlockBestMatchPos.Clear();
        curHaveClearMatchPos.Clear();
        ExecuteEvents.ExecuteHierarchy(firstDragGo, baseEventData, ExecuteEvents.endDragHandler);
        firstDragGo = null;
    }

    Sequence curPdSequence;
    public void OnPointerDownDrageArea(PointerEventData pointerEventData, Transform uiNodeRefer)
    {
        var theIndex = GetDragAreaIndex(firstEventGo);
        EventBus.Dispatch(new EventBlockClick(theIndex));
        var theBlockContainer = blockContainers[theIndex];
        var thePos = pointerEventData.pointerCurrentRaycast.worldPosition;
        var theTargetPos = theBlockContainer.position;
        thePos.z = theTargetPos.z;
        theTargetPos.y = uiNodeRefer.position.y;
        dragOffset = theTargetPos - thePos;
        firstPointX = thePos.x;
        theBlockContainer.DOKill();
        GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_2");
        ChoosedDragBlockContainer = theBlockContainer;
        CLog.Warning($"OnPointerDownDrageArea--{theBlockContainer.name}");
        curPdSequence = TMUtility.SequenceOpen(theBlockContainer, 1, theTargetPos, 0.075f, () =>
        {
            theBlockContainer.SetSiblingIndex(genBlockCount - 1);
            curPdSequence = null;

            var theBlock = blocks[theIndex];
            if (theBlock == null) return;
            var tempBlockInfo = blockNameToMatchMatrix[theBlock.name];
            switch (curProductType)
            {
                
                case EnumBlockProductType.EBPT_ComplexityUp:
                case EnumBlockProductType.EBPT_ComplexityDown:
                case EnumBlockProductType.EBPT_MultiClear:
                case EnumBlockProductType.EBPT_ClearScreen:
                case EnumBlockProductType.EBPT_Normal:
#if UNITY_EDITOR
                case EnumBlockProductType.EBPT_None:
#endif
                    {

                        if(curProductType == EnumBlockProductType.EBPT_ComplexityUp)
                        {
                            if (buildUpType == BlockPlayManager.EnumBuildUpType.EBT_ComplexityUp) break;
                        }
                        var theMatchRes = BlockPlayManager.Instance.MatrixBlockMatchForFillVacancy(tempBlockInfo, ref curBlockBestMatchPos);
                        if (!theMatchRes)
                        {
                            theMatchRes = BlockPlayManager.Instance.MatrixBlockMatchBestForBestPut(tempBlockInfo, ref curBlockBestMatchPos);
                        }
                        if (theMatchRes)
                        {
#if DEBUG || DEVELOPMENT_BUILD
                            StringBuilder sb = new StringBuilder();
                            for (var i = 0; i < curBlockBestMatchPos.Count; i++)
                            {
                                var tempPos = curBlockBestMatchPos[i];
                                sb.Append($"{tempPos.ToString()},");
                            }
                            CLog.Error($"最优解位置:{sb.ToString()}");
#endif
                            BlockPlayManager.Instance.MatrixBlockMatchBestForClear(tempBlockInfo, ref curHaveClearMatchPos);
                            List<BlockGrid> blockGrides = GetBlockGridList(theBlock);
                            if (blockGrides != null && blockGrides.Count > 0)
                            {
                                var theFirstGrid = blockGrides[0];
                                if (theFirstGrid.pos != 0)
                                {
                                    var referY = theFirstGrid.pos / tempBlockInfo.colNum;
                                    var referX = theFirstGrid.pos % tempBlockInfo.colNum;
                                    for (var i = 0; i < curBlockBestMatchPos.Count; i++)
                                    {
                                        var tempPos = curBlockBestMatchPos[i];
                                        tempPos.x = tempPos.x + referX;
                                        tempPos.y = tempPos.y + referY;
                                        curBlockBestMatchPos[i] = tempPos;
                                    }

                                    for (var i = 0;i < curHaveClearMatchPos.Count;i++)
                                    {
                                        var tempPos = curHaveClearMatchPos[i];
                                        tempPos.x = tempPos.x + referX;
                                        tempPos.y = tempPos.y + referY;
                                        curHaveClearMatchPos[i] = tempPos;
                                    }
                                }
                            }
                            else
                            {
                                curBlockBestMatchPos.Clear();
                            }
                        }
                        break;
                    }
            }

#if UNITY_EDITOR
            
            Vector2Int tPos = Vector2Int.zero;
            var theBestEdge = 0;
            var theRemove = 0;
            BlockPlayManager.Instance.MatrixBlockMatchBest_Watch(tempBlockInfo, ref tPos, ref theBestEdge, out theRemove);
#endif
        });
    }

    #endregion

    #region block块匹配(Match)

    List<Transform> bombMatchList = new List<Transform>();

    private int comboNum = 0;
    private int consecutiveNonMatchCount = 0; // 连续无消除放置次数
    private int lastDragIndex = -1;

    private Vector2 lastDragMatchPos = InvaildMatchPos;
    private Vector2Int lastDragMatchPosLU = InvaildPos;
    private List<Vector2Int> curBlockBestMatchPos = new List<Vector2Int>();
    private List<Vector2Int> curHaveClearMatchPos = new List<Vector2Int>();

    bool CheckBlockAllMatch(List<BlockGrid> blockGrides, BlockMatchMatrix tempBlockInfo, int theIndex, bool isUsingBomb)
    {
        if(tempBlockInfo.colNum == 0)return false;
        var theGridCount = blockGrides?.Count ?? 0;
        if (theGridCount == 0)return false;

        var theFirstGrid = blockGrides[0];
        
        var theAllGridCount = grids.Length;
        var tempRow = theIndex / boardWidth;
        var tempCol = theIndex % boardWidth;

        var referY = theFirstGrid.pos / tempBlockInfo.colNum;
        var referX = theFirstGrid.pos % tempBlockInfo.colNum;
        bool isAllMatch = true;
        for (var i = 0; i < theGridCount; i++)
        {
            var index = theIndex;
            var theGrid = blockGrides[i];
            if (i != 0)
            {
                var theY = theGrid.pos / tempBlockInfo.colNum;
                var theX = theGrid.pos % tempBlockInfo.colNum;
                var theYDiff = theY - referY;
                var theXDiff = theX - referX;

                var gRow = tempRow + theYDiff;
                var gCol = tempCol + theXDiff;
                //Debug.Log($"TryMatchBlock--row:{gRow}, col:{gCol}");
                if (gCol < 0 || gCol >= boardWidth || gRow < 0 || gRow >= boardHeight)
                {
                    index = -1;
                }
                else
                {
                    index = gRow * boardWidth + gCol;
                }
            }

            if (isUsingBomb)
            {
                if (index < 0)
                {
                    continue;
                }
            }
            else
            {
                if (index < 0 || index >= theAllGridCount)
                {
                    isAllMatch = false;
                    break;
                }

                var theGridInfo = grids[index];
                if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                {
                    isAllMatch = false;
                    break;
                }
            }
        }

        return isAllMatch;
    }

    bool TryMatchBlock(PointerEventData pointerEventData, bool isPutDown = false)
    {
        bombMatchList.Clear();
        bool bRes = false;
        var theBlock = ChoosedBlockReality;
        if (!theBlock)
        {
            CLog.Error($"填充块为空,退出处理");
            tempMatchIndex.Clear();
            HandleMatchState();
            return bRes;
        }

        var theChildCount = theBlock.childCount;
        if (theChildCount == 0)
        {
            CLog.Error($"填充块为空,退出处理");
            tempMatchIndex.Clear();
            HandleMatchState();
            return bRes;
        }

        List<BlockGrid> blockGrides = GetBlockGridList(theBlock);
        if(blockGrides == null || blockGrides.Count <= 0)
        {
            CLog.Error($"填充块对应的格子信息为空,退出处理");
            tempMatchIndex.Clear();
            HandleMatchState();
            return false;
        }

        bool isAllMatch = true;
        bool isUsingBomb = IsUsingBomb;
        var theFirstGrid = blockGrides[0];
        if (theFirstGrid == null || theFirstGrid.RectTf == null)
        {
            CLog.Error($"填充块为空,退出处理--");
            tempMatchIndex.Clear();
            HandleMatchState();
            return false;
        }

        var theBName = GetBlockName(genList[dragBlockNodeIndex]);
        if (!blockNameToMatchMatrix.ContainsKey(theBName))
        {
            CLog.Error($"填充块对应的匹配信息为空,退出处理");
            tempMatchIndex.Clear();
            HandleMatchState();
            return false;
        }

        var tempBlockInfo = blockNameToMatchMatrix[theBName];
        Vector2 referPos = curGridContainer.InverseTransformPoint(theFirstGrid.RectTf.position);
        var theIndex = PosToIndex(referPos);
        if(curBlockBestMatchPos.Count > 0)
        {
            var tempCheckIndex = curHaveClearMatchPos.FindIndex(ele => theIndex == (ele.y * boardWidth + ele.x));
            if (tempCheckIndex < 0)
            {
                var theMinDis = int.MaxValue;
                bool bNeedCheckDis = curBlockBestMatchPos.Count > 1;
                foreach (var matchPos in curBlockBestMatchPos)
                {
                    var checkIndex = matchPos.y * boardWidth + matchPos.x;
                    Grid theGrid = null;
                    if (checkIndex >= 0 && checkIndex < grids.Length) theGrid = grids[checkIndex];
                    if (theGrid != null)
                    {
                        Vector2 theGridPos = theGrid.RectTf.localPosition;
                        if (Math.Abs(referPos.y - theGrid.RectTf.localPosition.y) <= gridSize.y
                            && Math.Abs(referPos.x - theGrid.RectTf.localPosition.x) <= gridSize.x)
                        {
                            if(bNeedCheckDis == false)
                            {
                                if (lastDragIndex != -1 && lastDragIndex == checkIndex)
                                {
                                    if (tempMatchIndex.Count > 0) return true;
                                }
                            }

                            var theCheck = CheckBlockAllMatch(blockGrides, tempBlockInfo, checkIndex, isUsingBomb);
                            if (theCheck)
                            {
                                if(bNeedCheckDis)
                                {
                                    var theDis = (int)Vector2.Distance(referPos, theGridPos);
                                    if (theDis < theMinDis)
                                    {
                                        theMinDis = theDis;
                                        theIndex = checkIndex;
                                    }
                                }
                                else
                                {
                                    theIndex = checkIndex;
                                }
                                
                            }
                        }
                    }
                }
            }
        }

        var blockGridIndex = -1;
        if (theIndex == -1 && isUsingBomb)
        {
            tempMatchIndex.Clear();
            HandleMatchState();
            for (var i = 0; i < blockGrides.Count; i++)
            {
                var theGrid = blockGrides[i];
                Vector3 tempPos = curGridContainer.InverseTransformPoint(theGrid.RectTf.position);
                theIndex = PosToIndex(tempPos);
                if (theIndex < 0)
                {
                    continue;
                }

                if (tempMatchIndex.Contains(theIndex))
                {
                    CLog.Error("tempMatchIndex 有重复");
                    continue;
                }

                if (blockGridIndex == -1) blockGridIndex = theGrid.pos;

                tempMatchIndex.Add(theIndex);
            }

            var theCount = tempMatchIndex.Count;
            bRes = theCount >= 1 && theCount <= 9;
            if (!bRes)
            {
                HandleMatchState();
                tempMatchIndex.Clear();
                return bRes;
            }
        }
        else
        {
            if (theIndex != -1)
            {
                if (lastDragIndex != -1 && lastDragIndex == theIndex)
                {
                    if (tempMatchIndex.Count > 0)
                    {
                        //Debug.Log($"TryMatchBlock--theIndex:{theIndex}, lastDragIndex:{lastDragIndex}");
                        return true;
                    }
                }
                else
                {
                    lastDragIndex = theIndex;                    
                }
            }
            else
            {
                blockPutMatchIndex = -1;
                lastDragIndex = -1;
            }
            
            tempMatchIndex.Clear();
            var theAllGridCount = grids.Length;

            var tempRow = theIndex / boardWidth;
            var tempCol = theIndex % boardWidth;

            //Debug.Log($"TryMatchBlock--row:{tempRow}, col:{tempCol}");
            var referY = theFirstGrid.pos / tempBlockInfo.colNum;
            var referX = theFirstGrid.pos % tempBlockInfo.colNum;
            for (var i = 0; i < blockGrides.Count; i++)
            {
                var index = theIndex;
                var theGrid = blockGrides[i];
                if (i != 0)
                {
                    var theY = theGrid.pos / tempBlockInfo.colNum;
                    var theX = theGrid.pos % tempBlockInfo.colNum;
                    var theYDiff = theY - referY;
                    var theXDiff = theX - referX;

                    var gRow = tempRow + theYDiff;
                    var gCol = tempCol + theXDiff;
                    //Debug.Log($"TryMatchBlock--row:{gRow}, col:{gCol}");
                    if (gCol < 0 || gCol >= boardWidth || gRow < 0 || gRow >= boardHeight)
                    {
                        index = -1;
                    }
                    else
                    {
                        index = gRow * boardWidth + gCol;
                    }
                }

                if (isUsingBomb)
                {
                    if (index < 0)
                    {
                        continue;
                    }
                }
                else
                {
                    if (index < 0 || index >= theAllGridCount)
                    {
                        isAllMatch = false;
                        break;
                    }

                    var theGridInfo = grids[index];
                    if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                    {
                        isAllMatch = false;
                        break;
                    }
                }

                if (tempMatchIndex.Contains(index))
                {
                    CLog.Error("tempMatchIndex 有重复");
                    continue;
                }

                if (blockGridIndex == -1) blockGridIndex = theGrid.pos;

                tempMatchIndex.Add(index);
            }

            bRes = isAllMatch;
            if(isUsingBomb)
            {
                var theCount = tempMatchIndex.Count;
                bRes = theCount >= 1 && theCount <= 9;
            }

            if (!bRes)
            {
                tempMatchIndex.Clear();
                if (lastDragMatchPos != InvaildMatchPos)
                {
                    var theReferPos = (Vector2)referPos;
                    var theDis = Vector2.Distance(lastDragMatchPos, theReferPos);
                    if (theDis <= Grid.OneDotFiveGrid)
                    {
                        tempMatchIndex.AddRange(tempAlphaIndex);
                        return true;
                    }
                    else
                    {
                        lastDragMatchPos = InvaildMatchPos;
                        lastDragMatchPosLU = InvaildPos;
                    }
                }

                HandleMatchState();
                return bRes;
            }
            else
            {
                lastDragMatchPosLU = new Vector2Int(tempCol - referX, tempRow - referY);

                if (theFirstGrid.pos != 0)
                {
                    var theYDiff = 0 - referY;
                    var theXDiff = 0 - referX;
                    var gRow = tempRow + theYDiff;
                    var gCol = tempCol + theXDiff;
                    if (gCol < 0 || gCol >= boardWidth || gRow < 0 || gRow >= boardHeight)
                    {
                        CLog.Exception($"填充块填充块匹配出现异常");
                        blockPutMatchIndex = -1;
                    }
                    else
                    {
                        blockPutMatchIndex = gRow * boardWidth + gCol;
                    }
                }
                else
                {
                    blockPutMatchIndex = theIndex;
                }

                if (theIndex >= 0 && theIndex < grids.Length)
                {
                    var theGrid = grids[theIndex];
                    lastDragMatchPos = theGrid.RectTf.localPosition;
                }
                else
                {
                    lastDragMatchPos = referPos;
                }
            }
        }

        if (isUsingBomb)
        {
            HandleMatchState();
            HandleMatchState(false);

            if (blockGridIndex != -1)
            {
                var theTempIndex = tempMatchIndex[0];
                var theGrid = grids[theTempIndex];

                var theGridPos = theGrid.RectTf.localPosition;

                var theBlockGridY = blockGridIndex / tempBlockInfo.colNum;
                var theBlockGridX = blockGridIndex % tempBlockInfo.colNum;

                var theLeftUpPos = new Vector3((0 - theBlockGridX) * Grid.GridWidth + theGridPos.x, -(0 - theBlockGridY) * Grid.GridHeight + theGridPos.y);
                var theRightDownPos = new Vector3((tempBlockInfo.colNum - theBlockGridX - 1) * Grid.GridWidth + theGridPos.x,
                    -(tempBlockInfo.rowNum - theBlockGridY - 1) * Grid.GridHeight + theGridPos.y);

                var targetPos = Vector3.one;
                targetPos.x = (theRightDownPos.x + theLeftUpPos.x) * 0.5f;
                targetPos.y = (theRightDownPos.y + theLeftUpPos.y) * 0.5f;
                ShowBombFrameEffect(targetPos, true);
            }
            
        }
        else
        {
            HandleMatchState();
            HandleMatchState(false);
            RefreshMatchRowCol();

            var theColor = ChoosedBlockColor;
            tempRowPos.Clear();
            tempColPos.Clear();
            var tempPos = 0;
            foreach (var theRow in tempMatchRow)
            {
                tempPos = 0;
                var theBegain = theRow * boardWidth;
                while (tempPos < boardWidth)
                {
                    var curIndex = theBegain + tempPos;
                    tempPos++;
                    var theGridInfo = grids[curIndex];
                    if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                    {
                        theGridInfo.TempColor = ChoosedBlockColor;
                    }

                    if (tempPos == 5)
                    {
                        var thePos = theGridInfo.RectTf.localPosition;
                        thePos.x -= Grid.HalfGridWidth;
                        tempRowPos.Add(thePos);
                    }
                }
            }

            foreach (var theCol in tempMatchCol)
            {
                tempPos = 0;
                while (tempPos < boardHeight)
                {
                    var curIndex = theCol + tempPos * boardWidth;
                    tempPos++;
                    var theGridInfo = grids[curIndex];
                    if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                    {
                        theGridInfo.TempColor = ChoosedBlockColor;
                    }

                    if (tempPos == 5)
                    {
                        var thePos = theGridInfo.RectTf.localPosition;
                        thePos.y += Grid.HalfGridHeight;
                        tempColPos.Add(thePos);
                    }
                }
            }

            ShowTryMatchEffect(theColor);
        }

        return bRes;
    }

    void HandleMatchState(bool clear = true, bool putDown = false)
    {
        bool isUsingBomb = CurGameType == EnumBlockGameType.BlockGame_Stage && UIView_Game.CurUsingItem == EItemType.Bomb;
        var theEffectNodeName = "bombLight";
        if (clear)
        {
            ClearTempTryMatchPool();
            foreach (var curIndex in tempAlphaIndex)
            {
                var theGridInfo = grids[curIndex];
                if (isUsingBomb)
                {
                    var theEffect = theGridInfo.RectTf.Find(theEffectNodeName);
                    if (theEffect != null)
                    {
                        theEffect.SetParent(null);
                        GameObject.Destroy(theEffect.gameObject);
                    }
                }
                else
                {
                    if (theGridInfo.ShowState == EnumGridState.EGS_Alpha)
                    {
                        theGridInfo.ShowState = EnumGridState.EGS_Hide;
                    }
                }      
            }
            tempAlphaIndex.Clear();

            if(putDown == false)
            {
                var tempPos = 0;
                foreach (var theRow in tempMatchRow)
                {
                    tempPos = 0;
                    var theBegain = theRow * boardWidth;
                    while (tempPos < boardWidth)
                    {
                        var curIndex = theBegain + tempPos;
                        tempPos++;
                        var theGridInfo = grids[curIndex];
                        if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                        {
                            theGridInfo.ResumeColorState();
                        }
                    }
                }

                foreach (var theCol in tempMatchCol)
                {
                    tempPos = 0;
                    while (tempPos < boardHeight)
                    {
                        var curIndex = theCol + tempPos * boardWidth;
                        tempPos++;
                        var theGridInfo = grids[curIndex];
                        if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                        {
                            theGridInfo.ResumeColorState();
                        }
                    }
                }
            }

            ShowBombFrameEffect(Vector3.zero, false);
        }
        else
        {
            var theColor = ChoosedBlockColor;
            foreach (var curIndex in tempMatchIndex)
            {
                var theGridInfo = grids[curIndex];
                if (isUsingBomb)
                {
                    var theGo = theGridInfo.RectTf.Find(theEffectNodeName);
                    if (theGo == null)
                    {
                        theGo = theGridInfo.RectTf.CreateChild(theEffectNodeName).transform;
                    }
                    var bombLightAsset = "VFX_bomb_light";
                    var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(bombLightAsset).GetInstance(theGo.gameObject);
                    if (theAsset != null)
                    {
                        var theEffectGo = GameObject.Instantiate(theAsset, theGo.transform);
                        theEffectGo.name = theEffectNodeName;
                        theEffectGo.transform.localScale = Vector3.one;
                        theEffectGo.transform.localPosition = Vector3.zero;
                        theEffectGo.gameObject.SetActive(true);
                    }
                }
                else
                {
                    theGridInfo.ColorState = theColor;
                    theGridInfo.ShowState = EnumGridState.EGS_Alpha;
                }

                tempAlphaIndex.Add(curIndex);

            }
        }
    }

    List<KeyValuePair<int, Vector3>> achiveGemList = new List<KeyValuePair<int, Vector3>>();

    void RefreshMatchRowCol()
    {
        tempMatchRow.Clear();
        tempMatchCol.Clear();
        var tempPos = 0;
        foreach (var index in tempMatchIndex)
        {
            var theRow = index / boardWidth;
            var theCol = index % boardWidth;

            if (!tempMatchRow.Contains(theRow))
            {
                tempPos = 0;
                var theBegain = theRow * boardWidth;
                bool matchRow = true;
                while (tempPos < boardWidth)
                {
                    var curIndex = theBegain + tempPos;
                    tempPos++;
                    var theGridInfo = grids[curIndex];
                    if (theGridInfo.ShowState != EnumGridState.EGS_Show)
                    {
                        if (tempMatchIndex.Contains(curIndex) == false)
                        {
                            matchRow = false;
                            break;
                        }
                    }
                }
                if (matchRow) tempMatchRow.Add(theRow);
            }

            if (!tempMatchCol.Contains(theCol))
            {
                tempPos = 0;
                bool matchCol = true;
                while (tempPos < boardHeight)
                {
                    var curIndex = theCol + tempPos * boardWidth;
                    tempPos++;
                    var theGridInfo = grids[curIndex];
                    if (theGridInfo.ShowState != EnumGridState.EGS_Show)
                    {
                        if (tempMatchIndex.Contains(curIndex) == false)
                        {
                            matchCol = false;
                            break;
                        }
                    }
                }
                if (matchCol) tempMatchCol.Add(theCol);
            }
        }
    }
    
    /// <summary>
    /// 检查是否为木箱
    /// </summary>
    /// <param name="gridInfo">格子信息</param>
    /// <returns>是否为木箱</returns>
    private bool IsWoodenCrate(Grid gridInfo)
    {
        return gridInfo.IsMultiBlock;
    }

    /// <summary>
    /// 处理单个格子的消除逻辑
    /// </summary>
    /// <param name="gridInfo">要处理的格子信息</param>
    /// <param name="curIndex">格子索引</param>
    /// <param name="row">行索引（用于RemoveBlock调用）</param>
    /// <param name="col">列索引（用于RemoveBlock调用）</param>
    /// <param name="processedRoots">已处理的2x2方块根节点集合</param>
    /// <param name="achiveGemList">收集的宝石列表</param>
    /// <param name="eliminationType">消除类型：0=炸弹，1=行消除，2=列消除</param>
    /// <returns>是否真正清除了方块</returns>
    private bool ProcessGridElimination(Grid gridInfo, int curIndex, int row, int col, HashSet<Grid> processedRoots, List<KeyValuePair<int, Vector3>> achiveGemList, int eliminationType)
    {
        bool reallyClear = true;

        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
             // 处理2x2方块
            if (gridInfo.advancedBlockRoot != null)
            {
                var root = gridInfo.advancedBlockRoot;
                if (root.ShowState == EnumGridState.EGS_Show && !processedRoots.Contains(root) && root.advancedBlockRemainingLayers > 0)
                {
                    processedRoots.Add(root);
                    root.advancedBlockRemainingLayers--;

                    // 为2x2方块消除一层掩码
                    var rootIndex = root.yIndex * boardWidth + root.xIndex;
                    Handle2x2BlockMaskRemove(rootIndex, root.advancedBlockRemainingLayers);

                    if (root.advancedBlockRemainingLayers <= 0)
                    {
                        if (eliminationType == 0) // 炸弹消除
                        {
                            // 最后一层，清除整个2x2方块并触发收集事件
                            for (int r = 0; r < 2; r++)
                            {
                                for (int c = 0; c < 2; c++)
                                {
                                    var partX = root.xIndex + c;
                                    var partY = root.yIndex + r;

                                    // 添加边界检查
                                    if (partX >= boardWidth || partY >= boardHeight)
                                    {
                                        continue;
                                    }

                                    var gridToClear = grids[partY * boardWidth + partX];
                                    if (gridToClear.ShowState == EnumGridState.EGS_Show)
                                    {
                                        var clearIndex = gridToClear.yIndex * boardWidth + gridToClear.xIndex;
                                        // 2x2方块已经在Handle2x2BlockMaskRemove中处理掩码，这里只需要清理棋盘布局
                                        HandleBoardLayout(clearIndex, false);
                                        BlockPlayManager.Instance.RemoveBlock(gridToClear.yIndex, gridToClear.xIndex);
                                        gridToClear.ShowState = EnumGridState.EGS_Hide;
                                        gridToClear.AdvancedBlockId = 0;
                                    }
                                }
                            }
                            // 触发猫收集事件
                            TriggerCatCollectionFromAdvancedBlock(root);
                        }
                        else // 行/列消除
                        {
                            ClearAdvancedBlock(root, processedRoots);
                        }
                    }
                    else
                    {
                        root.Refresh2x2Block();
                    }
                }
                reallyClear = false;
            }
            // 处理覆盖物消除逻辑
            else if (gridInfo.OverlayBlockId > 0 || gridInfo.TotalOverlayLayers > 0)
            {
                // 优先消耗冰层，然后才是树叶
                bool consumed = false;
                bool triggerIceCollection = false;
                bool triggerLeafCollection = false;

                if (gridInfo.IceOverlayLayers > 0)
                {
                    gridInfo.IceOverlayLayers--;
                    consumed = true;
                    // 冰块只在最后一层消除时触发收集
                    if (gridInfo.IceOverlayLayers == 0 && HasTargetType(EnumTargetType.ETT_Ice))
                    {
                        triggerIceCollection = true;
                    }
                }
                else if (gridInfo.LeafOverlayLayers > 0)
                {
                    gridInfo.LeafOverlayLayers--;
                    consumed = true;
                    // 树叶消除时立即触发收集
                    if (HasTargetType(EnumTargetType.ETT_Leaf))
                    {
                        triggerLeafCollection = true;
                    }
                }
                else if (gridInfo.OverlayLayer > 0)
                {
                    // 兼容旧逻辑（只有一种覆盖物）
                    int oldOverlayId = gridInfo.OverlayBlockId;
                    gridInfo.OverlayLayer--;
                    if (gridInfo.OverlayLayer <= 0)
                    {
                        gridInfo.OverlayBlockId = 0;
                        // 根据覆盖物类型触发收集
                        if (oldOverlayId == OverlayConstants.OverlayIdLeaf && HasTargetType(EnumTargetType.ETT_Leaf))
                        {
                            triggerLeafCollection = true;
                        }
                        else if (oldOverlayId == OverlayConstants.OverlayIdIce && HasTargetType(EnumTargetType.ETT_Ice))
                        {
                            triggerIceCollection = true;
                        }
                    }
                    consumed = true;
                }

                if (consumed)
                {
                    // 统一同步主覆盖
                    int newOverlayId;
                    int newOverlayLayers;
                    // 优先冰块
                    if (gridInfo.IceOverlayLayers > 0)
                    {
                        newOverlayId = OverlayConstants.OverlayIdIce;
                        newOverlayLayers = gridInfo.IceOverlayLayers;
                    }
                    // 然后是树叶
                    else if (gridInfo.LeafOverlayLayers > 0)
                    {
                        newOverlayId = OverlayConstants.OverlayIdLeaf;
                        newOverlayLayers = gridInfo.LeafOverlayLayers;
                    }
                    else
                    {
                        newOverlayId = 0;
                        newOverlayLayers = 0;
                    }

                    gridInfo.OverlayBlockId = newOverlayId;
                    gridInfo.OverlayLayer = newOverlayLayers;

                    Handle1x1BlockMaskRemove(curIndex, gridInfo);
                    PlayOverlayBlockDestroyEffect(gridInfo.overlayBlockTs);

                    // 触发覆盖物收集事件
                    Vector3 position = gridInfo.RectTf != null ? gridInfo.RectTf.position : Vector3.zero;
                    if (triggerLeafCollection)
                    {
                        EventBus.Dispatch(new EventGameLeafAchive(1, position));
                    }
                    if (triggerIceCollection)
                    {
                        EventBus.Dispatch(new EventGameIceAchive(1, position));
                    }
                }

                reallyClear = false; // 只消除覆盖物一层
            }
            // 处理固定物消除逻辑：固定物参与消除但不真正被移除
            else if (gridInfo.IsFixedBlock)
            {
                reallyClear = false;
                // 固定物（鸟窝）不移除掩码，因为它是永久存在的
                // 新增：触发鸟收集事件
                if (HasTargetType(EnumTargetType.ETT_Bird))
                {
                    // 简化位置计算逻辑，优先使用RectTf位置，确保安全性
                    Vector3 position = gridInfo.RectTf != null ? gridInfo.RectTf.position : Vector3.zero;
                    EventBus.Dispatch(new EventGameBirdAchive(1, position));
                }
            }
            // 处理多层方块消除逻辑
            else if (gridInfo.IsMultiBlock)
            {
                if (gridInfo.MultiBlockClearTime == 1)
                {
                    // 判断是否为木箱（在重置标记之前判断）
                    bool isWoodenCrate = IsWoodenCrate(gridInfo);

                    // 最后一层，直接消除
                    gridInfo.MultiBlockClearTime--;
                    gridInfo.IsMultiBlock = false; // 重置多层方块标记

                    // 新增：触发木箱收集事件（消除最后一层时触发）
                    if (isWoodenCrate)
                    {
                        // 播放木箱消除特效
                        PlayWoodCrackEffect(gridInfo.RectTf);
                        // 简化位置计算逻辑，优先使用RectTf位置，确保安全性
                        Vector3 position = gridInfo.RectTf != null ? gridInfo.RectTf.position : Vector3.zero;
                        EventBus.Dispatch(new EventGameWoodenCrateAchive(1, position));
                    }

                    if (eliminationType == 1 || eliminationType == 2) // 行/列消除需要真正消除方块
                    {
                        // 真正消除方块
                        // 普通方块清除后，移除一层掩码
                        Handle1x1BlockMaskRemove(curIndex, gridInfo);
                        HandleBoardLayout(curIndex, false);
                        BlockPlayManager.Instance.RemoveBlock(row, col);
                        gridInfo.ShowState = EnumGridState.EGS_Hide;
                        levelLayoutGridList.Remove(curIndex);
                    }

                    reallyClear = eliminationType == 0; // 炸弹消除时返回true，行列消除时已经处理完毕
                }
                else
                {
                    // 还有多层，先播放动画
                    int previousClearTime = gridInfo.MultiBlockClearTime;
                    // 动画播放完成后，真正执行状态更新
                    gridInfo.MultiBlockClearTime--;
                    // 多层方块层数减少后，移除一层掩码
                    Handle1x1BlockMaskRemove(curIndex, gridInfo);

                    if (eliminationType == 0) // 炸弹消除
                    {
                        // 播放多层方块消除动画，在回调中执行状态更新
                        PlayMultiBlockDestroyEffect(gridInfo, previousClearTime, previousClearTime - 1, () =>
                        {
                            // 如果这是最后一层，重置多层方块标记
                            if (gridInfo.MultiBlockClearTime <= 0)
                            {
                                gridInfo.IsMultiBlock = false;
                            }
                        });
                    }
                    else // 行/列消除
                    {
                        // 播放多层方块消除动画，在回调中执行状态更新
                        PlayMultiBlockDestroyEffect(gridInfo, previousClearTime, previousClearTime - 1, () =>
                        {
                            // 新增：触发木箱收集事件（消除最后一层时触发）
                            bool isWoodenCrate = IsWoodenCrate(gridInfo);
                            if (isWoodenCrate && gridInfo.MultiBlockClearTime == 0)
                            {
                                // 播放木箱消除特效
                                PlayWoodCrackEffect(gridInfo.RectTf);
                                // 使用安全的位置计算
                                Vector3 position = gridInfo.RectTf != null ? gridInfo.RectTf.position : Vector3.zero;
                                EventBus.Dispatch(new EventGameWoodenCrateAchive(1, position));
                                // 重置多层方块标记
                                gridInfo.IsMultiBlock = false;
                            }
                        });
                    }

                    reallyClear = false; // 暂时不参与消除，等动画完成后再判断
                }
            }
            // 消除一般方块
            else
            {
                // 普通方块清除后，移除一层掩码
                Handle1x1BlockMaskRemove(curIndex, gridInfo);
                HandleBoardLayout(curIndex, false);
                BlockPlayManager.Instance.RemoveBlock(row, col);

                // 修复：所有消除类型都应该收集宝石
                if (gridInfo.GemType > GemType.None && gridInfo.GemType < GemType.GemEnd && gridInfo.gemTs != null)
                {
                    achiveGemList.Add(new KeyValuePair<int, Vector3>((int)gridInfo.GemType, gridInfo.gemTs.position));
                }
                gridInfo.ShowState = EnumGridState.EGS_Hide;

                if (eliminationType == 1 || eliminationType == 2) // 行/列消除
                {
                    levelLayoutGridList.Remove(curIndex);
                }
            }
        }
        // 无尽模式-消除方块
        else
        {
            HandleBoardLayout(curIndex, false);
            BlockPlayManager.Instance.RemoveBlock(row, col);
            gridInfo.ShowState = EnumGridState.EGS_Hide;

            if (eliminationType == 1 || eliminationType == 2) // 行/列消除
            {
                levelLayoutGridList.Remove(curIndex);
            }
        }

        return reallyClear;
    }

    void HandlePutDownBlock(bool bForMatchSuccess = true)
    {
        HandleMatchState(true, bForMatchSuccess);
        if (bForMatchSuccess == false)
        {
            MoveHomeBlock();
            return;
        }

        //消除增加震动效果
        TMUtility.VibrateMatch();
        var theBlock = ChoosedBlockReality;
        List<BlockGrid> blockGrides = null;
        if (BlockToGrides.ContainsKey(theBlock)) blockGrides = BlockToGrides[theBlock];

        var isGameEndless = CurGameType == EnumBlockGameType.BlockGame_Endless;

        var theBlockGridNum = blockGrides?.Count ?? 0;
        EnumGridColor theColor = ChoosedBlockColor;

        blockTipInfos.Clear();
        bool playTip = true;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            var guideSys = GameGlobal.GetMod<GuideSys>();
            int theStep = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
            if (theStep < 2)
            {
                playTip = false;
            }
        }
        var theMatch = 0;
        var theBaseScore = tempMatchIndex.Count;
        var theGainScore = theBaseScore;
        var isUsingBomb = IsUsingBomb;
        bool canplay_Unbelievable = true;
        achiveGemList.Clear();
        HashSet<Grid> damagedAdvancedBlocks = new HashSet<Grid>();
        if (isUsingBomb)
        {
            Vector3 thePos = curGridContainer.InverseTransformPoint(theBlock.position);
            EventBus.Dispatch(new EventPlayBombExplode(thePos));

            foreach (var index in tempMatchIndex)
            {
                var theGridInfo = grids[index];
                if (theGridInfo.ShowState == EnumGridState.EGS_Show)
                {
                    var theRow = theGridInfo.yIndex;
                    var theCol = theGridInfo.xIndex;

                    var reallyClear = ProcessGridElimination(theGridInfo, index, theRow, theCol, damagedAdvancedBlocks, achiveGemList, 0);

                    if (reallyClear)
                    {
                        theGridInfo.ShowState = EnumGridState.EGS_Hide;
                    }
                }
            }
            tempMatchIndex.Clear();
        }
        else
        {
            RefreshMatchRowCol();           
            int pos = 0;
            foreach (var index in tempMatchIndex)
            {
                var theGridInfo = grids[index];
                theGridInfo.ShowState = EnumGridState.EGS_Show;
                var theRow = theGridInfo.yIndex;
                var theCol = theGridInfo.xIndex;
                //if (!tempMatchRow.Contains(theRow) && !tempMatchCol.Contains(theCol))
                {
                    HandleBoardLayout(index, true);//放置的块不包含叶子、木箱、鸟窝
                    BlockPlayManager.Instance.AddBlock(theRow, theCol);
                }

                if (pos < theBlockGridNum)
                {
                    theGridInfo.GemType = blockGrides[pos].GemType;
                }
                if (!tempMatchRow.Contains(theRow) && !tempMatchCol.Contains(theCol))
                {
                    theGridInfo.ShowPutDownEffect();
                }
                pos++;
            }

            var curTime = UnityEngine.Time.time;
            theMatch = tempMatchRow.Count + tempMatchCol.Count;
            if (theMatch > 0)
            {
                // 有消除时重置连续无消除计数器
                consecutiveNonMatchCount = 0;
                
                matchClearSum += theMatch;
                matchClearTime += 1;
                if (theMatch > maxMatchClearCount) maxMatchClearCount = theMatch;

                var theConfigCount = 0;
                List<int> theConfigList = null;
                Table_InGame_Score theCfg = InGameConfigManager.GetInGameScoreCfgByMatchNum(theMatch);
                if (theCfg != null)
                {
                    theConfigList = theCfg.ComboScore;
                    theConfigCount = theConfigList?.Count ?? 0;
                }

                if(curBlockProductIsNative)
                {
                    comboNum += theMatch;
                }
                else
                {
                    comboNum ++;
                }
                bool isMaxCombo = comboNum - 2 >= theConfigCount;

                if (comboNum > 1) curComboSum += 1;
                if (comboNum > maxComboNumber)
                {
                    maxComboNumber = comboNum;
                    if (isMaxCombo) maxComboNumber = theConfigCount;
                    maxComboNumber -= 1;
                }


                blockTipInfos.Add(new BlockTipInfo(EnumBlockTip.EBT_Combo, comboNum, isMaxCombo ? 1 : 0, 0));
                //PlayComboEffect(comboNum, comboNum - 2 >= theConfigCount);

                tempRowPos.Clear();
                tempColPos.Clear();


                var needCheckPatternFinished = levelLayoutGridList.Count > 0;

                //消除------------
                var tempPos = 0;
                bool bHaveRow = tempMatchRow.Count > 0;
                foreach (var theRow in tempMatchRow)
                {
                    var processedRoots = new HashSet<Grid>();
                    var theBegain = theRow * boardWidth;
                    tempPos = 0;
                    while (tempPos < boardWidth)
                    {
                        var curIndex = theBegain + tempPos;
                        var theGridInfo = grids[curIndex];

                        ProcessGridElimination(theGridInfo, curIndex, theRow, tempPos, processedRoots, achiveGemList, 1);

                        tempPos++;
                        if (tempPos == 5)
                        {
                            var thePos = theGridInfo.RectTf.localPosition;
                            thePos.x -= Grid.HalfGridWidth;
                            tempRowPos.Add(thePos);
                        }
                    }
                    CLog.Warning($"HandlePutDownBlock--消除 {theRow} 行");
                }
                tempMatchRow.Clear();

                foreach (var theCol in tempMatchCol)
                {
                    var processedRoots = new HashSet<Grid>();
                    tempPos = 0;
                    while (tempPos < boardHeight)
                    {
                        var curIndex = theCol + tempPos * boardWidth;
                        var theGridInfo = grids[curIndex];

                        // 如果该格子已经在行消除中被处理过，跳过
                        if (bHaveRow && theGridInfo.ShowState == EnumGridState.EGS_Hide)
                        {
                            tempPos++;
                            if (tempPos == 5)
                            {
                                var thePos = theGridInfo.RectTf.localPosition;
                                thePos.y += Grid.HalfGridHeight;
                                tempColPos.Add(thePos);
                            }
                            continue;
                        }

                        ProcessGridElimination(theGridInfo, curIndex, tempPos, theCol, processedRoots, achiveGemList, 2);

                        tempPos++;
                        if (tempPos == 5)
                        {
                            var thePos = theGridInfo.RectTf.localPosition;
                            thePos.y += Grid.HalfGridHeight;
                            tempColPos.Add(thePos);
                        }
                    }
                    CLog.Warning($"HandlePutDownBlock--消除 {theCol} 列");
                }
                tempMatchCol.Clear();

                {
                    if (isGameEndless)
                    {
                        if (curBlockProductIsNative)
                        {
                            // 新的分数计算方式
                            var comboForCalculation = comboNum > 0 ? comboNum : 0;
                            var calculatedScore = CalculateValue(theMatch, comboForCalculation);
                            theGainScore += calculatedScore;

                            CLog.Info($"新分数计算: 消除行数={theMatch}, 连击数={comboNum}, 计算分数={calculatedScore}");
                        }
                        else
                        {
                            //老的分数计算方式
                            if (theCfg != null && theConfigList != null)
                            {
                                theGainScore += theCfg.BaseScore;
                                var theCount = theConfigList?.Count ?? 0;
                                var theComboIndex = comboNum - 2;
                                if (theComboIndex >= theCount)
                                {
                                    theComboIndex = theCount - 1;
                                }

                                if (isGameEndless)
                                {
                                    if (theComboIndex >= 0)
                                    {
                                        theGainScore += theConfigList[theComboIndex];
                                    }
                                }

                                var theTipType = (EnumBlockTip)theCfg.ArtisticFont;
                                if (playTip) blockTipInfos.Add(new BlockTipInfo(theTipType, (int)theColor, 0, 0));
                            }
                        }
                    }
                    else
                    {
                        // 关卡模式保留原有逻辑
                        if (theCfg != null && theConfigList != null)
                        {
                            theGainScore += theCfg.BaseScore;
                        }
                    }

                    // 原有逻辑（已注释）
                    /*
                    if (theCfg != null && theConfigList != null)
                    {
                        theGainScore += theCfg.BaseScore;
                        var theCount = theConfigList?.Count ?? 0;
                        var theComboIndex = comboNum - 2;
                        if (theComboIndex >= theCount)
                        {
                            theComboIndex = theCount - 1;
                        }

                        if (isGameEndless)
                        {
                            if (theComboIndex >= 0)
                            {
                                theGainScore += theConfigList[theComboIndex];
                            }
                        }

                        var theTipType = (EnumBlockTip)theCfg.ArtisticFont;
                        if (playTip) blockTipInfos.Add(new BlockTipInfo(theTipType, (int)theColor, 0, 0));
                    }
                    */

                    if (theCfg != null)
                    {
                        var theTipType = (EnumBlockTip)theCfg.ArtisticFont;
                        if (playTip) blockTipInfos.Add(new BlockTipInfo(theTipType, (int)theColor, 0, 0));
                    }

                    if (needCheckPatternFinished)
                    {
                        if (levelLayoutGridList.Count <= 0)
                        {
                            Table_InGame_Score tempCfg = InGameConfigManager.GetInGameScoreCfgByMatchType(2);
                            theGainScore += tempCfg.BaseScore;
                            if (playTip &&  CurGameTargetType!= EnumTargetType.ETT_Gem)
                            {
                                blockTipInfos.Add(new BlockTipInfo(EnumBlockTip.EBT_PatternFinished, (int)theColor, 0,
                                    0));
                                canplay_Unbelievable = isGameEndless;
                            }
                        }
                    }
                }

                ShowMatchClearEffect(theColor);
            }
            else
            {
                // 无消除时增加连续无消除计数器
                consecutiveNonMatchCount++;
                
                // 如果连续3次无消除，清空连击数
                if (consecutiveNonMatchCount >= 3 && putdownCount >= 3)
                {
                    if (comboNum > 0)
                    {
                        CLog.Info($"HandlePutDownBlock--Clear comboNum due to 3 consecutive non-match placements, consecutiveNonMatchCount:{consecutiveNonMatchCount}");
                        comboNum = 0;
                        consecutiveNonMatchCount = 0; // 重置计数器
                    }
                }
            }

            if (curProductType == EnumBlockProductType.EBPT_Hard && putdownCount < 3)
            {
                var theLikeType = -1;
                var theCheckBlock = curBlockIsNewOrder ? genList[1] : genList[0];
                var theWillPutBlock = 0;
                if (putdownCount == 1)
                {
                    theWillPutBlock = dragBlockNodeIndex == 1 ? genList[2] : genList[1];
                    if (curBlockIsNewOrder)
                    {
                        theWillPutBlock = dragBlockNodeIndex == 2 ? genList[0] : genList[2];
                    }
                }

                var theCheck = BlockPlayManager.Instance.CheckForLike(theCheckBlock, theWillPutBlock);
                if (theCheck) theLikeType = putdownCount - 1;

                if (theLikeType >= 0)
                {
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_27");
                    foreach (var index in tempMatchIndex)
                    {
                        var theGridInfo = grids[index];
                        theGridInfo.ShowPutDownForLike(theLikeType);
                    }
                }
            }
            else
            {
                //abtest for like new
                var abTestLike = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestLike);
                if (abTestLike == EABTestGroup.Group2)
                {
                    if (BlockPlayManager.Instance.CheckForLikeNew())
                    {
                        GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_27");
                        foreach (var index in tempMatchIndex)
                        {
                            var theGridInfo = grids[index];
                            theGridInfo.ShowPutDownForLike(putdownCount - 1);
                        }
                    }
                }
            }

            // abtest for hard extra score
            // ab测废弃
            // var abTestHardExtraScore = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestHardExtraScore);
            // if (abTestHardExtraScore == EABTestGroup.Group2)
            // {
            //     if (curProductType == EnumBlockProductType.EBPT_Hard && putdownCount == 3)
            //     {
            //         theGainScore += 500;
            //     }
            // }

            tempMatchIndex.Clear();
        }

        //CLog.Error($"HandlePutDownBlock--{BlockPlayManager.Instance.EdgeCountAdvance}");
        var theBlockCount = BlockPlayManager.Instance.GetBlockCount();
        if (theBlockCount <= 0)
        {//清屏
            var theBoard = BlockPlayManager.Instance.GetBoardInfo();
            lastClearScreenRound = genRoundIndex;
            clearScreenTime += 1;
            //TODO PatternFinished和Unbelievable如果同时触发，则只显示PatternFinished，不显示Unbelievable
            
            if (playTip && canplay_Unbelievable && CurGameTargetType!= EnumTargetType.ETT_Gem) blockTipInfos.Add(new BlockTipInfo(EnumBlockTip.EBT_Unbelievable, (int)theColor, 0, 0));

            if (isGameEndless)
            {
                Table_InGame_Score theCfg = InGameConfigManager.GetInGameScoreCfgByMatchType(3);
                if (theCfg != null) theGainScore += theCfg.BaseScore;
            }
        }
        
        blockTipInfos.Add(new BlockTipInfo(EnumBlockTip.EBT_ScoreNum, theGainScore, theMatch, IsUsingBomb ? 1 : 0));
        EventBus.Dispatch(new EventBlockPutDownInfo(comboNum, theMatch,lastDragIndex,theGainScore));

        if (theMatch > 1)
        {
            lastMClearRound = genRoundIndex;
        }

        if (achiveGemList.Count > 0)
        {//发事件
            EventBus.Dispatch(new EventGameGemAchive(achiveGemList));
        }

        //PlayBlockScore(theGainScore, theMatch);
        HandleRewardScore(theGainScore);
        MoveHomeBlock(true, isUsingBomb);
        HandleGameOver();

        if (PlayingClearScreen)
        {
            playTip = false;
        }
        if (playTip) PlayBlockTip();
        if (theMatch > 0) CLog.Warning($"HandlePutDownBlock---RewardScore[{theBaseScore},{theGainScore - theBaseScore}], comboNum:{comboNum},theMatch:{theMatch}");
    }

    public void HandleRewardScore(int score)
    {
        if (score < 0 || CurGameType == EnumBlockGameType.BlockGame_None) return;
        int preScore = curScore;
        curScore += score;
        //发事件
        EventBus.Dispatch(new EventGameScoreChange(preScore, score));        
    }

    // 定义 multiplier 映射数组，下标从 1 开始，索引 0 保留不用
    // multiplier[x] 就是对应的 multiplier 值
    private readonly int[] multipliers = { 0, 1, 1, 2, 3, 4, 5 };
    /// <summary>
    /// 计算分数的新函数
    /// </summary>
    /// <param name="x">当前消除的行数</param>
    /// <param name="y">当前连击数</param>
    /// <returns>计算得出的分数</returns>
    public int CalculateValue(int x, int y)
    {
        if (x < 1 || x >= multipliers.Length)
            throw new ArgumentOutOfRangeException(nameof(x), "x 超出支持的范围（1~6）");

        int multiplier = multipliers[x];
        return 10 * x * multiplier * (y + 1);
    }

    #endregion

    #region 工具类API

    Sequence curMoveHomeSequence;

    public void MoveHomeBlockImmediately(bool endDrag = false)
    {
        HandleMatchState(true);
        lastDragIndex = -1;
        lastDragMatchPos = InvaildMatchPos;
        lastDragMatchPosLU = InvaildPos;
        curBlockBestMatchPos.Clear();
        curHaveClearMatchPos.Clear();
        tempMatchRow.Clear();
        tempMatchCol.Clear();
        tempMatchIndex.Clear();
        var theBlockContainer = ChoosedDragBlockContainer;
        if (theBlockContainer != null)
        {//MoveHome

            if(curPdSequence != null)
            {
                CLog.Warning("MoveHomeBlockImmediately--11");
                curPdSequence.Kill();
                curPdSequence = null;
            }
            theBlockContainer.DOKill();


            if (endDrag)
            {               
                firstDragGo = null;
                _baseEventData = null;
                isDraging = false;
            }

            var theIndex = dragBlockNodeIndex;
            CLog.Warning($"MoveHomeBlockImmediately--{theBlockContainer.name}--{theIndex}-{blockContainerScale}");
            if (theIndex >= 0 && theIndex < blockContainers.Count)
            {
                var thePos = blockContainerPos[theIndex];
                if (curMoveHomeSequence != null)
                {
                    curMoveHomeSequence.Kill();
                    curMoveHomeSequence = null;
                }
                theBlockContainer.localScale = blockContainerScale;
                theBlockContainer.position = thePos;
                theBlockContainer.SetSiblingIndex(theIndex);
            }

            ChoosedDragBlockContainer = null;
        }       
    }

    void MoveHomeBlock(bool putdownBlock = false, bool fromBomb = false)
    {
        HandleMatchState(true);
        lastDragIndex = -1;
        lastDragMatchPos = InvaildMatchPos;
        lastDragMatchPosLU = InvaildPos;
        curBlockBestMatchPos.Clear();
        curHaveClearMatchPos.Clear();
        tempMatchRow.Clear();
        tempMatchCol.Clear();
        tempMatchIndex.Clear();

        var theBlockContainer = ChoosedDragBlockContainer;
        if (theBlockContainer != null)
        {//MoveHome
            var thePos = ChoosedBlockPos;
            var theIndex = dragBlockNodeIndex;
            if (putdownBlock)
            {
                if (false == fromBomb)
                {
                    var theRealBlock = blocks[theIndex];
                    if (theRealBlock)
                    {
                        theRealBlock.gameObject.SetActive(false);
                        theRealBlock.SetParent(curBlockPool);
                        blocks[theIndex] = null;
                    }
                }

                theBlockContainer.localScale = blockContainerScale;
                theBlockContainer.position = thePos;
                theBlockContainer.SetSiblingIndex(theIndex);
            }
            else
            {
                var theTargetScale = blockContainerScale.x;
                if (IsUsingBomb && theIndex == 1) theTargetScale = 1f;

                curMoveHomeSequence?.Complete();
                curMoveHomeSequence = TMUtility.SequenceOpen(theBlockContainer, theTargetScale, thePos, 0.2f, () =>
                {
                    ChoosedDragBlockContainer = null;
                    curMoveHomeSequence = null;
                    theBlockContainer?.SetSiblingIndex(theIndex);
                });
            }

        }
        else
        {
            var theIndex = GetDragAreaIndex(firstEventGo);
            if (theIndex >= 0 && theIndex < blockContainers.Count)
            {
                theBlockContainer = blockContainers[theIndex];
                curPdSequence?.Kill();
                curPdSequence = null;
                var thePos = blockContainerPos[theIndex];
                var theTargetScale = blockContainerScale;
                if (IsUsingBomb && theIndex == 1) theTargetScale = Vector2.one;
                theBlockContainer.localScale = theTargetScale;
                theBlockContainer.position = thePos;
                theBlockContainer.SetSiblingIndex(theIndex);
            }
        }
    }

    EnumBlockProductType GetProductTypeByNativeType(BlockMatchCoreNative.StrategyEnum eType)
    {
        var theRes = EnumBlockProductType.EBPT_Guarantee;
        switch (eType)
        {
            case BlockMatchCoreNative.StrategyEnum.StrategyRandom:
                theRes = EnumBlockProductType.EBPT_Normal;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyBuild:
                theRes = EnumBlockProductType.EBPT_ComplexityUp;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyClear:
                theRes = EnumBlockProductType.EBPT_ComplexityDown;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyClearMulti:
                theRes = EnumBlockProductType.EBPT_MultiClear;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyClearScreen:
                theRes = EnumBlockProductType.EBPT_ClearScreen;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyHard:
                theRes = EnumBlockProductType.EBPT_Hard;
                break;
            case BlockMatchCoreNative.StrategyEnum.StrategyDeath:
                break;
            default:
                break;
        }

        return theRes;
    }

    BlockMatchCoreNative.StrategyEnum GetNativeTypeByProductType(EnumBlockProductType eType)
    {
        var theRes = BlockMatchCoreNative.StrategyEnum.StrategyRandom;
        switch (eType)
        {                      
            case EnumBlockProductType.EBPT_ComplexityUp:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyBuild;
                break;
            case EnumBlockProductType.EBPT_ComplexityDown:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyClear;
                break;
            case EnumBlockProductType.EBPT_Normal:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyRandom;
                break;
            case EnumBlockProductType.EBPT_Hard:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyHard;
                break;
            case EnumBlockProductType.EBPT_MultiClear:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyClearMulti;
                break;
            case EnumBlockProductType.EBPT_ClearScreen:
                theRes = BlockMatchCoreNative.StrategyEnum.StrategyClearScreen;
                break;           
            default:
                break;
        }

        return theRes;
    }


    int GetCurFeelType()
    {
        var theFeelType = 1;
        var theIndex = experienceFeelList.FindIndex(ele => ele.ScoreRange.Key <= curScore && curScore < ele.ScoreRange.Value);
        if (theIndex >= 0) theFeelType = experienceFeelList[theIndex].feelType;
        return theFeelType;
    }

    EnumBlockProductType GetEndlessProductType()
    {
        var theSys = GameGlobal.GetMod<ActionGroupSystem>();
        EnumBlockProductType pType = EnumBlockProductType.EBPT_Normal;

        var theFeelType = GetCurFeelType();
        var theComplex = BlockPlayManager.Instance.Complexity;
        var complextiyGroup = 100;
        var mapping = theSys.GetGroupMapping();
        if (null != mapping) complextiyGroup = mapping.ComplextiyGroup;
        //如果是难题ABTest直接将分组设置为300
        if (abTestEndlessDifficultTiming) complextiyGroup = 300;
        
        var theList = InGameConfigManager.GetComplexInfoByType(theFeelType, complextiyGroup);
        Table_InGame_Complextiy theInfo = null;
        if (theList != null && theList.Count > 0)
        {
            theInfo = theList.Find(
                ele => theComplex >= ele.ComplextiyRange[0] && theComplex < ele.ComplextiyRange[1]
                );
        }

        var theRandom = 0;
        if (theInfo != null)
        {
            var theWeight1 = theInfo.ComplexityIncreaseWeight;
            var theWeight2 = theInfo.ComplexityDecreaseWeight;
            var theWeight3 = theInfo.RandomWeight;
            var theWeight4 = theInfo.DifficultyWeight;
            
            // 爽关模式下且分数小于8000时不触发难题
            if (curABtestEndlessSimply && curScore < 8000)
            {
                theWeight4 = 0;
            }

            var theScoreCount = gameStorage.ClosestFivePlayScores.Count;
            if(theScoreCount == 5 && gameStorage.CurFirstScore >= 3000)
            {
                var theScore = gameStorage.ClosestFivePlayScores.Sum();
                theScore = theScore / theScoreCount;
                var theRadio = theScore * 100 / gameStorage.CurFirstScore;
                CLog.Info($"AlgorithmCorrection--AvgScore:{theScore}, theRadio:{theRadio}");
                var theConfig = InGameConfigManager.GetAlgorithmCorrectionConfig(theRadio);
                if (theConfig != null)
                {
                    var theCorrect = 0;
                    var theDiffCorrect = Math.Abs(theConfig.DifficultyWeight);
                    if (theDiffCorrect > 0 && theWeight4 > 0)
                    {
                        theCorrect = Math.Min(theWeight4, theDiffCorrect);
                        theWeight4 -= theCorrect;
                    }
                    else
                    {
                        var theRandomCorrect = Math.Abs(theConfig.RandomWeight);
                        if (theRandomCorrect > 0 && theWeight3 > 0)
                        {
                            theCorrect = Math.Min(theWeight3, theRandomCorrect);
                            theWeight3 -= theCorrect;
                        }
                    }

                    if (theCorrect > 0) theWeight2 += theCorrect;
                    CLog.Info($"AlgorithmCorrection--theCorrectWeight:{theCorrect},[构筑:{theWeight1},消除:{theWeight2},随机:{theWeight3},难题:{theWeight4}]");
                }
            }

            var theSum = theWeight1 + theWeight2 + theWeight3 + theWeight4;
            theRandom = UnityEngine.Random.Range(1, theSum + 1);

            var thePreValue = 0;
            var theCurValue = theWeight1;
            if (theRandom > thePreValue && theRandom <= theCurValue)
            {
                pType = EnumBlockProductType.EBPT_ComplexityUp;
            }
            else
            {
                thePreValue = theCurValue;
                theCurValue += theWeight2;
                if (theRandom > thePreValue && theRandom <= theCurValue)
                {
                    pType = EnumBlockProductType.EBPT_ComplexityDown;
                }
                else
                {
                    thePreValue = theCurValue;
                    theCurValue += theWeight3;
                    if (theRandom > thePreValue && theRandom <= theCurValue)
                    {
                        pType = EnumBlockProductType.EBPT_Normal;
                    }
                    else
                    {
                        thePreValue = theCurValue;
                        theCurValue += theWeight4;
                        if (theRandom > thePreValue && theRandom <= theCurValue)
                        {
                            pType = EnumBlockProductType.EBPT_Hard;
                        }
                    }
                }
            }
        }
        CLog.Info($"HandleBlockProduct, theRandom:{theRandom}, pType:{pType}, theFeelType:{theFeelType}, complextiyGroup:{complextiyGroup}, ComplextiyId:{theInfo?.Id}, theComplex:{theComplex}");
        return pType;
    }

    KeyValuePair<int, int> GenerateGemForBlock()
    {
        KeyValuePair<int, int> theRes = new KeyValuePair<int, int>(-1, 0);
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage
            && HasTargetType(EnumTargetType.ETT_Gem) && theView != null)
        {
            theRes = theView.GenerateGem();
        }
        return theRes;
    }

    void OnHardProduct()
    {
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
        if (theView != null)
        {
            theView.OnHardProduct();
        }
    }
    private bool isFirstTimeAfterGuide = false;
    public void HandleBlockProduct()
    {
        //引导期间阻止块生成（关卡模式和无尽模式统一处理）
        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        // 引导期间直接返回，不生成新块
        if (step < 2)
        {
            isFirstTimeAfterGuide = true;
            return;
        }

        // 引导结束后第一次正常出块时，清空引导期间累积的分数和连击数
        if (CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            if (isFirstTimeAfterGuide)
            {
                // 清空引导期间累积的分数和连击数
                int preScore = curScore;
                curScore = 0;
                comboNum = 0;
                
                // 发送分数清零事件，让UI更新显示
                EventBus.Dispatch(new EventGameScoreChange(preScore, -preScore));
                // 发送连击清零事件，让UI更新显示
                EventBus.Dispatch(new EventBlockPutDownInfo(0, 0, -1, 0));

                isFirstTimeAfterGuide = false;
                CLog.Info($"引导结束，清空累积分数 {preScore} 和连击数");
            }
        }

        var bCanProduct = putdownCount == genBlockCount || blocks.FindIndex(ele => ele != null) < 0;
        if (!bCanProduct)
            return;

        List<int> weights = null;
        var theParam = GenerateParam.defaultP;

        EnumBlockProductType pType = EnumBlockProductType.EBPT_Normal;
        if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            bool bNeedProductType = true;
            if (genRoundIndex == 0 && curLevelConfig != null)
            {
                var firstBlocks = curLevelConfig.FirstBlockId;
                if (firstBlocks != null && firstBlocks.Count == genBlockCount)
                {
                    foreach (var blockId in firstBlocks)
                    {
                        if(origainBlockConfig.Contains(blockId) == false)
                        {
                            genList.Clear();
                            break;
                        }
                        else
                        {
                            genList.Add(blockId);
                        }
                    }

                    if (genList.Count == genBlockCount) bNeedProductType = false;
                }
            }

            if (bNeedProductType)
            {
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                if (theView != null) theParam = theView.GetLevelGameBlockProductType(ref weights);
            }
            else
            {
                theParam = GenerateParam.invaildP;
                theParam.pType = EnumBlockProductType.EBPT_NoNeed;
            }
            pType = theParam.pType;
        }
        else
        {
            pType = GetEndlessProductType();
            theParam = GenerateParam.GetNormalParam(pType);
        }

        if (pType == EnumBlockProductType.EBPT_ComplexityUp)
        {
            HandleBuildUpType(weights);
        }
        else if(pType == EnumBlockProductType.EBPT_ComplexityDown)
        {
            HandleClearBlockType(weights);
        }

        GenerateBlockGo(theParam);
    }


    public void SetPutdownCount(int count)
    {
        putdownCount = count;
    }

    public bool IsBlockGenerating => isGenerating;

    public List<RectTransform> DragAreas => dragAreas;
    public List<RectTransform> BlockContainers => blockContainers;

    public List<EnumGridColor> BlockColors => blockColors;

    public List<RectTransform> Blocks => blocks;
    public List<Vector3> BlockContainerPos => blockContainerPos;

    public bool InInfintyEnergyState { get; private set; } //进关时是否是无限体力状态

    public EnumBlockGameType CurGameType { get; private set; } 

    public EnumTargetType CurGameTargetType { get; private set; } //关卡模式下游戏目标类型

    /// <summary>
    /// 检查当前关卡是否包含指定的目标类型（支持多目标）
    /// </summary>
    /// <param name="targetType">要检查的目标类型</param>
    /// <returns>是否包含该目标类型</returns>
    public bool HasTargetType(EnumTargetType targetType)
    {
        // 从UI获取当前关卡的激活目标类型
        var gameView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
        if (gameView != null)
        {
            return gameView.HasActiveTargetType(targetType);
        }
        
        // 如果UI不存在，回退到原来的单一目标判断
        return CurGameTargetType == targetType;
    }

    public int GenBlockCount => genBlockCount;

    private bool isGenerating = false;

    private int theMClearBlockId = 0;
    List<BlockPutInfo> theListForClearScreen = new List<BlockPutInfo>();

    int curHaveBlockCount = 0;

    public int GenerateBlockCount => (genBlockCount - curHaveBlockCount);

    public int MaxMatchClearCount => maxMatchClearCount;

    public int MatchClearSum => matchClearSum;

    public int MatchClearTime => matchClearTime;

    public int GenRoundIndex => genRoundIndex;

    public int ClearScreenTime => clearScreenTime;    //本局清屏次数
    public int MaxComboNumber => maxComboNumber;     //本局最大连击数
    public int CurComboSum => curComboSum;        //本局累计连击次数

    bool IsUsingBomb => CurGameType == EnumBlockGameType.BlockGame_Stage && UIView_Game.CurUsingItem == EItemType.Bomb;

    public void GetClearScreenBlock(ref int firstBlock, ref int secondBlock)
    {
        var clearScreenCount = theListForClearScreen.Count;
        if (clearScreenCount > 0)
        {
            firstBlock = theListForClearScreen[0].blockConfig;
            if (clearScreenCount > 1) secondBlock = theListForClearScreen[1].blockConfig;
        }
    }

    public Transform GetFirstGridTs(RectTransform theBlock)
    {
        Transform theRes = null;
        var theChildCount = theBlock.childCount;
        if (theChildCount > 0)
        {
            theRes = theBlock.GetChild(0);
        }
        else
        {
            CLog.Exception("GetFirstGridTs 获取block第一个子节点失败");
        }
        return theRes;
    }

    public List<BlockGrid> GetBlockGridList(RectTransform theBlock)
    {
        List<BlockGrid> blockGrides = null;
        var isGet = BlockToGrides.TryGetValue(theBlock, out blockGrides);
        if (!isGet)
        {
            var theChildCount = theBlock.childCount;
            blockGrides = new List<BlockGrid>();
            for (var i = 0; i < theChildCount; i++)
            {
                var theChild = theBlock.GetChild(i).GetComponent<RectTransform>();
                var theImage = theChild.GetComponent<Image>();
                if (theImage == null || theImage.enabled == false) continue;
                BlockGrid theBG = new BlockGrid();
                theBG.RectTf = theChild;
                theBG.pos = i;
                blockGrides.Add(theBG);
            }
            BlockToGrides[theBlock] = blockGrides;
        }

        return blockGrides;
    }


    public string GetBlockName(int blockConfig)
    {
        var theKey = blockConfig / 100;
        var theValue = blockConfig % 100;
        return $"{theKey}Block{theValue}";
    }

    public BlockMatchMatrix GetBlockMatchMatrix(int kvConfig)
    {
        var theName = GetBlockName(kvConfig);
        if(blockNameToMatchMatrix.ContainsKey(theName) == false)
            return default(BlockMatchMatrix);
        return blockNameToMatchMatrix[theName];
    }

    List<BlockGrid> tempGemList = new List<BlockGrid>();
    public void HandleGemForRotateBlock(RectTransform origain, RectTransform newRect, int name1, int name2, EnumGridColor blockColor)
    {
        if(origain == null || newRect == null) return;
        if (!BlockToGrides.ContainsKey(origain) || !BlockToGrides.ContainsKey(newRect)) return;
        if (origain.childCount != newRect.childCount) return;

        var theBName1 = GetBlockName(name1);
        var theBName2 = GetBlockName(name2);
        if (!blockNameToMatchMatrix.ContainsKey(theBName1) || !blockNameToMatchMatrix.ContainsKey(theBName2)) return;
        

        var blockInfo1 = blockNameToMatchMatrix[theBName1];
        var blockInfo2 = blockNameToMatchMatrix[theBName2];
        var theList1 = BlockToGrides[origain];
        var theList2 = BlockToGrides[newRect];
        if (theList1.Count != theList2.Count) return;

        tempGemList.Clear();
        foreach (var item in theList1)
        {
            var tempType = item.GemType;
            if (tempType > GemType.None && tempType < GemType.GemEnd)
            {
                tempGemList.Add(item);
            }
        }

        foreach (var item in theList2)
        {
            var theY = item.pos / blockInfo2.colNum;
            var theX = item.pos % blockInfo2.colNum;
            var theFind = tempGemList.Find(ele =>
            {
                var tY = ele.pos / blockInfo1.colNum;
                var tX = ele.pos % blockInfo1.colNum;
                return theY == tX && theX == blockInfo1.rowNum - tY - 1;
            });
            if (theFind != null)
            {
                item.GemType = theFind.GemType;
            }
            else
            {
                item.GemType = GemType.None;
                item.ColorState = blockColor;
            }
        }

    }

    /// <summary>
    /// 统计Block横轴的边
    /// </summary>
    /// <param name="edgePos"></param>
    /// <param name="row1Value"></param>
    /// <param name="row2Value"></param>
    /// <param name="blockColNum"></param>
    /// <param name="edgeContainer"></param>
    void RefreshEdgeForRow(int edgePos, int row1Value, int row2Value, int blockColNum, ref List<Edge> edgeContainer)
    {
        if (edgeContainer == null) return;

        var tempValue = (byte)(row1Value ^ row2Value);
        if (tempValue > 0)
        {
            int begin = -1;
            EnumEdgeOrientation theOrientation = EnumEdgeOrientation.EDO_None;
            for (var i = 0; i < blockColNum; i++)
            {
                var theValue = tempValue & (1 << i);
                if (theValue != 0)
                {
                    if (i > 0)
                    {
                        var curValue = (row1Value >> i) & 1;
                        var preHave = (row1Value >> (i - 1)) & 1;
                        if (curValue != preHave)
                        {
                            if (begin >= 0)
                            {
                                var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = i, Y2 = edgePos, orientation = theOrientation };                              
                                edgeContainer.Add(theEdge);
                                begin = -1;
                            }
                        }
                    }

                    if (begin < 0)
                    {
                        var curValue = (row1Value >> i) & 1;
                        theOrientation = curValue == 1 ? EnumEdgeOrientation.EDO_Bottom : EnumEdgeOrientation.EDO_Top;
                        begin = i;
                    }
                }
                else
                {
                    if (begin >= 0)
                    {
                        var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = i, Y2 = edgePos, orientation = theOrientation };
                        edgeContainer.Add(theEdge);
                    }
                    begin = -1;
                }
            }

            if (begin >= 0)
            {
                var theEdge = new Edge { X1 = begin, Y1 = edgePos, X2 = blockColNum, Y2 = edgePos, orientation = theOrientation };
                edgeContainer.Add(theEdge);
            }
        }
    }

    /// <summary>
    /// 统计Block纵轴的边
    /// </summary>
    /// <param name="edgePos"></param>
    /// <param name="col1Value"></param>
    /// <param name="col2Value"></param>
    /// <param name="blockRowNum"></param>
    /// <param name="edgeContainer"></param>
    void RefreshEdgeForCol(int edgePos, int col1Value, int col2Value, int blockRowNum, ref List<Edge> edgeContainer)
    {
        if (edgeContainer == null) return;

        var tempValue = (byte)(col1Value ^ col2Value);
        if (tempValue > 0)
        {
            int begin = -1;
            EnumEdgeOrientation theOrientation = EnumEdgeOrientation.EDO_None;
            for (var i = 0; i < blockRowNum; i++)
            {
                var theValue = tempValue & (1 << i);
                if (theValue != 0)
                {
                    if (i > 0)
                    {
                        var curValue = (col1Value >> i) & 1;
                        var preHave = (col1Value >> (i - 1)) & 1;
                        if (curValue != preHave)
                        {
                            if (begin >= 0)
                            {
                                var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = i, orientation = theOrientation };                                
                                edgeContainer.Add(theEdge);
                                begin = -1;
                            }
                        }
                    }
                    if (begin < 0)
                    {
                        var curValue = (col1Value >> i) & 1;
                        theOrientation = curValue == 1 ? EnumEdgeOrientation.EDO_Right : EnumEdgeOrientation.EDO_Left;
                        begin = i;
                    }
                }
                else
                {
                    if (begin >= 0)
                    {
                        var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = i, orientation = theOrientation };                        
                        edgeContainer.Add(theEdge);
                    }
                    begin = -1;
                }
            }

            if (begin >= 0)
            {
                var theEdge = new Edge { X1 = edgePos, Y1 = begin, X2 = edgePos, Y2 = blockRowNum, orientation = theOrientation };                
                edgeContainer.Add(theEdge);
            }
        }
    }

    public List<ulong> tempBlockRowLayout = new List<ulong>();
    public RectTransform CreateOrGetBlock(string theAssetName, Transform parent = null, EnumGridColor blockColor = EnumGridColor.EGC_None, GenBlockInfo genInfo = default)
    {
        var theBlockPool = curBlockPool;
        var theBlock = theBlockPool.Find(theAssetName)?.GetComponent<RectTransform>();
        List<BlockGrid> blockGrides = null;
        if (theBlock != null)
        {
            theBlock.gameObject.SetActive(false);
            if (BlockToGrides.ContainsKey(theBlock)) blockGrides = BlockToGrides[theBlock];
        }

        if (theBlock == null)
        {
            var theGo = GameGlobal.GetMgr<ResMgr>().GetGameObject(theAssetName).GetInstance(GameGlobal.DontDestoryRoot.transform);
            if (theGo == null)
            {
                CLog.Exception($"创建Block[{theAssetName}]出错, 预设资源[{theAssetName}] 生成失败 ");
                return null;
            }
            else
            {
                theGo.name = theAssetName;
                theBlock = theGo.GetComponent<RectTransform>();
            }
        }

        List<int> emptyList = new List<int>();
        bool haveEmpty = false;
        if (blockGrides == null)
        {
            var theChildCount = theBlock.childCount;
            blockGrides = new List<BlockGrid>();
            for (var index = 0; index < theChildCount; index++)
            {
                var theChild = theBlock.GetChild(index).GetComponent<RectTransform>();
                var theImage = theChild.GetComponent<Image>();
                if (theImage == null || theImage.enabled == false)
                {
                    haveEmpty = true;
                    emptyList.Add(index);
                    continue;
                }

                BlockGrid theBg = new BlockGrid();
                theBg.RectTf = theChild;
                theBg.pos = index;
                blockGrides.Add(theBg);
            }
            BlockToGrides[theBlock] = blockGrides;
        }

        if (!blockNameToMatchMatrix.ContainsKey(theAssetName))
        {
            var theGridLg = theBlock.GetComponent<GridLayoutGroup>();
            if (theGridLg != null && theGridLg.startCorner == GridLayoutGroup.Corner.UpperLeft)
            {
                var theChildCount = theBlock.childCount;
                var theConstraint = theGridLg.constraint;
                var theSC = theGridLg.startCorner;
                var theTemp = new BlockMatchMatrix();
#if DEBUG || DEVELOPMENT_BUILD
                theTemp.strBlockName = theAssetName;
#endif
                var theCol = 0;
                var theRow = 0;
                switch (theConstraint)
                {
                    case GridLayoutGroup.Constraint.FixedColumnCount:
                        {
                            theCol = theGridLg.constraintCount;
                            theRow = theChildCount / theCol;
                            break;
                        }
                    case GridLayoutGroup.Constraint.FixedRowCount:
                        {
                            theRow = theGridLg.constraintCount;
                            theCol = theChildCount / theRow;
                            break;
                        }
                    default:
                        {
                            Debug.LogError("GridLayoutGroup组件的constraint属性需设置为FixedColumnCount 或 FixedRowCount");
                            break;
                        }
                }

                if (theRow > 0 && theCol > 0)
                {
                    tempBlockRowLayout.Clear();

                    theTemp.rowNum = theRow;
                    theTemp.colNum = theCol;
                    theTemp.curLayout = 0;
                    theTemp.gridCount = 0;
                    theTemp.haveEmpty = haveEmpty;
                    theTemp.checkColEdges = new List<int>();
                    theTemp.checkRowEdges = new List<int>();
                    theTemp.curLayouts = new List<ulong>();
                    theTemp.curLayouts_col = new List<ulong>();
                    theTemp.rowEdges = new List<Edge>();
                    theTemp.colEdges = new List<Edge>();

                    theTemp.checkRowEdges.Add(0);
                    theTemp.checkColEdges.Add(0);
                    
                    for (int index = 0; index < theRow; index++)
                    {                      
                        var theLayout = 0ul;
                        for (int indexj = 0; indexj < theCol; indexj++)
                        {
                            var theIndex = index * theCol + indexj;
                            var isEmpty = haveEmpty && emptyList.Contains(theIndex);
                            if (!isEmpty)
                            {
                                theTemp.gridCount++;
                                theLayout |= ((uint)1 << indexj);
                                theTemp.curLayout |= ((uint)1 << theIndex);
                            }
                            
                            #region 影响边数变化的行列统计
                            bool bNeedCheck = false;
                            int tempIndex = -1;
                            int rowEdge = index + 1;
                            int colEdge = indexj + 1;
                            if(false == theTemp.checkRowEdges.Contains(rowEdge))
                            {//row check
                                if (index < theRow - 1)
                                {//下边的格子
                                    tempIndex = theIndex + theCol;
                                    var tempValue = haveEmpty && emptyList.Contains(tempIndex);
                                    if (tempValue != isEmpty)
                                    {
                                        bNeedCheck = true;
                                    }
                                }
                                if (bNeedCheck) theTemp.checkRowEdges.Add(rowEdge);
                            }

                            if (false == theTemp.checkColEdges.Contains(colEdge))
                            {//col check
                                bNeedCheck = false;
                                if (indexj < theCol - 1)
                                {//右边的格子
                                    tempIndex = theIndex + 1;
                                    var tempValue = haveEmpty && emptyList.Contains(tempIndex);
                                    if (tempValue != isEmpty)
                                    {
                                        bNeedCheck = true;
                                    }
                                }

                                if (bNeedCheck) theTemp.checkColEdges.Add(colEdge);
                            }
                            #endregion
                        }
                        tempBlockRowLayout.Add(theLayout);

                        var row1Value = (byte)0;
                        var row2Value = (byte)theLayout;
                        if (index > 0) row1Value = (byte)tempBlockRowLayout[index - 1];                    
                        RefreshEdgeForRow(index, row1Value, row2Value, theCol, ref theTemp.rowEdges);
                        if (index == theRow - 1)
                        {
                            RefreshEdgeForRow(theRow, row2Value, 0, theCol, ref theTemp.rowEdges);
                        }
                    }

                    theTemp.checkRowEdges.Add(theRow);
                    theTemp.checkColEdges.Add(theCol);

                    var theColDiff = boardWidth - theCol;
                    for (int colIndex = 0; colIndex < (theColDiff + 1); colIndex++)
                    {
                        var theLayout = 0ul;
                        for (int index = 0; index < theRow; index++)
                        {
                            var rLayout = tempBlockRowLayout[index];
                            rLayout = rLayout << colIndex;
                            theLayout |= (rLayout << (boardWidth * index));
                        }
                        theTemp.curLayouts.Add(theLayout);
                    }

                    var tRow = theCol;
                    var tCol = theRow;
                    tempBlockRowLayout.Clear();
                    for (int index = 0; index < tRow; index++)
                    {
                        var theLayout = 0ul;
                        for (int indexj = 0; indexj < tCol; indexj++)
                        {
                            var realIndex = indexj * theCol + index;
                            var isEmpty = haveEmpty && emptyList.Contains(realIndex);
                            if (isEmpty) continue;

                            var colIndex = tCol - indexj - 1;
                            var theIndex = index * tCol + colIndex;
                            theLayout |= ((uint)1 << indexj);
                        }
                        tempBlockRowLayout.Add(theLayout);

                        var col1Value = (byte)0;
                        var col2Value = (byte)theLayout;
                        if (index > 0) col1Value = (byte)tempBlockRowLayout[index - 1];
                        RefreshEdgeForCol(index, col1Value, col2Value, theRow, ref theTemp.colEdges);
                        if (index == tRow - 1)
                        {
                            RefreshEdgeForCol(tRow, col2Value, 0, theRow, ref theTemp.colEdges);
                        }
                    }

                    theColDiff = boardHeight - tCol;
                    for (int colIndex = 0; colIndex < (theColDiff + 1); colIndex++)
                    {
                        var theLayout = 0ul;
                        for (int index = 0; index < tRow; index++)
                        {
                            var rLayout = tempBlockRowLayout[index];
                            rLayout = rLayout << colIndex;
                            theLayout |= (rLayout << (boardHeight * index));
                        }
                        theTemp.curLayouts_col.Add(theLayout);
                    }

                    blockNameToMatchMatrix[theAssetName] = theTemp;
                }
            }
            else
            {
                Debug.LogError("不应该出现这样情况, GridLayoutGroup组件不存在或者startCorner属性没设置为UpperLeft");
            }
        }
        theBlock.gameObject.SetActive(false);


        if (parent == null) return theBlock;

        theBlock.gameObject.SetActive(true);
        theBlock.transform.SetParent(parent, false);
        theBlock.transform.localPosition = Vector3.zero;
        theBlock.transform.localScale = Vector3.one;
       
        if (blockGrides != null)
        {
            var theStorageBlockInfo = genInfo;
            var theColor = blockColor;
            if (theStorageBlockInfo.gemList != null
                && theStorageBlockInfo.gemList.Count == theStorageBlockInfo.gemPosList.Count)
            {
                for (int j = 0; j < blockGrides.Count; j++)
                {
                    var item = blockGrides[j];
                    if (item == null || item.RectTf == null) Debug.LogError("不应该出现这样情况, blockGrid 为 空");

                    item.CurImage.enabled = true;
                    var theIndex = theStorageBlockInfo.gemPosList.FindIndex(ele => ele == j);
                    if (theIndex >= 0)
                    {
                        item.GemType = (GemType)theStorageBlockInfo.gemList[theIndex];
                    }
                    else
                    {
                        item.GemType = GemType.None;
                    }
                    item.ColorState = theColor;
                }
            }
            else
            {
                var theGem = GenerateGemForBlock();
                var theGemId = theGem.Key;
                var theGemCount = theGem.Value;

                // 追踪已经选择的随机索引
                HashSet<int> selectedIndices = new HashSet<int>();

                while (theGemCount > 0 && selectedIndices.Count < blockGrides.Count)
                {
                    int randomIndex = UnityEngine.Random.Range(0, blockGrides.Count);

                    if (!selectedIndices.Contains(randomIndex))
                    {
                        selectedIndices.Add(randomIndex);

                        var item = blockGrides[randomIndex];
                        if (item == null || item.RectTf == null)
                        {
                            Debug.LogError("不应该出现这样情况, blockGrid 为 空");
                            continue;
                        }

                        // 分配宝石类型
                        item.CurImage.enabled = true;
                        item.GemType = (GemType)theGemId;
                        item.ColorState = theColor;
                        theGemCount--;
                    }
                }

                // 设置未被选中的格子为无宝石状态
                for (int i = 0; i < blockGrides.Count; i++)
                {
                    if (!selectedIndices.Contains(i))
                    {
                        var item = blockGrides[i];
                        if (item != null && item.RectTf != null)
                        {
                            item.CurImage.enabled = true;
                            item.GemType = GemType.None;
                            item.ColorState = theColor;
                        }
                    }
                }
            }
        }
        else
        {
            CLog.Exception($"创建Block[{theAssetName}]出错, 预设资源[{theAssetName}] 对应的 blockGrides 为空");
        }

        return theBlock;
    }

    int PosToIndex(Vector2 pos)
    {
        var theMul = 100.0f;
        var tempX = pos.x * theMul;
        var tempY = -pos.y * theMul;
        var rectX = boardRect.x * theMul;
        var rectY = boardRect.y * theMul;
        if (tempX < rectX || tempY < rectX) return -1;

        var gridWidth = gridSize.x * theMul;
        var gridHeight = gridSize.y * theMul;
        int x = (int)((tempX - rectX) / gridWidth + 0.01f);
        int y = (int)((tempY - rectX) / gridWidth + 0.01f);
        if (x >= 0 && x < boardWidth && y >= 0 && y < boardHeight)
        {
            return x + y * boardWidth;
        }

        return -1;
    }

    bool PosToCoordinate(Vector2 pos, out Vector2 vRes)
    {
        vRes = Vector2.zero;
        var theMul = 100.0f;
        var tempX = pos.x * theMul;
        var tempY = -pos.y * theMul;
        var rectX = boardRect.x * theMul;
        var rectY = boardRect.y * theMul;
        if (tempX < rectX || tempY < rectX) return false;

        var gridWidth = gridSize.x * theMul;
        var gridHeight = gridSize.y * theMul;
        float x = (tempX - rectX) / gridWidth;
        float y = (tempY - rectX) / gridWidth;
        if (x >= 0 && x < boardWidth && y >= 0 && y < boardHeight)
        {
            vRes.x = x;
            vRes.y = y;
            return true;
        }

        return false;
    }

    bool IsChoosedBlock => dragBlockNodeIndex >= 0 && dragBlockNodeIndex < blockContainers.Count;

    Vector3 ChoosedBlockPos
    {
        get
        {
            if (!IsChoosedBlock)
            {
                return Vector3.zero;
            }
            return blockContainerPos[dragBlockNodeIndex];
        }
    }


    /// <summary>
    /// 选中填充块的拖动体
    /// </summary>
    Transform choosedDragBlockContainer = null;
    Transform ChoosedDragBlockContainer
    {
        get
        {
            return choosedDragBlockContainer;
        }

        set
        {
            if (choosedDragBlockContainer != value)
            {
                choosedDragBlockContainer = value;
                dragBlockNodeIndex = blockContainers.FindIndex(ele => ele == choosedDragBlockContainer);
            }
        }
    }

    /// <summary>
    /// 真正选中的拖动体
    /// </summary>
    RectTransform ChoosedBlockReality
    {
        get
        {
            if (!IsChoosedBlock)
            {
                return null;
            }
            return blocks[dragBlockNodeIndex];
        }
    }

    /// <summary>
    /// 真正选中的拖动体
    /// </summary>
    EnumGridColor ChoosedBlockColor
    {
        get
        {
            if (!IsChoosedBlock)
            {
                return EnumGridColor.EGC_Red;
            }
            return blockColors[dragBlockNodeIndex];
        }
    }

    protected int GetDragAreaIndex(GameObject go)
    {
        if (go == null)
            return -1;
        var theIndex = dragAreas.FindIndex(a => a.gameObject == go);
        return theIndex;
    }

    protected bool IsDragNode(GameObject go)
    {
        var theIndex = GetDragAreaIndex(go);
        if (theIndex < 0) return false;
        
        var theBlockContainer = theIndex < blockContainers.Count ? blockContainers[theIndex] : null;
        var theBlock = theIndex < blocks.Count ? blocks[theIndex] : null;
        if (theBlockContainer == null || theBlock == null || isGenerating)
        {
            return false;
        }
        return theIndex >= 0;
    }

    #endregion

    #region 分数更新

    StorageGameEndless gameStorage
    {
        get
        {
            return StorageExtension.GameEndlssStorage;
        }
    }

    public bool CheckGameOver()
    {
        bool isOver = true;
        for (int i = 0; i < blocks.Count; i++)
        {
            var theBlock = blocks[i];
            if (theBlock == null) continue;
            bool bGet = blockNameToMatchMatrix.TryGetValue(theBlock.name, out var curMatrix);
            if (bGet == false) continue;

            bool bRes = BlockPlayManager.Instance.MatrixBlockMatchCheck(curMatrix);
            if (bRes)
            {
                isOver = false;
                break;
            }
        }
        return isOver;
    }
    public void HandleGameOver()
    {
        if(isRewarded) return;

        if (putdownCount == 0 ||putdownCount >= genBlockCount) return;
        bool isOver = true;
        for (int i = 0; i < blocks.Count; i++)
        {
            var theBlock = blocks[i];
            if (theBlock == null) continue;
            bool bGet = blockNameToMatchMatrix.TryGetValue(theBlock.name, out var curMatrix);
            if (bGet == false) continue;

            bool bRes = BlockPlayManager.Instance.MatrixBlockMatchCheck(curMatrix);
            if (bRes)
            {
                isOver = false;
                break;
            }
        }
        if (!isOver) return;//还有可放的块
        MoveHomeBlock(false);

        if (CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            StorageExtension.GameEndlssStorage.TodayPlayCount++;
            EventBus.Dispatch(new BlockGameOver(curLife));
        }
        else if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {//关卡模式无限复活
            EventBus.Dispatch(new BlockGameOver(curLife));
        }
    }

    bool isRewarded = false;
    private int curScore = 0;
    private int curLife = 0;

    public int CurScore => curScore;

    public int CurLife => curLife;

    public void RewardLife(int life)
    {
        curLife += life; 
    }
    /// <summary>
    /// 最终结算
    /// </summary>
    public void HandleGameReward(EnumGameResultType type = EnumGameResultType.EFST_NoResult)
    {
        if (isRewarded) return;
        isRewarded = true;
        
        if (SDK<INetwork>.Instance.GetServerTime() - startPlayTime >= 15000)
        {
            SDK<IStorage>.Instance.Get<StorageGlobal>().EfficientEnterCount++;
            BIHelper.SendTrackingEvent_GamePlayTimes();
        }
        BIHelper.SendTrackingEvent_GameOnLineTime();
        if(CurGameType == EnumBlockGameType.BlockGame_Endless)
        {
            if (type != EnumGameResultType.EFST_None)
            {
                var theStorage = StorageExtension.GameEndlssStorage;
                if (type != EnumGameResultType.EFST_NoResult) theStorage.HandleReset();
                theStorage.GameResult = (int)type;
            }

            if (type == EnumGameResultType.EFST_Fail || type == EnumGameResultType.EFST_Victory)
            {
                if (curScore > gameStorage.CurFirstScore)
                {
                    gameStorage.CurThirdScore = gameStorage.CurSecondScore;
                    gameStorage.CurSecondScore = gameStorage.CurFirstScore;
                    gameStorage.CurFirstScore = curScore;
                }
                else if (curScore > gameStorage.CurSecondScore)
                {
                    gameStorage.CurThirdScore = gameStorage.CurSecondScore;
                    gameStorage.CurSecondScore = curScore;
                }
                else if (curScore > gameStorage.CurThirdScore)
                {
                    gameStorage.CurThirdScore = curScore;
                }

                while (gameStorage.ClosestFivePlayScores.Count >= 5)
                {
                    gameStorage.ClosestFivePlayScores.RemoveAt(0);
                }
                gameStorage.ClosestFivePlayScores.Add(curScore);

                BIHelper.SendTrackingEvent_GameEndlessTopScore(curScore);
            }
        }
        else if (CurGameType == EnumBlockGameType.BlockGame_Stage)
        {
            if(type != EnumGameResultType.EFST_None)
            {
                var theStorage = StorageExtension.GameBlockLevelStorage;
                if (type != EnumGameResultType.EFST_NoResult) theStorage.HandleReset();
                var theInfo = theStorage.CurLevelInfo;
                theInfo.gameResult = (int)type;
                theStorage.CurLevelInfo = theInfo;
              

                if(type == EnumGameResultType.EFST_Victory)
                {
                    var theConfig = InGameConfigManager.GetChapterLevelInfo(theInfo.levelId);
                    if (theConfig!= null && theConfig.RewardId > 0)
                    {
                        var conf = InGameConfigManager.GetChapterRewardConfig(theConfig.RewardId);
                        if (null!= conf && conf.UnlockRoomNodeId > 0)
                        {
                            RoomManager.Instance.SetRoomNodeUnlockStaus(conf.UnlockRoomNodeId);
                        }
                    }

                    var theNewInfo = LevelPlayInfo.defaultInfo;
                    theNewInfo.levelId = theInfo.levelId + 1;
                    theStorage.LevelPlayInfos.Add(theNewInfo);
                }
            }  
        }
        
    }

    #endregion

    #region GM相关

    void CurLayoutStore()
    {
#if DEBUG || DEVELOPMENT_BUILD
        var theLayout = BlockPlayManager.Instance.GetBoardInfo();
        preLayout = theLayout;

        pregGenList.Clear();
        pregGenList.AddRange(genList);
#endif
    }

    public void PreLayoutLoad()
    {
#if DEBUG || DEVELOPMENT_BUILD
        
        if (preLayout == 0) return;

        LoadBoardInfo(preLayout, true);
        
        CLog.Error($"PreLayoutLoad--{BlockPlayManager.Instance.EdgeCountAdvance}");

        var theParam = GenerateParam.GetNormalParam(EnumBlockProductType.EBPT_PreBack);
        GenerateBlockGo(theParam);

#endif

    }

#if DEBUG || DEVELOPMENT_BUILD
    ulong preLayout = 0;
    List<int> pregGenList = new List<int>(genBlockCount);
#endif

    public void LoadBoardInfo(ulong boardLayout, bool fromPreLayout = false)
    {
        if (boardLayout <= 0) return;

        curGridLayout = 0;
        BlockPlayManager.Instance.ClearBoardLayout();

        for (int i = 0; i < grids.Length; i++)
        {
            Grid theGrid = grids[i];
            var theHave = (boardLayout >> i) & 1;
            if (theHave == 0)
            {               
                theGrid.ShowState = EnumGridState.EGS_Hide;
                HandleBoardLayout (i, false);
                BlockPlayManager.Instance.RemoveBlock(theGrid.yIndex, theGrid.xIndex);
            }
            else
            {
                theGrid.ShowState = EnumGridState.EGS_Show;
                HandleBoardLayout(i, true);
                BlockPlayManager.Instance.AddBlock(theGrid.yIndex, theGrid.xIndex);
            }
        }

        CLog.Error($"LoadBoardInfo--{BlockPlayManager.Instance.EdgeCountAdvance}");

        if(fromPreLayout == false)
        {
            for (int i = 0; i < blocks.Count; i++)
            {
                var block = blocks[i];
                if(block == null) continue;
                block.gameObject.SetActive(false);
                block.SetParent(curBlockPool);
                blocks[i] = null;
            }

            GenerateBlockGo(GenerateParam.defaultP);
        }
    }

    public void Debug_GenerateBlockGo(EnumBlockProductType pType = EnumBlockProductType.EBPT_Normal)
    {
        //ulong board = (ulong)UnityEngine.Random.Range(0, 1000000);
        //ulong[] ids =  BoardRecommendShapes(board, 0, BlockMatchCoreNative.StrategyEnum.StrategyRandom);
        //Debug.Log("id1:" + ids[0] + " id2:" + ids[1] + " id3:" + ids[2]);
        
        //string json = "{}";
        //var arr = System.Text.Encoding.UTF8.GetBytes(json);
        //int ret = BlockMatchCoreNative.reload_weights(arr);
        //Debug.Log("reload ret = " + ret);
        
        {
            for (int i = 0; i < blocks.Count; i++)
            {
                var block = blocks[i];
                if (block == null) continue;
                block.gameObject.SetActive(false);
                block.SetParent(curBlockPool);
                blocks[i] = null;
            }

            List<int> weights = null;
            if (CurGameType == EnumBlockGameType.BlockGame_Stage)
            {
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                if (theView != null)
                {
                    weights = theView.GetProductSubTypeWeights(pType);
                }
            }
            
            if (pType == EnumBlockProductType.EBPT_ComplexityUp)
            {
                HandleBuildUpType(weights);
            }
            else if (pType == EnumBlockProductType.EBPT_ComplexityDown)
            {
                HandleClearBlockType(weights);
            }

            var theParam = GenerateParam.GetNormalParam(pType);
            GenerateBlockGo(theParam);
        }
    }

    public void Debug_RefreshBlock(int blockId, int index)
    {
        if (index < 0 || index >= genList.Count || blockContainers.Count != genBlockCount) return;

        {
            var theAssetName = GetBlockName(blockId);
            if (!blockNameToMatchMatrix.ContainsKey(theAssetName)) return;
            var theColor = blockColors[index];
            var theNewBlock = CreateOrGetBlock(theAssetName, blockContainers[index], (EnumGridColor)theColor);
            if (theNewBlock == null) return;

            genList[index] = blockId;
            var block = blocks[index];
            if (block == null)
            {
                putdownCount++;
            }
            else
            {
                block.gameObject.SetActive(false);
                block.SetParent(curBlockPool);
            }
            blocks[index] = theNewBlock;
        }
    }

    public void Debug_JumpStage(int levelId)
    {
        var theLevelConfig = InGameConfigManager.GetInGameLevelCfgByLevelId(levelId);
        if (theLevelConfig == null) return;

        CLog.Info($"Debug_JumpStage--跳转到关卡[{levelId}]，ABTest分组[{theLevelConfig.AbTestGroup}]");

        var theLevelInfo = StorageExtension.GameBlockLevelStorage.CurLevelInfo;
        theLevelInfo.levelId = levelId;
        StorageExtension.GameBlockLevelStorage.CurLevelInfo = theLevelInfo;
}

    public async Task TestCheckBlockGameDead()
    {
        CancellationTokenSource cts = new CancellationTokenSource();
        cts.CancelAfter(1000000000);
        var theTask = BlockPlayManager.Instance.DeadCheck(cts.Token);
        await theTask;
        cts.Cancel();
    }

    public bool IsForbidRewardUI => isForbidRewardUI;

    bool isForbidRewardUI = false;
    public void GM_HandleRewardUI(bool forbid)
    {
        isForbidRewardUI = forbid;
    }

    #endregion

    #region event
    void OnAcrossDay(EventAcrossDay ev)
    {
        StorageExtension.GameEndlssStorage.TodayPlayCount = 0;
        StorageExtension.GameEndlssStorage.LastBaseBoardIndex = 0;
    }

    #endregion

    #region 继承API
    public override void Init()
    {
        base.Init();

        try
        {
            csInterval = InGameConfigManager.GetGlobalConfig<int>("ClearScreenBlockInterval");
            mcInterval = InGameConfigManager.GetGlobalConfig<int>("MultiClearBlockInterval");
            sbInterval = InGameConfigManager.GetGlobalConfig<int>("SmallBlockTriggerInterval");
            limitClearComplextiy = InGameConfigManager.GetGlobalConfig<int>("ComplextiyDownTriggerNumber");
            firstClearScreenBlockInterval = InGameConfigManager.GetGlobalConfig<int>("FirstClearScreenBlockInterval");
            firstClearScreenBestScore = InGameConfigManager.GetGlobalConfig<int>("FirstClearScreenBestScore");
            predictChurnValue = InGameConfigManager.GetGlobalConfig<float>("PredictChurnValue");

        }
        catch (Exception ex)
        {
            CLog.Error(ex);
        }

        if (blockConfig.Count <= 0)
        {
            origainBlockConfig.Clear();
            foreach (var kv in bockDic)
            {
                var theBase = kv.Key * 100 + 1;
                var theValue = kv.Value;
                for (int i = 0; i < theValue; i++)
                {
                    var theKv = theBase + i;
                    blockConfig.Add(theKv);
                    origainBlockConfig.Add(theKv);
                }
            }
            curBlockConfig = blockConfig;
        }
        blockConfig.Shuffle();

        if (dragAreas == null) dragAreas = new List<RectTransform>(genBlockCount);

        if (blocks == null)
        {
            blockContainers = new List<RectTransform>(genBlockCount);
            blocks = new List<RectTransform>(genBlockCount) { null, null, null };
            blockColors = new List<EnumGridColor>(genBlockCount) { EnumGridColor.EGC_None, EnumGridColor.EGC_None, EnumGridColor.EGC_None };
            blockContainerPos = new List<Vector3>(genBlockCount);
        }
    }

    public override void SubscribeEvents()
    {
        base.SubscribeEvents();
        EventBus.Subscribe<EventAcrossDay>(OnAcrossDay);
    }
    
    public override void OnShutDown()
    {
        base.OnShutDown();
        HandleGameDispose();
    }

    #endregion
   
    #region GM
    public void GM_HandleVictory()
    {
        if (TargetScore == 0)
        {
            var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
            if (theView == null) return;
            achiveGemList.Clear();
            if (CurGameType != EnumBlockGameType.BlockGame_Stage) return;
            foreach (var item in TargetItems)
            {
                // 宝石
                if (item.type <= 5 && HasTargetType(EnumTargetType.ETT_Gem))
                {
                    for (var j = 0; j < item.count; j++)
                    {
                        achiveGemList.Add(new KeyValuePair<int, Vector3>(item.type, Vector3.zero));
                    }
                    if (achiveGemList.Count > 0)
                    {
                        //发事件
                        EventBus.Dispatch(new EventGameGemAchive(achiveGemList));
                    }
                }
                // 木箱
                else if (item.type == UIView_Game.SPECIAL_ID_WOODEN_CRATE && HasTargetType(EnumTargetType.ETT_WoodenCrate))
                {
                    EventBus.Dispatch(new EventGameWoodenCrateAchive(item.count, Vector3.zero));
                }
                // 鸟窝
                else if (item.type == UIView_Game.SPECIAL_ID_BIRD && HasTargetType(EnumTargetType.ETT_Bird))
                {
                    EventBus.Dispatch(new EventGameBirdAchive(item.count, Vector3.zero));
                }

                // 检查猫目标
                if (item.type == UIView_Game.SPECIAL_ID_CAT && HasTargetType(EnumTargetType.ETT_Cat))
                {
                    EventBus.Dispatch(new EventGameCatAchive(item.count, Vector3.zero));
                }
            }
        }
        else
        {
            EventBus.Dispatch(new EventGameScoreChange(curScore, TargetScore));
        }
    }
    public string GetPruductName(EnumBlockProductType type)
    {
        var theRes = "None";
        switch (type)
        {
            case EnumBlockProductType.EBPT_Guarantee:
                theRes = "Guarantee";
                break;
            case EnumBlockProductType.EBPT_ComplexityUp:
                theRes = "build";
                break;
            case EnumBlockProductType.EBPT_ComplexityDown:
                theRes = "clear";
                break;
            case EnumBlockProductType.EBPT_Normal:
                theRes = "random";
                break;
            case EnumBlockProductType.EBPT_Hard:
                theRes = "hard";
                break;
            case EnumBlockProductType.EBPT_MultiClear:
                theRes = "mClear";
                break;
            case EnumBlockProductType.EBPT_ClearScreen:
                theRes = "clearScreen";
                break;
            default:
                break;
        }
        return theRes;
    }

    #endregion


    
    /// <summary>
    /// 播放木箱裂开特效
    /// </summary>
    /// <param name="targetTransform">目标Transform</param>
    public void PlayWoodCrackEffect(Transform targetTransform)
    {
        if (targetTransform == null) return;
        
        // 查找多层方块的Transform（应该是multiBlockTs）
        var gridInfo = GetGridInfoByTransform(targetTransform);
        if (gridInfo == null || gridInfo.multiBlockTs == null)
        {
            CLog.Warning($"无法找到多层方块Transform，targetTransform: {targetTransform.name}");
            return;
        }
        
        // 在MultiBlock的子节点中查找VFX_Wood_1
        var vfxWood1 = gridInfo.multiBlockTs.Find("VFX_Wood_1");
        if (vfxWood1 == null)
        {
            CLog.Warning($"在MultiBlock中未找到VFX_Wood_1特效节点");
            return;
        }
        
        // 播放一次性的木箱裂开特效
        PlaySingleWoodCrackEffect(vfxWood1);
    }
    
    /// <summary>
    /// 播放单次木箱裂开特效
    /// </summary>
    /// <param name="vfxWood1">VFX_Wood_1特效节点</param>
    void PlaySingleWoodCrackEffect(Transform vfxWood1)
    {
        if (vfxWood1 == null) return;
        
        // 播放音效
        //GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_wood_crack");
        
        // 激活特效
        vfxWood1.gameObject.SetActive(true);
        
        // 1秒后自动停用特效
        GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1f, () =>
        {
            if (vfxWood1 != null)
            {
                vfxWood1.gameObject.SetActive(false);
                CLog.Info($"停用木箱裂开特效: {vfxWood1.name}");
            }
        }));
    }

    /// <summary>
    /// 播放OverlayBlock特效
    /// </summary>
    /// <param name="parentTransform">父节点Transform</param>
    /// <param name="onCompleteCallback">特效播放完成后的回调</param>
    public void PlayOverlayBlockDestroyEffect(Transform parentTransform, Action onCompleteCallback = null)
    {
        // 添加空引用检查
        if (parentTransform == null)
        {
            onCompleteCallback?.Invoke();
            return;
        }
        
        var leafBlockNode = parentTransform.GetChild(0);
        if (leafBlockNode == null)
        {
            onCompleteCallback?.Invoke();
            return;
        }

        var vfxNode = leafBlockNode.GetChild(0);
        if (vfxNode != null)
        {
            // 激活特效节点
            vfxNode.gameObject.SetActive(true);
        }
        // 0.8秒后隐藏特效节点
        DOVirtual.DelayedCall(0.8f, () =>
        {
            // 停用特效节点
            if (vfxNode != null && vfxNode.gameObject != null)
            {
                vfxNode.gameObject.SetActive(false);
            }
            onCompleteCallback?.Invoke();
        });
    }

    /// <summary>
    /// 播放多层方块消除动画
    /// </summary>
    /// <param name="gridInfo">Grid信息</param>
    /// <param name="previousClearTime">消除前的层数</param>
    /// <param name="currentClearTime">消除后的层数</param>
    /// <param name="onCompleteCallback">动画完成后的回调</param>
    public void PlayMultiBlockDestroyEffect(Grid gridInfo, int previousClearTime, int currentClearTime, Action onCompleteCallback = null)
    {
        try
        {
            if (gridInfo?.multiBlockTs == null)
            {
                onCompleteCallback?.Invoke();
                return;
            }

            var animator = gridInfo.multiBlockTs.GetComponent<Animator>();
            if (animator == null)
            {
                onCompleteCallback?.Invoke();
                return;
            }

            string animationClip = "";
            
            // 根据消除层数选择动画片段
            if (previousClearTime == 3 && currentClearTime == 2)
            {
                animationClip = "disappear1";
            }
            else if (previousClearTime == 2 && currentClearTime == 1)
            {
                animationClip = "disappear2";
            }
            else
            {
                onCompleteCallback?.Invoke();
                return;
            }

            // 播放指定的动画片段
            animator.Play(animationClip);
            
            // 获取动画片段长度
            AnimationClip[] clips = animator.runtimeAnimatorController.animationClips;
            float animationLength = 0.5f; // 默认长度
            
            foreach (var clip in clips)
            {
                if (clip.name == animationClip)
                {
                    animationLength = clip.length;
                    break;
                }
            }

            // 等待动画播放完成后执行回调
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(animationLength, () =>
            {
                onCompleteCallback?.Invoke();
            }));

        }
        catch (System.Exception ex)
        {
            onCompleteCallback?.Invoke();
        }
    }

    /// <summary>
    /// 根据Transform获取对应的Grid信息
    /// </summary>
    /// <param name="targetTransform">目标Transform</param>
    /// <returns>Grid信息，如果未找到返回null</returns>
    Grid GetGridInfoByTransform(Transform targetTransform)
    {
        if (grids == null || targetTransform == null) return null;
        
        foreach (var grid in grids)
        {
            if (grid?.RectTf == targetTransform)
            {
                return grid;
            }
        }
        
        CLog.Warning($"未找到Transform对应的Grid信息: {targetTransform.name}");
        return null;
    }
    /// <summary>
    /// 清除2x2方块
    /// </summary>
    /// <param name="root">2x2方块根节点</param>
    /// <param name="processedRoots">已处理的2x2方块列表</param>
    private void ClearAdvancedBlock(Grid root, HashSet<Grid> processedRoots)
    {
        Action doClear = () =>
        {
            // 清除高级方块后，触发猫收集事件
            TriggerCatCollectionFromAdvancedBlock(root);
            for (int r = 0; r < 2; r++)
            {
                for (int c = 0; c < 2; c++)
                {
                    var partX = root.xIndex + c;
                    var partY = root.yIndex + r;

                    // 添加边界检查
                    if (partX >= boardWidth || partY >= boardHeight)
                    {
                        CLog.Warning($"AdvancedBlock boundary check failed: partX={partX}, partY={partY}, boardWidth={boardWidth}, boardHeight={boardHeight}");
                        continue;
                    }

                    var gridToClear = grids[partY * boardWidth + partX];
                    if (gridToClear.ShowState == EnumGridState.EGS_Show)
                    {
                        var gridIndex = gridToClear.yIndex * boardWidth + gridToClear.xIndex;
                        HandleBoardLayout(gridIndex, false);
                        BlockPlayManager.Instance.RemoveBlock(gridToClear.yIndex, gridToClear.xIndex);
                        gridToClear.ShowState = EnumGridState.EGS_Hide;
                        gridToClear.AdvancedBlockId = 0;
                        gridToClear.advancedBlockRoot = null;
                        levelLayoutGridList.Remove(gridIndex);
                    }
                }
            }
        };

        // 立即执行清除逻辑（与动画同时进行）
        doClear();

        // 如果有动画，播放动画但不等待完成
        if (root.boxSpine != null)
        {
            var animationName = "open4";
            var animation = root.boxSpine.Skeleton.Data.FindAnimation(animationName);
            if (animation != null)
            {
                root.boxSpine.AnimationState.SetAnimation(0, animationName, false);
                // 注意：这里移除了等待动画完成的逻辑，让动画独立播放
            }
        }
    }
    
    /// <summary>
    /// 从高级方块清除时触发猫收集事件
    /// </summary>
    /// <param name="root">被清除的高级方块根节点</param>
    public void TriggerCatCollectionFromAdvancedBlock(Grid root)
    {
        // 只在关卡模式且目标类型为猫时触发
        if (CurGameType != EnumBlockGameType.BlockGame_Stage || !HasTargetType(EnumTargetType.ETT_Cat))
        {
            return;
        }
        
        // 计算猫收集的位置（使用高级方块的中心位置）
        Vector3 catCollectionPos = Vector3.zero;
        if (root?.RectTf != null)
        {
            catCollectionPos = root.RectTf.position;
        }
        
        // 触发猫收集事件，每个高级方块产生1只猫
        EventBus.Dispatch(new EventGameCatAchive(1, catCollectionPos));
        
        CLog.Info($"从高级方块收集到猫，位置: {catCollectionPos}");
    }
}