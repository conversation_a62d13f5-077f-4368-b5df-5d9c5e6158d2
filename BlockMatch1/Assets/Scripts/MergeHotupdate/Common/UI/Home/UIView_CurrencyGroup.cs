using DG.Tweening;
using DragonPlus.Core;
using Framework;
using TMGame;
using UnityEngine;
using UnityEngine.UI;

public class UIView_CurrencyGroup : UIView_CurrencyGroupBase
{
    
    public enum CurrencyType
    {
        Normal,
        Shop,
        DailyTask,
        Level,
    }
    
    private UserProfileSys userProfileSys;
    private EnergySys energySys;
    private long _energyCountNum;
    private long _coinCountNum;
    private long _diamondCountNum;

    public int OrigainLayer;
    protected override void OnCreate()
    {
        base.OnCreate();
        energySys = GameGlobal.GetMod<EnergySys>();
        userProfileSys = GameGlobal.GetMod<UserProfileSys>();

        _energyCountNum = energySys.GetEnergy();
        _coinCountNum = userProfileSys.GetItemCount(EItemType.Coin);
        _diamondCountNum = userProfileSys.GetItemCount(EItemType.Key);
        OrigainLayer = this.SortingOrder;
        TMUtility.NotchAdapte(this.UINode_Main);
        FlyTarget.AddTarget(EItemType.Energy, UIImg_Infinite.transform);
        FlyTarget.AddTarget(EItemType.EnergyInfinity, UIImg_Infinite.transform);
        FlyTarget.AddTarget(EItemType.Coin, UIBtn_Coin.transform.Find("Icon"));
        FlyTarget.AddTarget(EItemType.Key, UIBtn_Diamond.transform.Find("Icon"));
    }

    private bool orgCoinActive = true;
    private bool orgLiveActive = true;
    protected override void OnOpen()
    {
        base.OnOpen();
        RefreshView_ResourceBar();
        orgCoinActive = !ModABTestEx.IPlayGroupB();
        orgLiveActive = orgCoinActive;
        UIBtn_Live.gameObject.SetActive(orgLiveActive);
        UIBtn_Coin.gameObject.SetActive(orgCoinActive);
        if (!orgCoinActive)
        {
            var hlayout = GO.transform.Find("Root/UINode_Main/TopNode").GetComponent<HorizontalLayoutGroup>();
            if (hlayout != null) hlayout.enabled = true;
        }
        UIImg_BuyImage3.gameObject.SetActive(false);
        UIBtn_Diamond.interactable = false;
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();
        UpdateEnergyTime();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        UnRegisterGameEvent();

        FlyTarget.RemoveTarget(EItemType.Energy, UIImg_Infinite.transform);
        FlyTarget.RemoveTarget(EItemType.EnergyInfinity, UIImg_Infinite.transform);
        FlyTarget.RemoveTarget(EItemType.Coin, UIBtn_Coin.transform.Find("Root/Icon"));
        FlyTarget.RemoveTarget(EItemType.Key, UIBtn_Diamond.transform.Find("Root/Icon"));
    }

    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();
        
        UIBtn_ButtonAvatar.onClick.AddListener(OnAvatarBtn);   
        UIBtn_Live.onClick.AddListener(OnAddEnergyButtonClicked);
        UIBtn_Coin.onClick.AddListener(OnAddCoinButtonClicked);
        UIBtn_Diamond.onClick.AddListener(OnAddDiamondButtonClicked);      
    }
    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<EventEnergyChange>(OnEventEnergyChange);
        EventBus.Subscribe<EventCurrencyChange>(OnEventCurrencyChange);

    }

    public void SetCurrencyType(CurrencyType type)
    {
        UIBtn_Live.gameObject.SetActive(orgLiveActive);
        UIBtn_Coin.gameObject.SetActive(orgCoinActive);
        if (type == CurrencyType.Shop)
        {
            //UIBtn_Live.gameObject.SetActive(true);
            UIBtn_Diamond.gameObject.SetActive(false);
            UIImg_BuyImage2.gameObject.SetActive(false);
            UIBtn_Coin.interactable = false;
        }
        else if (type == CurrencyType.Level)
        {
            UIImg_BuyImage3.gameObject.SetActive(false);
            UIBtn_Diamond.interactable = false;

        }
        else if (type == CurrencyType.Normal)
        {
            UIBtn_Diamond.gameObject.SetActive(true);
            UIImg_BuyImage2.gameObject.SetActive(true);
            UIBtn_Coin.interactable = true;
            UIImg_BuyImage3.gameObject.SetActive(false);
            UIBtn_Diamond.interactable = false;
        }
        else
        {
            UIBtn_Live.gameObject.SetActive(false);
            UIBtn_Coin.interactable = true;
        }
      
    }

    private void UnRegisterGameEvent()
    {
        EventBus.Unsubscribe<EventEnergyChange>(OnEventEnergyChange);
        EventBus.Unsubscribe<EventCurrencyChange>(OnEventCurrencyChange);
    }

    public void RefreshView_ResourceBar()
    {
        UITxt_Count1.SetTerm(_energyCountNum.ToString());
        int cacheKeyCount = GameGlobal.GetMod<ModFly>().GetItemCount(EItemType.Key);
        int cacheCoinCount = GameGlobal.GetMod<ModFly>().GetItemCount(EItemType.Coin);
        _coinCountNum -= cacheCoinCount;
        _diamondCountNum -= cacheKeyCount;
        UpdateCoinCount(_coinCountNum, 0, false);
        UpdateDiamondCount(_diamondCountNum, 0, false);
    }
    #region ����
    private void UpdateEnergyTime()
    {
        var leftTime = energySys.GetEnergyInfinityLeftTime();
        if (leftTime > 0)
        {
            UITxt_Count1.gameObject.SetActive(false);
            var timeStr = TMUtility.FormatPropItemTime(leftTime);
            if (UITxt_TimeText1.GetText() != timeStr)
            {
                UITxt_TimeText1.gameObject.SetActive(true);
                UITxt_TimeText1.SetText(timeStr);
            }

            UIImg_Infinite.gameObject.SetActive(true);
            UIBtn_Live.interactable = false;
            UIImg_BuyImage1.gameObject.SetActive(false);
            return;
        }
        if (!energySys.IsEnergyFull())
        {
            UITxt_Count1.gameObject.SetActive(true);
            UIImg_Infinite.gameObject.SetActive(false);
            var newText = SDKUtil.TimeDate.GetTimeString("%mm:%ss", (int)(energySys.LeftAutoAddEnergyTime() * 0.001));
            if (UITxt_TimeText1.GetText() != newText)
            {
                UITxt_TimeText1.SetText(newText);
            }
            UIBtn_Live.interactable = true;
            UIImg_BuyImage1.gameObject.SetActive(true);
        }
        else
        {
            UITxt_Count1.gameObject.SetActive(true);
            UIImg_Infinite.gameObject.SetActive(false);
            var fullText = CoreUtils.GetLocalization("&key.UI_store_energy_full_text");
            if (UITxt_TimeText1.GetText() != fullText)
            {
                UITxt_TimeText1.SetText(fullText);
            }
            UIBtn_Live.interactable = false;
            UIImg_BuyImage1.gameObject.SetActive(false);
        }
    }

    private void OnAddEnergyButtonClicked()
    {
        if (UIImg_BuyImage1.gameObject.activeSelf)
        {
            if (!energySys.IsEnergyFull())
            {
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_BuyEnergy);
            }
        }
    }

    private Tween _energyTween;

    private void UpdateEnergyCount(long startCoinNum, long delta, bool hasRollAnimation = true)
    {
        if (energySys.IsInfiniteEnergy())
        {
            hasRollAnimation = false;
        }

        long target = startCoinNum + delta;

        if (_energyTween != null)
        {
            _energyTween.Kill();
            _energyTween = null;
        }

        DOTween.Kill(UITxt_Count1);

        if (target < 0)
        {
            target = 0;
            startCoinNum = 0;
        }

        if (target > energySys.GetMaxEnergy())
        {
            target = energySys.GetMaxEnergy();
        }

        if (hasRollAnimation)
        {
            UITxt_Count1.SetText(startCoinNum.GetCommaFormat());

            long v = startCoinNum;

            _energyTween = DOTween.To(() => v, (x) =>
            {
                v = x;
                UITxt_Count1.SetText(v.GetCommaFormat());
            }, target, 1.0f).OnComplete(() =>
            {
                UITxt_Count1.SetText(target.GetCommaFormat());
                if (target != energySys.GetEnergy())
                {
                    UITxt_Count1.SetText(energySys.GetEnergy().GetCommaFormat());
                    _energyCountNum = energySys.GetEnergy();
                    Log.Error($"�ͻ���չʾ������({target})�뱾�ش洢({energySys.GetEnergy()})���ݲ�ͬ���������!");
                }
            });
        }
        else
        {
            UITxt_Count1.SetText(target.GetCommaFormat());
            if (target != energySys.GetEnergy())
            {
                UITxt_Count1.SetText(energySys.GetEnergy().GetCommaFormat());
                _energyCountNum = energySys.GetEnergy();
                Log.Error($"�ͻ���չʾ������({target})�뱾�ش洢({energySys.GetEnergy()})���ݲ�ͬ���������!");
            }
        }
    }

    private void OnEventEnergyChange(EventEnergyChange evt)
    {
        UpdateEnergyTime();

        if (evt.delta == 0)
            return;

        UpdateEnergyCount(_energyCountNum, evt.delta);
        _energyCountNum += evt.delta;
        _energyCountNum = Mathf.Max(0, (int)_energyCountNum);
        if (_energyCountNum > energySys.GetMaxEnergy())
            _energyCountNum = energySys.GetMaxEnergy();
    }

    private void OnAddDiamondButtonClicked()
    {
        //if (GameGlobal.GetMod<GuideSys>().IsShowingGuide()) return;
        UIView_PopupNoMoney.ShowUI();
    }

    private void OnAvatarBtn()
    {
    }

    #endregion

    #region ���
    private void OnAddCoinButtonClicked()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ShopMain,
            new ShopOpenParams(false, 0, ShopOpenSource.CoinWidget, ShopPageType.PageCoin));
    }

    private Tween _coinTween;

    private void UpdateCoinCount(long startCoinNum, long delta, bool hasRollAnimation)
    {
        long target = startCoinNum + delta;

        if (_coinTween != null)
        {
            _coinTween.Kill();
            _coinTween = null;
        }

        DOTween.Kill(UITxt_Count2);

        if (target < 0)
        {
            target = 0;
            startCoinNum = 0;
        }

        if (hasRollAnimation)
        {
            UITxt_Count2.SetTerm(startCoinNum.GetCommaFormat());

            long v = startCoinNum;

            _coinTween = DOTween.To(() => v, (x) =>
            {
                v = x;
                UITxt_Count2.SetTerm(v.GetCommaFormat());
            }, target, 0.8f).OnComplete(() => { UITxt_Count2.SetTerm(target.GetCommaFormat()); });
        }
        else
        {
            UITxt_Count2.SetTerm(target.GetCommaFormat());
        }
    }


    #endregion

    #region ��ʯ
    private Tween _diamondTween;

    private void UpdateDiamondCount(long startCoinNum, long delta, bool hasRollAnimation)
    {
        long target = startCoinNum + delta;

        if (_diamondTween != null)
        {
            _diamondTween.Kill();
            _diamondTween = null;
        }

        DOTween.Kill(UITxt_Count3);
        if (target < 0)
        {
            target = 0;
            startCoinNum = 0;
        }

        if (hasRollAnimation)
        {
            UITxt_Count3.SetTerm(startCoinNum.GetCommaFormat());

            long v = startCoinNum;
            _diamondTween = DOTween.To(() => v, (x) =>
            {
                v = x;
                UITxt_Count3.SetTerm(v.GetCommaFormat());
            }, target, 0.8f).OnComplete(() => { UITxt_Count3.SetTerm(target.GetCommaFormat()); });
        }
        else
        {
            UITxt_Count3.SetTerm(target.GetCommaFormat());
        }
    }


    #endregion

    private void OnEventCurrencyChange(EventCurrencyChange evt)
    {
        if (evt.EItemType == EItemType.Coin)
        {
            UpdateCoinCount(_coinCountNum, evt.delta, evt.playAni);
            _coinCountNum += evt.delta;
        }
        else if (evt.EItemType == EItemType.Key)
        {
            UpdateDiamondCount(_diamondCountNum, evt.delta, evt.playAni);
            _diamondCountNum += evt.delta;
        }
    }

    public void HandleResBarForView(int viewId, bool bReg)
    {
        if (viewId == UIViewName.UIView_GameVictory)
        {
            UIBtn_Live.gameObject.SetActive(!bReg);
            UIBtn_Diamond.gameObject.SetActive(!bReg);
        }
    }
}
