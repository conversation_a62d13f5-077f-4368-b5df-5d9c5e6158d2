using DragonPlus;
using Framework;
using System.Collections.Generic;
using TMGame;
using UnityEngine.UI;

public class UIWidget_AdsItem : UIWidget_AdsItemBase
{
    private List<ShopItemViewParam> paramList;

    public void AddNoAdItem(ShopItemViewParam paramData)
    {
        if (paramList == null) paramList = new List<ShopItemViewParam>();
        paramList.Add(paramData);
        if (paramList.Count > 1)
        {
            UIBtn_Buy1.onClick.AddListener(() =>
            {
                paramList[0].buyOnClickItem.Invoke(paramList[0].ItemData, this);
            });

            UIBtn_Buy2.onClick.AddListener(() =>
            {
                paramList[1].buyOnClickItem.Invoke(paramList[1].ItemData, this);
            });
            RefreshUI();
        }
    }

    private void RefreshUI()
    {
        for (int i = 0; i < paramList.Count; i++)
        {
            if (i > 1) break;
            var paramData = paramList[i];
            string priceStr = GameGlobal.GetMod<IAPSys>().GetPrice(paramData.ItemData.Id);

            if (i == 1)
            {
                UIOldTxt_Price2.text = priceStr;
                //UITxt_TitleText_right.SetText(LocalizationManager.Instance.GetLocalizedString(paramData.ItemData.Name));
                //if (paramData.ItemData.ItemCnt != null && paramData.ItemData.ItemCnt.Count >= 0)
                //{
                //    UITxt_NumberText.SetText($"{paramData.ItemData.ItemCnt[0].ToString()}");
                //}

                //if (paramData.ItemData.Market != 0)
                //{
                //    if (paramData.ItemData.ShowDiscount > 0)
                //        UITxt_TagText.SetText($"-{paramData.ItemData.ShowDiscount}%");
                //    else if (paramData.ItemData.Market == 1)
                //        UITxt_TagText.SetText(LocalizationManager.Instance.GetLocalizedString("UI_iap_market_1"));
                //    else if (paramData.ItemData.Market == 2)
                //        UITxt_TagText.SetText(LocalizationManager.Instance.GetLocalizedString("UI_iap_market_2"));
                //    else if (paramData.ItemData.Market == 3)
                //        UITxt_TagText.SetText(LocalizationManager.Instance.GetLocalizedString("UI_iap_market_3"));
                //}
            }
            else
            {
                UIOldTxt_Price1.text = priceStr;
                //UITxt_TitleText_left.SetText(LocalizationManager.Instance.GetLocalizedString(paramData.ItemData.Name));
            }
        }
    }

    protected override void OnClose()
    {
        base.OnClose();
        UIBtn_Buy1.onClick.RemoveAllListeners();
        UIBtn_Buy2.onClick.RemoveAllListeners();
    }
}
