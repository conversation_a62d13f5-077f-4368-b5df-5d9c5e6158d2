using DragonPlus.Core;
using TMGame;
using UnityEngine;

public class UIView_Tip : UIView_TipBase
{
    protected override void OnInit(object viewData)
    {
        base.OnInit(viewData);
        EventBus.Subscribe<EvtShowTip>(OnShowTip);
        EventBus.Subscribe<EvtCloseTip>(OnCloseTip);
    }

    private void OnShowTip(EvtShowTip evt)
    {
        Transform trans = GetTrans(evt.openData.posType);
        OpenUIWidget<UIWidget_Tip>(trans, false, evt.openData);
    }

    private void OnCloseTip(EvtCloseTip evt)
    {
        CloseUIWidget(evt.tipWidget, true);
    }

    private Transform GetTrans(ETipPosType posType)
    {
        switch (posType)
        {
            case ETipPosType.Top:
                return UINode_Top;

            case ETipPosType.Mid:
                return UINode_Mid;

            case ETipPosType.Bottom:
                return UINode_Bottom;
        }
        return null;
    }

    protected override void OnD<PERSON>roy()
    {
        base.OnDestroy();
        EventBus.Unsubscribe<EvtShowTip>(OnShowTip);
        EventBus.Unsubscribe<EvtCloseTip>(OnCloseTip);
    }
}