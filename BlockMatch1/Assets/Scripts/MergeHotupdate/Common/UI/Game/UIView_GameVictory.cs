using DragonPlus;
using DragonPlus.Ad;
using DragonPlus.Config.InGame;
using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using System.Collections.Generic;
using TMGame;
using UnityEngine;
using UnityEngine.UI;

public class GameVictoryData
{
    public int levelId;
    public int achievedScore;
    public Dictionary<int, GemInfo> curGemInfoDic;
    public EnumTargetType curTargetType;
    public EnumLevelGameType levelGameType;
    public HashSet<EnumTargetType> activeTargetTypes;
    public bool isVictory;

    public GameVictoryData()
    {
        achievedScore = 0;
        curTargetType = EnumTargetType.ETT_Invaild;
        levelGameType = EnumLevelGameType.ELT_Score;
        activeTargetTypes = new HashSet<EnumTargetType>();
        isVictory = false;
    }
}

public class UIView_GameVictory : UIView_GameVictoryBase
{
    GameVictoryData gameVictoryData;
    Table_InGame_Level levelConfig;
    Transform origainGem;

    int needCoinForReLife = 200;
    int buildCoinReward = 10;
    int coinReward;

    int origainLayer;
    private ModGame gameSys;
    
    protected override void BindComponent()
    {
        base.BindComponent();
        var theNode = UINode_Failed.Find("Level/UICe_LevelTip/Tip/BG_Gem");
        origainGem = UILayoutH_Gems.transform.Find("UIView_GemItemBig");
        origainGem.gameObject.SetActive(false);
        gameSys = GameGlobal.GetMod<ModGame>();
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        gameVictoryData = ViewData as GameVictoryData;
        if (levelConfig == null)
        {
            levelConfig = InGameConfigManager.GetInGameLevelCfgByLevelId(gameVictoryData.levelId);
        }

        coinReward = InGameConfigManager.GetGlobalConfig<int>("CoinLevelReward");
        buildCoinReward = InGameConfigManager.GetGlobalConfig<int>("BuildCoinLevelReward");
        needCoinForReLife = InGameConfigManager.GetGlobalConfig<int>("ReviveCostCoins");
        ;
        UITxt_ReviveCoin.SetText(needCoinForReLife.ToString());
        UINode_Failed.gameObject.SetActive(false);
        UINode_Passed.gameObject.SetActive(false);
        UINode_Spaces.gameObject.SetActive(false);
        if (gameVictoryData.isVictory)
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_settlement_3");
            HandleVictory();
        }
        else
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_settlement_7");
            HandleShowSpace();
        }
    }

    protected override void OnShow()
    {
        base.OnShow();
        gameVictoryData = ViewData as GameVictoryData;
        if (gameVictoryData == null) return;

        if (gameVictoryData.isVictory)
        {
            HandleVictory();
        }
        else
        {
            if (UINode_Failed.gameObject.activeSelf)
            {
                HandleFail();
            }
            else
            {
                HandleShowSpace();
            }
        }
    }


    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();

        UIBtn_Close.onClick.AddListener(HandleFail);
        UIBtn_Receive.onClick.AddListener(OnReciveReward);
        UIBtn_Again.onClick.AddListener(OnAgainBtn);
        UIBtn_Double.onClick.AddListener(OnReciveDoubleReward);
        UIBtn_Home.onClick.AddListener(OnCloseBtn);
        UIBtn_PlayOn.onClick.AddListener(OnReLifeByAds);
        UIBtn_Receive_Ns.onClick.AddListener(OnReLifeByPay);
    }

    protected override void OnClose()
    {
        base.OnClose();
        HandleResumeCurrencyUI();
    }

    void HandleResumeCurrencyUI()
    {
        var theCurrencyUI = GameGlobal.GetMgr<UIMgr>().GetCurrencyUI() as UIView_CurrencyGroup;
        if (theCurrencyUI != null && origainLayer != -1)
        {
            theCurrencyUI.HandleResBarForView(this.ViewId, false);
            theCurrencyUI.OrderInLayer = origainLayer;
        }
    }

    void HandleShowSpace()
    {
        UINode_Spaces.gameObject.SetActive(true);

        var theCurrencyUI = GameGlobal.GetMgr<UIMgr>().GetCurrencyUI() as UIView_CurrencyGroup;
        if (theCurrencyUI != null)
        {
            origainLayer = theCurrencyUI.OrderInLayer;
            theCurrencyUI.OrderInLayer = OrderInLayer + 1;
            theCurrencyUI.HandleResBarForView(this.ViewId, true);
        }
        else
        {
            origainLayer = -1;
        }

        var curRvCount = GameGlobal.GetMod<AdSys>().GetRewardLastCount(eAdReward.GameRevive);
        if (curRvCount <= 0)
        {
            UIBtn_PlayOn.gameObject.SetActive(false);
        }
    }

    void HandleVictory()
    {      
        UINode_Passed.gameObject.SetActive(true);

        //UITxt_Level_Pass.gameObject.SetActive(true);
        //UITxt_Level_Pass.SetText($"LEVEL{gameVictoryData.levelId}");
        UITxt_RewardCount.gameObject.SetActive(true);

        UITxt_RewardCount.SetText(buildCoinReward.ToString());
        UITxt_RewardCount_Coin.SetText(coinReward.ToString());
        var rvCount = GameGlobal.GetMod<AdSys>().GetRewardLastCount(eAdReward.GameWinDoubleReward);
        if (rvCount <= 0)
        {
            UIBtn_Double.gameObject.SetActive(false);
        }
        Active.DailyTask.Model.Instance.AddCurrentDailyPassLevelCount();
    }

    void HandleFail()
    {       
        UINode_Passed.gameObject.SetActive(false);
        UINode_Spaces.gameObject.SetActive(false);

        HandleResumeCurrencyUI();

        UINode_Failed.gameObject.SetActive(true);

        //UITxt_Level_Fail.gameObject.SetActive(true);
        //UITxt_Level_Fail.SetText($"LEVEL{gameVictoryData.levelId}");

        var isScoreTarget = gameVictoryData.levelGameType == EnumLevelGameType.ELT_Score;
        UINode_GemTarget.gameObject.SetActive(!isScoreTarget);
        UISlider_ScoreTarget.gameObject.SetActive(isScoreTarget);
        if (isScoreTarget)
        {
            UITxt_TargetDes.SetTerm("UI_level_prompt_score");
            //var targetScore = levelConfig.PointsCount; //弃用google表数据
            var targetScore = gameSys.TargetScore;
            var curScore = gameVictoryData.achievedScore;
            UISlider_ScoreTarget.value = (float)curScore / targetScore;
            UITxt_ProgressEx.SetText(curScore.ToString());
            UITxt_End.SetText(targetScore.ToString());
        }
        else
        {
            UITxt_TargetDes.SetTerm("UI_level_prompt_target");
            
            foreach (Transform child in UILayoutH_Gems.transform)
            {
                if (child != origainGem)
                {
                    child.gameObject.SetActive(false);
                }
            }
            
            foreach (var item in gameVictoryData.curGemInfoDic)
            {
                var theId = item.Key;
                var theName = theId.ToString();
                Transform theGemPlay = UILayoutH_Gems.transform.Find(theName);
                if (theGemPlay == null)
                {
                    theGemPlay = GameObject.Instantiate(origainGem);
                    theGemPlay.SetParent(UILayoutH_Gems.transform);
                    theGemPlay.localPosition = Vector3.zero;
                    theGemPlay.localScale = Vector3.one;
                    theGemPlay.name = theName;
                }

                var gemImage = theGemPlay.Find("Icon").GetComponent<Image>();
                var gemCountTxt = theGemPlay.Find("UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
                var gemCollectedFull = theGemPlay.Find("UIImg_Full");
                theGemPlay.gameObject.SetActive(true);
                var iconPath = GetIconPathForTarget(theId);
                CoreUtils.SetImg(gemImage, Const_Common.GameAtlas, iconPath);
                var isFull = item.Value.gemTargetCount - item.Value.gemCount <= 0;
                gemCountTxt.gameObject.SetActive(!isFull);
                gemCollectedFull.gameObject.SetActive(isFull);
                var count = isFull ? 0 : (item.Value.gemTargetCount - item.Value.gemCount);
                gemCountTxt.SetText(count.ToString());
            }
        }
    }

    private void HandleReciveReward(int multiple = 1)
    {
        var theCoinReward = coinReward * multiple;
        var theBuildCoinReward = buildCoinReward * multiple;
        var theLevelId = levelConfig?.Id ?? 0;
        var theItemChangeBiArg = new BIHelper.ItemChangeReasonArgs();
        if (multiple == 1)
        {
            theItemChangeBiArg.reason = BiEventBlockMatch1.Types.ItemChangeReason.LevelReward;
        }
        else
        {
            theItemChangeBiArg.reason = BiEventBlockMatch1.Types.ItemChangeReason.LevelRewardAd;
        }

        theItemChangeBiArg.data1 = gameVictoryData.levelId.ToString();
        theItemChangeBiArg.data2 = BIHelper.GetBIItemByLogicItemType(EItemType.Coin).ToString();
        theItemChangeBiArg.data3 = theCoinReward.ToString();

        GameGlobal.GetMod<ModBag>().AddItem(EItemType.Coin, theCoinReward,
           theItemChangeBiArg, false);
        
        
        theItemChangeBiArg.data1 = gameVictoryData.levelId.ToString();
        theItemChangeBiArg.data2 = BIHelper.GetBIItemByLogicItemType(EItemType.Key).ToString();
        theItemChangeBiArg.data2 = theBuildCoinReward.ToString();
        GameGlobal.GetMod<ModBag>().AddItem(EItemType.Key, theBuildCoinReward,
           theItemChangeBiArg, false);

        ModFly.curFlyCurrencys.Clear();
        ModFly.curFlyCurrencys.Add(new KeyValuePair<EItemType, int>(EItemType.Coin, theCoinReward));
        ModFly.curFlyCurrencys.Add(new KeyValuePair<EItemType, int>(EItemType.Key, theBuildCoinReward));
        Close();
        EventBus.Dispatch(new ExitGameEvent(EnumGameResultType.EFST_Victory));

    }

    private void OnReciveReward()
    {
        bool canShow = GameGlobal.GetMod<AdSys>().ShouldShowInterstitial(eAdInterstitial.QuitGameByLevelWin, false);
        canShow &= GameGlobal.GetMod<AdSys>().UnLockInterstitial(eAdInterstitial.QuitGameByLevelWin);
        if (canShow)
        {
            GameGlobal.GetMod<AdSys>().TryShowInterstitial(eAdInterstitial.QuitGameByLevelWin);
        }

        HandleReciveReward();
    }

    private void OnReciveDoubleReward()
    {
        var adSys = GameGlobal.GetMod<AdSys>();
        adSys.TryShowRewardedVideo(eAdReward.GameWinDoubleReward, (result, str) =>
        {
            if (result == AdPlayResult.Success)
            {
                HandleReciveReward(2);
            }
        }, $"{EItemType.Coin.ToString()}, {EItemType.Key.ToString()}");
    }

    private void OnCloseBtn()
    {
        bool canShow = GameGlobal.GetMod<AdSys>().ShouldShowInterstitial(eAdInterstitial.QuitGameByLevelFail, false);
        canShow &= GameGlobal.GetMod<AdSys>().UnLockInterstitial(eAdInterstitial.QuitGameByLevelFail);
        if (canShow)
        {
            GameGlobal.GetMod<AdSys>().TryShowInterstitial(eAdInterstitial.QuitGameByLevelFail);
        }

        Close();
        EventBus.Dispatch(new ExitGameEvent(EnumGameResultType.EFST_Fail));
    }

    int origainLayerInBuyEnergy;

    private void OnAgainBtn()
    {
        var theEnergySys = GameGlobal.GetMod<EnergySys>();
        var InInfintyEnergyState = theEnergySys.IsInfiniteEnergy();
        if (!InInfintyEnergyState)
        {
            var theEnergy = theEnergySys.GetEnergy();
            if (theEnergy <= 0)
            {
                //GameGlobal.GetMod<ModTip>().ShowTip("体力不足");
                BuyEnergyViewData viewData = new BuyEnergyViewData()
                {
                    onClose = HandleResumeCurrencyUIBuyEnergy
                };
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_BuyEnergy);
                var theCurrencyUI = GameGlobal.GetMgr<UIMgr>().GetCurrencyUI() as UIView_CurrencyGroup;
                var buyEnergyUI = GameGlobal.GetMgr<UIMgr>().FindViewFirst((int)UIViewName.UIView_BuyEnergy);
                if (theCurrencyUI != null)
                {
                    origainLayerInBuyEnergy = theCurrencyUI.OrderInLayer;
                    theCurrencyUI.OrderInLayer = buyEnergyUI.OrderInLayer + 1;
                    theCurrencyUI.HandleResBarForView(this.ViewId, true);
                }
                else
                {
                    origainLayerInBuyEnergy = -1;
                }

                void HandleResumeCurrencyUIBuyEnergy()
                {
                    var theCurrencyUI = GameGlobal.GetMgr<UIMgr>().GetCurrencyUI() as UIView_CurrencyGroup;
                    if (theCurrencyUI != null && origainLayerInBuyEnergy != -1)
                    {
                        theCurrencyUI.HandleResBarForView(this.ViewId, false);
                        theCurrencyUI.OrderInLayer = origainLayerInBuyEnergy;
                    }
                }

                return;
            }
        }

        Close();
        EventBus.Dispatch(new ReplayGameEvent());
    }


    private void OnReLifeByAds()
    {
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
        var theCount = theView?.CurReviveCount ?? 0 + 1;
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRelive, "1",
            levelConfig.Id.ToString(), theCount.ToString());
        var adSys = GameGlobal.GetMod<AdSys>();
        adSys.TryShowRewardedVideo(eAdReward.GameRevive, (result, str) =>
        {
            if (result == AdPlayResult.Success)
            {
                Close();
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                if (theView != null)
                {
                    theView.HandleRevive();
                }
            }
        });
    }

    private void OnReLifeByPay()
    {
        var theLevelId = levelConfig?.Id ?? 0;
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
        var theCount = theView?.CurReviveCount ?? 0 + 1;
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventRelive, "1",
            theLevelId.ToString(), theCount.ToString());

        var theItemChangeBiArg = new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.BlockRevive);
        theItemChangeBiArg.data1 = theLevelId.ToString();
        theItemChangeBiArg.data2 = needCoinForReLife.ToString();
        
        GameGlobal.GetMod<ModBag>().ConsumeItem(EItemType.Coin, needCoinForReLife,
           theItemChangeBiArg,
            () =>
            {
                Close();
                var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
                if (theView != null)
                {
                    theView.HandleRevive(needCoinForReLife);
                }
            },
            () =>
            {
                //金币不够
                GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_ShopMain,
                    new ShopOpenParams(false, 0, ShopOpenSource.CoinWidget, ShopPageType.PageCoin));
            }
        );
    }
    
    private string GetIconPathForTarget(int targetId)
    {
        const int SPECIAL_ID_WOODEN_CRATE = 301;
        const int SPECIAL_ID_BIRD = 401;
        const int SPECIAL_ID_CAT = 302;

        switch (targetId)
        {
            case SPECIAL_ID_WOODEN_CRATE:
                return "bm_game_big_wood";
            case SPECIAL_ID_BIRD:
                return "bm_game_big_bird";
            case SPECIAL_ID_CAT:
                return "bm_game_big_cat";
            default:
                return $"bm_game_gem{targetId}";
        }
    }
}