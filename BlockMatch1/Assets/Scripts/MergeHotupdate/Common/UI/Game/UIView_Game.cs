using DragonPlus.Core;
using DragonPlus;
using Framework;
using TMGame.Storage;
using TMGame;
using DragonPlus.Config.InGame;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;
using DG.Tweening;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using MainLauncher.Utils;

public struct GemInfo
{
    public int gemId;
    public int gemCount;
    public int gemTargetCount;
    public Transform gemFull;
    public Transform gemTs;
    public Image gemImage;
    public Animator AnimatorCom;
    public LocalizeTextMeshProUGUI gemCountTxt;
}

public class BuffItem
{
    ModBag bagSys;
    EItemType itemId;
    bool isUnlock;

    Transform rootTs;
    Image itemIcon;
    Transform lockNode;
    Transform countNode;
    LocalizeTextMeshProUGUI countTxt;
    GameButton addBtn;
    Button useBtn;

    public EItemType ItemId =>itemId;
    public Transform RootTs => rootTs;

    public BuffItem(Transform ts, EItemType item, bool unLock)
    {
        rootTs = ts;
        itemId = item;
        isUnlock = unLock;
        var theTs = rootTs;
        this.itemIcon = theTs.Find("Icon").GetComponent<Image>();
        lockNode = theTs.Find("lock");
        countNode = theTs.Find("CountBG");
        countTxt = countNode.GetComponentInChildren<LocalizeTextMeshProUGUI>();
        addBtn = theTs.Find("Add").gameObject.GetComponentOrAdd<GameButton>();
        useBtn = theTs.GetComponent<Button>();
        bagSys = GameGlobal.GetMod<ModBag>();

        EventBus.Subscribe<EventCurrencyChange>(OnEventCurrencyChange);

        RefreshInfo();
        RegGuideTarget();
    }

    private void RegGuideTarget()
    {
        if (!isUnlock)
        {
            return;
        }
        
        // 检查道具引导开关
        if (!UIView_Game.ENABLE_PROP_GUIDE)
        {
            return;
        }
        
        GuideTargetType targetType = GuideTargetType.None;
        if (itemId == EItemType.Bomb)
        {
            targetType = GuideTargetType.Bomb;
        }
        else if (itemId == EItemType.Rotate)
        {
            targetType = GuideTargetType.Rotate;
        }
        else if (itemId == EItemType.Convert)
        {
            targetType = GuideTargetType.Convert;
        }

        if (targetType == GuideTargetType.None)
        {
            return;
        }

        var guide = GameGlobal.GetMod<GuideSys>();
        guide.RegisterTarget(targetType, useBtn.transform, "1");
    }

    public void HandleDispose()
    {
        EventBus.Unsubscribe<EventCurrencyChange>(OnEventCurrencyChange);
    }

    public void RefreshInfo()
    {
        bool isItemUnLock = isUnlock;
        lockNode.gameObject.SetActive(isItemUnLock == false);
        itemIcon.gameObject.SetActive(isItemUnLock);
        countNode.gameObject.SetActive(isItemUnLock);
        addBtn.gameObject.SetActive(isItemUnLock);

        if (!isItemUnLock) return;

        var theCount = bagSys.GetItemCount(itemId);
        countNode.gameObject.SetActive(theCount > 0);
        addBtn.gameObject.SetActive(theCount <= 0);
        countTxt.SetText(theCount.ToString());
    }

    private void OnEventCurrencyChange(EventCurrencyChange evt)
    {
        if (evt.EItemType == itemId)
        {
            RefreshInfo();
        }
    }

    Action<EItemType> useItemAction;

    public Action<EItemType> UseItemAction
    {
        set
        {
            useItemAction = value;
            useBtn.onClick.AddListener(() =>
            {
                useItemAction?.Invoke(itemId);
               
            });
        }
    }

    Action<EItemType> addItemAction;

    public Action<EItemType> AddItemAction
    {
        set
        {
            addItemAction = value;
            addBtn.onClick.AddListener(() => { addItemAction?.Invoke(itemId); });
        }
    }
}

public class UIView_GameData
{
    public int levelId;

    public UIView_GameData()
    {
        levelId = 0;
    }
}

public class UIView_Game : UIView_GameBase
{
    // 特殊目标物的ID常量
    public const int SPECIAL_ID_WOODEN_CRATE = 301;  // 木箱的specialId
    public const int SPECIAL_ID_BIRD = 401;          // 鸟的specialId
    public const int SPECIAL_ID_CAT = 302;           // 猫的specialId
    public const int SPECIAL_ID_LEAF = 501;          // 树叶的specialId
    public const int SPECIAL_ID_ICE = 502;           // 冰块的specialId
    
    // 道具引导开关 - 后期如需恢复可设置为true
    public static bool ENABLE_PROP_GUIDE = false;
    
    public static EItemType CurUsingItem { get; private set; }
    public static bool CurUsingItemWaitEffect { get; private set; }

    UIView_GameData curViewData;
    Table_InGame_Level levelConfig;
    Table_InGame_StorySub storySubConfig;

    private ModGame gameSys;
    EnumTargetType targetType;

    protected Transform UINode_CombolingBoardEffect;
    protected Transform UINode_CombolingFullEffect;

    Transform UINode_ClickEffect;

    List<int> targetGems = new List<int>() { 1, 2, 3, 4 };
    List<int> targetGemCounts = new List<int>() { 5, 8, 12, 31 };
    Dictionary<int, GemInfo> curGemInfoDic = new Dictionary<int, GemInfo>();
    List<BuffItem> buffItems;
    bool bCanPlay = false;
    bool isPlayingVictoryAnimation = false; // 新增：标记是否正在播放胜利动画
    bool isCollectingTargets = false; // 新增：标记是否有目标收集事件正在处理中
    int curHardCount = 0;
    int curScore = 50;
    int targetScore = 500;
    // 新增：木箱和鸟的目标跟踪
    int targetWoodenCrates = 0;
    int curWoodenCrateCount = 0;
    int targetBirds = 0;
    int curBirdCount = 0;
    int targetCats = 0;
    int curCatCount = 0;
    // 新增：覆盖物目标跟踪
    int targetLeaves = 0;
    int curLeafCount = 0;
    int targetIces = 0;
    int curIceCount = 0;
    EnumGameResultType gameResultType = EnumGameResultType.EFST_None;
    
    // 新增：多目标支持
    EnumLevelGameType levelGameType = EnumLevelGameType.ELT_Score;  // 关卡游戏类型
    HashSet<EnumTargetType> activeTargetTypes = new HashSet<EnumTargetType>();  // 当前关卡激活的目标类型

    /// <summary>
    /// 检查当前关卡是否包含指定的目标类型
    /// </summary>
    /// <param name="targetType">要检查的目标类型</param>
    /// <returns>是否包含该目标类型</returns>
    public bool HasActiveTargetType(EnumTargetType targetType)
    {
        return activeTargetTypes.Contains(targetType);
    }

    private Animation shakeAnim;
    ModBag bagSys;

    // 引导系统相关变量（使用新的变量名避免冲突）
    private Tween mainGuideTween;
    private bool isMainGuideTriggered = false;
    private CoroutineHandler _shakeEffectCoroutine;
    // 新增：引导完成后的清屏特效处理
    private bool isGuideCompletingWithEffect = false;
    // 新增：控制引导期间是否加分的变量
    private bool isGuideActive = false;

    // 新增：批量飞行收集器
    private struct TargetFlyInfo
    {
        public int targetId;
        public Vector3 sourcePos;
        public Vector3 targetPos;
        public Transform uiTransform;
        public System.Action onComplete;
    }

    private List<TargetFlyInfo> pendingFlyAnimations = new List<TargetFlyInfo>();
    private bool isBatchProcessing = false;

    // 清屏引导系统变量
    private bool isPlayClearGuide = false;
    private Tween clearFingerTween;
    private List<BlockPutInfo> tempClearList = new List<BlockPutInfo>();
    private List<RectTransform> tempBlockList = new List<RectTransform>();
    private int tempClearIndex = 0;
    private int tempCurIndex = 0;
    private GameObject tempBlock;

    private const int MaxClearGuideSteps = 2;
    private const float GuideMoveDuration = 2.3f;
    private const int GuideLoopCount = 9999;

    protected int[] SelectIndexs = new[] { 0, 1, 2 };
    protected int[] SelectIndexsNew = new[] { 1, 2, 0 };

    protected override void BindComponent()
    {
        base.BindComponent();
        UINode_PropEffect.gameObject.SetActive(false);
        UINode_UseItemBG.gameObject.SetActive(false);
        UINode_Props.gameObject.SetActive(false);
        UINode_CloseProp.gameObject.SetActive(false);

        shakeAnim = this.GO.GetComponent<Animation>();
        gameSys = GameGlobal.GetMod<ModGame>();
        bagSys = GameGlobal.GetMod<ModBag>();

        UINode_ClickEffect = UINode_Main.Find("VFX_Click");
        UINode_ClickEffect.SetAsLastSibling();
        UINode_ClickEffect.gameObject.SetActive(false);
        UINode_CombolingBoardEffect = UINode_Main.Find("VFX_checkerboard_light_1");
        UINode_CombolingFullEffect = GO.transform.Find("Root/VFX_checkerboard_light_2");

        TMUtility.NotchAdapte(UINode_Main);

        if (gameSys.DragAreas.Count <= 0)
        {
            gameSys.DragAreas.Add(UINode_Area1);
            gameSys.DragAreas.Add(UINode_Area2);
            gameSys.DragAreas.Add(UINode_Area3);
        }

        if (gameSys.BlockContainers.Count <= 0)
        {
            for (int i = 0; i < UINode_Block1.childCount; i++)
            {
                UINode_Block1.GetChild(i).gameObject.SetActive(false);
            }

            for (int i = 0; i < UINode_Block2.childCount; i++)
            {
                UINode_Block2.GetChild(i).gameObject.SetActive(false);
            }

            for (int i = 0; i < UINode_Block3.childCount; i++)
            {
                UINode_Block3.GetChild(i).gameObject.SetActive(false);
            }

            gameSys.BlockContainers.Add(UINode_Block1);
            gameSys.BlockContainers.Add(UINode_Block2);
            gameSys.BlockContainers.Add(UINode_Block3);

            gameSys.BlockContainerPos.Add(UINode_Block1.position);
            gameSys.BlockContainerPos.Add(UINode_Block2.position);
            gameSys.BlockContainerPos.Add(UINode_Block3.position);
        }


#if DEVELOPMENT_BUILD
        UIBtn_Debug.gameObject.SetActive(true);
        UITxt_Complex.gameObject.SetActive(true);
#else
    UINode_Debug.gameObject.SetActive(false);
     UIBtn_Debug.gameObject.SetActive(false);
    UITxt_Complex.gameObject.SetActive(false);
#endif
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<ExitGameEvent>(onExitGame);
        EventBus.Subscribe<ReplayGameEvent>(onReplayGame);
        EventBus.Subscribe<EventGameScoreChange>(HandleRewardScore);
        EventBus.Subscribe<EventGameGemAchive>(HandleRewardGem);
        EventBus.Subscribe<BlockGameOver>(onGameOver);
        EventBus.Subscribe<EventProductBlockInfo>(HandleProductBlockEvent);
        EventBus.Subscribe<EventBlockPutDownInfo>(HandleBlockPutDown);
        EventBus.Subscribe<EventPlayBombExplode>(OnPlayBombExplode);
        // 新增：注册木箱、鸟和猫收集事件
        EventBus.Subscribe<EventGameWoodenCrateAchive>(HandleRewardWoodenCrate);
        EventBus.Subscribe<EventGameBirdAchive>(HandleRewardBird);
        EventBus.Subscribe<EventGameCatAchive>(HandleRewardCat);
        // 新增：注册覆盖物收集事件
        EventBus.Subscribe<EventGameLeafAchive>(HandleRewardLeaf);
        EventBus.Subscribe<EventGameIceAchive>(HandleRewardIce);
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        EventBus.Unsubscribe<ExitGameEvent>(onExitGame);
        EventBus.Unsubscribe<ReplayGameEvent>(onReplayGame);
        EventBus.Unsubscribe<EventGameScoreChange>(HandleRewardScore);
        EventBus.Unsubscribe<EventGameGemAchive>(HandleRewardGem);
        EventBus.Unsubscribe<BlockGameOver>(onGameOver);
        EventBus.Unsubscribe<EventProductBlockInfo>(HandleProductBlockEvent);
        EventBus.Unsubscribe<EventBlockPutDownInfo>(HandleBlockPutDown);
        EventBus.Unsubscribe<EventPlayBombExplode>(OnPlayBombExplode);
        // 新增：取消订阅木箱、鸟和猫收集事件
        EventBus.Unsubscribe<EventGameWoodenCrateAchive>(HandleRewardWoodenCrate);
        EventBus.Unsubscribe<EventGameBirdAchive>(HandleRewardBird);
        EventBus.Unsubscribe<EventGameCatAchive>(HandleRewardCat);
        // 新增：取消订阅覆盖物收集事件
        EventBus.Unsubscribe<EventGameLeafAchive>(HandleRewardLeaf);
        EventBus.Unsubscribe<EventGameIceAchive>(HandleRewardIce);
    }

    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();

        UIBtn_Close.onClick.AddListener(OnExitGameBtn);
        UIBtn_SetUp.onClick.AddListener(OnSetBtn);
        var mFullShield = UINode_FullShield;
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.PointerDown, OnPointerDown);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.PointerUp, OnPointerUp);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.Drag, OnDrag);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.BeginDrag, OnBeginDrag);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.EndDrag, OnEndDrag);
        
        UIBtn_ComplexityUp.onClick.AddListener(OnComplexityUpBtn);
        UIBtn_ComplexityDown.onClick.AddListener(OnComplexityDownBtn);
        UIBtn_Hard.onClick.AddListener(OnHardBtn);
        UIBtn_Random.onClick.AddListener(OnRandomBlockBtn);
        UIBtn_MultiClear.onClick.AddListener(() =>
        {
            gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_MultiClear);
        });
        UIBtn_ClearScreen.onClick.AddListener(() => {
            gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ClearScreen);
        });
        
        UIBtn_Debug.onClick.AddListener(OnShowDebugBtn);
    }

    void HandleInitInfo()
    {
        // 清空之前的目标配置
        activeTargetTypes.Clear();
        // 新目标判断，如果没通过再使用下面的作为兼容
        if(gameSys.TargetScore > 0)
        {
            levelGameType = EnumLevelGameType.ELT_Score;
            targetType = EnumTargetType.ETT_Score;  // 保持兼容性
            targetScore = gameSys.TargetScore;
            activeTargetTypes.Add(EnumTargetType.ETT_Score);
        }
        else
        {
            if (gameSys.TargetItems == null || gameSys.TargetItems?.Count == 0)
            {
                CLog.Error($"关卡[{levelConfig.JsonFileName}]目标配置错误");
            }
            else
            {
                levelGameType = EnumLevelGameType.ELT_Target;
                // 目标关：收集所有配置的目标类型
                bool hasAnyTarget = false;
                targetGems.Clear();
                targetGemCounts.Clear();
                foreach (var item in gameSys.TargetItems)
                {
                    // 宝石
                    if (item.type <= 5)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_Gem);
                        targetGems.Add(item.type);
                        targetGemCounts.Add(item.count);
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_Gem; hasAnyTarget = true; }  // 保持兼容性
                    }
                    // 木箱
                    else if (item.type == SPECIAL_ID_WOODEN_CRATE)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_WoodenCrate);
                        targetWoodenCrates = item.count;
                        curWoodenCrateCount = 0;
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_WoodenCrate; hasAnyTarget = true; }  // 保持兼容性
                    }
                    // 鸟窝
                    else if (item.type == SPECIAL_ID_BIRD)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_Bird);
                        targetBirds =  item.count;
                        curBirdCount = 0;
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_Bird; hasAnyTarget = true; }  // 保持兼容性
                    }
                    // 检查猫目标
                    else if (item.type == SPECIAL_ID_CAT)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_Cat);
                        targetCats = item.count;
                        curCatCount = 0;
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_Cat; hasAnyTarget = true; }  // 保持兼容性
                    }
                    // 检查树叶目标
                    else if (item.type == SPECIAL_ID_LEAF)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_Leaf);
                        targetLeaves = item.count;
                        curLeafCount = 0;
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_Leaf; hasAnyTarget = true; }  // 保持兼容性
                    }
                    // 检查冰块目标
                    else if (item.type == SPECIAL_ID_ICE)
                    {
                        activeTargetTypes.Add(EnumTargetType.ETT_Ice);
                        targetIces = item.count;
                        curIceCount = 0;
                        if (!hasAnyTarget) { targetType = EnumTargetType.ETT_Ice; hasAnyTarget = true; }  // 保持兼容性
                    }
                }
            }
        }
        
#if DEVELOPMENT_BUILD || UNITY_EDITOR
        UITxt_Level.gameObject.SetActive(true);
        UITxt_Level.SetText($"LEVEL{levelConfig.Id}");
#endif

        HandleInitBuff();
    }

    void HandleInitBuff()
    {
        UINode_Down.gameObject.SetActive(true);
        if (buffItems == null)
        {
            var theLayout = UINode_Down.GetComponentInChildren<HorizontalLayoutGroup>();
            if (theLayout != null)
            {
                var theItemCount = theLayout.transform.childCount;
                buffItems = new List<BuffItem>(theItemCount);
                for (int i = 0; i < theItemCount; i++)
                {
                    EItemType theItemId = GameUtils.GetBlockItemByPos(i);
                    bool isUnlock = false;
                    var theConfig = InGameConfigManager.GetIngameItemConfig((int)theItemId);
                    if (theConfig != null)
                    {
                        isUnlock = levelConfig.Sort >= theConfig.UnlockLevelId;
                        if (isUnlock)
                        {
                            var tempFind =
                                StorageExtension.GameBlockLevelStorage.RewardItemForInit.Contains((int)theItemId);
                            if (!tempFind && theConfig.InitCount > 0)
                            {
                                StorageExtension.GameBlockLevelStorage.RewardItemForInit.Add((int)theItemId);
                                var theInitCount = theConfig.InitCount;
                                bagSys.AddItem(theItemId, theInitCount, new BIHelper.ItemChangeReasonArgs()
                                {
                                    reason = BiEventBlockMatch1.Types.ItemChangeReason.Debug,
                                    data1 = theItemId.ToString(),
                                    data2 = theInitCount.ToString(),
                                });
                            }
                        }
                    }

                    var theTs = theLayout.transform.GetChild(i);
                    BuffItem theInfo = new BuffItem(theTs, theItemId, isUnlock);
                    theInfo.AddItemAction = OnAddItemForGame;
                    theInfo.UseItemAction = OnUseItemForGame;
                    buffItems.Add(theInfo);
                    FlyTarget.AddTarget(theItemId, theTs);
                }
            }
        }
        else
        {
            foreach (var item in buffItems)
            {
                FlyTarget.AddTarget(item.ItemId, item.RootTs);
                item.RefreshInfo();
            }
        }
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        UINode_PropEffect.gameObject.SetActive(false);
        UINode_UseItemBG.gameObject.SetActive(false);
        UINode_Props.gameObject.SetActive(false);
        UINode_CloseProp.gameObject.SetActive(false);

        CurUsingItem = EItemType.None;
        CurUsingItemWaitEffect = false;
        curViewData = ViewData as UIView_GameData;
        levelConfig = InGameConfigManager.GetInGameLevelCfgByLevelId(curViewData.levelId);
        //todo 1会修改成对应难度
        storySubConfig = InGameConfigManager.GetInGameStorySubConfig(levelConfig.StoryGroup);
        if (levelConfig == null)
        {
            CLog.Exception($"UIView_Game.OnOpen--关卡[{curViewData.levelId}]配置有误或不存在");
            return;
        }

        CLog.Info($"UIView_Game.OnOpen--加载关卡[{curViewData.levelId}]配置，ABTest分组[{levelConfig.AbTestGroup}]");

        var theIndex = StorageExtension.GameBlockLevelStorage.LevelPlayInfos.FindIndex(ele=>ele.levelId == curViewData.levelId);
        if (theIndex != -1)
        {
            var theInfo = StorageExtension.GameBlockLevelStorage.LevelPlayInfos[theIndex];
            theInfo.enterCount += 1;
            StorageExtension.GameBlockLevelStorage.LevelPlayInfos[theIndex] = theInfo;
        }

        //BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventFunnelEnterLevel1);

        OnOpenGame();

        // 检查是否需要触发引导
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            gameSys.HandleEndlessLayoutForGuide(step);
        }

        BIHelper.SendLevelInfo(BIHelper.ELevelInfoType.Enter, EnumBlockGameType.BlockGame_Stage,
            gameSys.PassTime, 0, 0, 0,
            gameSys.CurHardProductCount, gameSys.MaxMatchClearCount, 0, gameSys.CurProductType
            , (int)BlockPlayManager.Instance.Complexity, 0, 0, 1,
            gameSys.GenRoundIndex, (targetType == EnumTargetType.ETT_Score ? curScore : 0), gameSys.PlayerActionRecord);
    }

    protected override void OnShow()
    {
        base.OnShow();
        UINode_ClickEffect?.gameObject.SetActive(false);
        UINode_CombolingBoardEffect?.gameObject.SetActive(false); ;
        UINode_CombolingFullEffect?.gameObject.SetActive(false); ;
    }

    protected override void OnOpenAniComplete()
    {
        base.OnOpenAniComplete();

        GameGlobal.GetMgr<SoundMgr>().PlayBgMusic("bg_level_in_1");       
    }

    protected override void OnClose()
    {
        base.OnClose();
        HandleDispose();

        GameGlobal.GetMgr<SoundMgr>().PlayBgMusic("bg_level_out_1");
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();
        if (!bCanPlay) return;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        
        HandleDispose(true);
    }

    private void OnCloseBtn()
    {
        gameSys.HandleQuiteGame(gameResultType);
        Close(true, () => { EventBus.Dispatch(new ExitBlockPlayEvent(0)); });
    }

    PurchasePropViewData gameBuyPropViewData;
    UISubView_PurchaseProp uISubView_PurchaseProp;
    void HandleBuyProp(EItemType itemType)
    {
        if (gameBuyPropViewData == null)
        {
            gameBuyPropViewData = new PurchasePropViewData()
            {
                levelId = levelConfig?.Id??0,
                itemType = itemType,
                onClose = ClosePurchasePropView,
                onBuyProp = RealBuyProp,
            };
        }
        gameBuyPropViewData.itemType = itemType;

        // 关卡模式
        if (uISubView_PurchaseProp == null)
        {
            uISubView_PurchaseProp = OpenUISubView<UISubView_PurchaseProp>(UINode_Main, gameBuyPropViewData);
            uISubView_PurchaseProp.Canvas.sortingOrder = this.SortingOrder + 1;
        }
        else
        {
            uISubView_PurchaseProp.InternalShow();
        }
    }

    private void ClosePurchasePropView()
    {
        if (uISubView_PurchaseProp != null)
        {
            CloseUISubView(uISubView_PurchaseProp);
        }
    }

    void RealBuyProp(EItemType itemType,  int count)
    {

    }

    void HandleLevelInfoBI(EnumGameResultType resultType)
    {
        var theType = BIHelper.ELevelInfoType.Quit;
        switch (resultType)
        {
            case EnumGameResultType.EFST_Fail:
                theType = BIHelper.ELevelInfoType.Fail;
                break;
            case EnumGameResultType.EFST_Victory:
                theType = BIHelper.ELevelInfoType.Pass;
                break;
            default:
                break;
        }

        useItemToCount.TryGetValue(EItemType.Rotate, out var count1);
        useItemToCount.TryGetValue(EItemType.Bomb, out var count2);
        useItemToCount.TryGetValue(EItemType.Convert, out var count3);
        var theProgress = GetCurCompleteProgress(100);

        BIHelper.SendLevelInfo(theType, EnumBlockGameType.BlockGame_Stage,
            gameSys.PassTime, count1, count2, count3,
            gameSys.CurHardProductCount, gameSys.MaxMatchClearCount, theProgress, gameSys.CurProductType
            , (int)BlockPlayManager.Instance.Complexity, coinReviveCount, adReviveCount, 1,
            gameSys.GenRoundIndex, (targetType == EnumTargetType.ETT_Score ? curScore : 0),  gameSys.PlayerActionRecord);
    }


    GameExitData gameExitViewData;
    UISubView_GameExit uISubView_GameExit;

    private void RealHandleExitGame()
    {
        HandleLevelInfoBI(EnumGameResultType.EFST_NoResult);

        bool canShow = GameGlobal.GetMod<AdSys>().ShouldShowInterstitial(eAdInterstitial.QuitGameBySetting, false);
        CLog.Info($"======>:RealHandleExitGame  ShouldShowInterstitial 1:{canShow}");
        canShow &= GameGlobal.GetMod<AdSys>().UnLockInterstitial(eAdInterstitial.QuitGameBySetting);
        CLog.Info($"======>:RealHandleExitGame  ShouldShowInterstitial 2:{canShow}");
        if (canShow)
        {
            GameGlobal.GetMod<AdSys>().TryShowInterstitial(eAdInterstitial.QuitGameBySetting);
        }
        GameGlobal.GetMod<AdSys>().ResetGameWinDoubleAd();
        OnCloseBtn();
    }

    private void CloseGameExitView()
    {
        if (uISubView_GameExit != null)
        {
            CloseUISubView(uISubView_GameExit);
        }
    }

    private void HandleExitGame()
    {
        CloseGameExitView();
        RealHandleExitGame();
    }

    private void OnExitGameBtn()
    {       
        HandleGame();
    }

    void HandleGame(bool isExitGame = true)
    {
        if (gameExitViewData == null)
        {
            gameExitViewData = new GameExitData()
            {
                onClose = CloseGameExitView,
                onExit = HandleExitGame,
            };
        }
        gameExitViewData.isExit = isExitGame;
        
        // 关卡模式
        if (uISubView_GameExit == null)
        {
            uISubView_GameExit = OpenUISubView<UISubView_GameExit>(UINode_Main, gameExitViewData);
            uISubView_GameExit.Canvas.sortingOrder = this.SortingOrder + 1;
        }
        else
        {
            uISubView_GameExit.InternalShow();
        }
    }
    

    private void OnSetBtn()
    {
        var theViewData = new GameSettingData();
        theViewData.curBlockGameType = EnumBlockGameType.BlockGame_Stage;
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameSetting, theViewData);
    }

    #region Tool函数

    void OnOpenGame()
    {
        curHardCount = 0;
        bCanPlay = false;
        isPlayingVictoryAnimation = false; // 重置胜利动画标记
        blockBoardAni = null;
        isPayBoard = false;
        UINode_Content.gameObject.SetActive(false);
        UINode_Ads.gameObject.SetActive(false);
        UINode_Target.gameObject.SetActive(false);
        UINode_Tip.gameObject.SetActive(false);
        UINode_PutTip.gameObject.SetActive(false);

        OpenGameInfo theInfo = new OpenGameInfo()
        {
            enumBlockGameType = EnumBlockGameType.BlockGame_Stage,
            blockPool = UINode_BlockPool,
            gridContainer = UINode_Content,
            tipPlay = UINode_TipPlay,
            enumTargetType = targetType,
            levelId = levelConfig.Sort,
        };
        gameSys.OnOpenGame(theInfo);
        HandleInitInfo();
        curScore = StorageExtension.GameBlockLevelStorage.AchiveScore;

        showTipInfo();
        RefreshTargetInfo();

        gameResultType = EnumGameResultType.EFST_None;
        isCollectingTargets = false; // 重置收集目标标记

#if DEBUG || DEVELOPMENT_BUILD
        var thePlan = gameSys.CurBlockProductIsNative ? "Native" : "Local";
        UITxt_BlockPlan.SetText(thePlan);
        thePlan = gameSys.CurBlockIsNewOrder ? "MRL" : "LMR";
        UITxt_BlockOrder.SetText(thePlan);
#endif
    }

    void RefreshGemInfo(Transform theOrigainGem, int gemId, int gemTargetNum)
    {
        if (gemId <= 0 || gemTargetNum <= 0) return;

        var theGemId = gemId;
        Transform theGemPlay = null;
        if (!curGemInfoDic.ContainsKey(theGemId))
        {
            theGemPlay = GameObject.Instantiate(theOrigainGem);
            theGemPlay.SetParent(UILayoutH_PlayGems.transform);
            theGemPlay.localPosition = Vector3.zero;
            theGemPlay.localScale = Vector3.one;

            var curGemCount = 0;
            if (StorageExtension.GameBlockLevelStorage.CollectGemDic.ContainsKey(theGemId))
            {
                curGemCount = StorageExtension.GameBlockLevelStorage.CollectGemDic[theGemId];
            }

            curGemInfoDic[theGemId] = new GemInfo()
            {
                gemId = theGemId,
                gemCount = curGemCount,
                gemTargetCount = gemTargetNum,
                AnimatorCom = theGemPlay.GetComponent<Animator>(),
                gemTs = theGemPlay,
                gemFull = theGemPlay.Find("UIFull"),
                gemImage = theGemPlay.Find("Icon").GetComponent<Image>(),
                gemCountTxt = theGemPlay.Find("UITex_Count").GetComponent<LocalizeTextMeshProUGUI>(),
            };
        }
        else
        {
            theGemPlay = curGemInfoDic[theGemId].gemTs;
        }
        theGemPlay.gameObject.SetActive(true);
        var theIcon = curGemInfoDic[theGemId].gemImage;
        var theTxt = curGemInfoDic[theGemId].gemCountTxt;
        var iconPath = $"bm_game_gem{theGemId}";
        CoreUtils.SetImg(theIcon, Const_Common.GameAtlas, iconPath);
        var theNum = Math.Max(0, gemTargetNum - curGemInfoDic[theGemId].gemCount);
        bool isFull = theNum == 0;
        theTxt.gameObject.SetActive(!isFull);
        theTxt.SetText(theNum.ToString());
        curGemInfoDic[theGemId].gemFull.gameObject.SetActive(isFull);
    }
    
    // 新增：处理木箱和鸟的特殊目标显示
    void RefreshSpecialTargetInfo(int specialId, int curCount, int targetCount, string iconPath, int rewardCount)
    {
        if (theOrigainGem == null) theOrigainGem = UILayoutH_PlayGems.transform.Find("GemItem");
        theOrigainGem.gameObject.SetActive(false);

        Transform theSpecialPlay = null;
        if (!curGemInfoDic.ContainsKey(specialId))
        {
            theSpecialPlay = GameObject.Instantiate(theOrigainGem);
            theSpecialPlay.SetParent(UILayoutH_PlayGems.transform);
            theSpecialPlay.localPosition = Vector3.zero;
            theSpecialPlay.localScale = Vector3.one;

            curGemInfoDic[specialId] = new GemInfo()
            {
                gemId = specialId,
                gemCount = curCount,
                gemTargetCount = targetCount,
                AnimatorCom = theSpecialPlay.GetComponent<Animator>(),
                gemTs = theSpecialPlay,
                gemFull = theSpecialPlay.Find("UIFull"),
                gemImage = theSpecialPlay.Find("Icon").GetComponent<Image>(),
                gemCountTxt = theSpecialPlay.Find("UITex_Count").GetComponent<LocalizeTextMeshProUGUI>(),
            };
        }
        else
        {
            theSpecialPlay = curGemInfoDic[specialId].gemTs;
            // 更新当前计数
            GemInfo theInfo = curGemInfoDic[specialId];
            theInfo.gemCount = curCount;
            theInfo.gemTargetCount = targetCount;
            curGemInfoDic[specialId] = theInfo;
        }
        
        theSpecialPlay.gameObject.SetActive(true);
        var theIcon = curGemInfoDic[specialId].gemImage;
        var theTxt = curGemInfoDic[specialId].gemCountTxt;
        CoreUtils.SetImg(theIcon, Const_Common.GameAtlas, iconPath);
        var theNum = Math.Max(0, targetCount - curCount);
        bool isFull = theNum == 0;
        theTxt.gameObject.SetActive(!isFull);
        theTxt.SetText(theNum.ToString());
        curGemInfoDic[specialId].gemFull.gameObject.SetActive(isFull);
        
        // 处理奖励动画
        if (rewardCount > 0)
        {
            var theAnimator = curGemInfoDic[specialId].AnimatorCom;
            TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
            {
                TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                var theEffect = theAnimator.transform.Find("vfx_Target");
                if (theEffect != null)
                {
                    theEffect.gameObject.SetActive(false);
                }
            });
        }
    }

    void CheckGameVictory(bool bPlayVictory = true)
    {
        bool isWin = true;
        
        if (levelGameType == EnumLevelGameType.ELT_Score)
        {
            // 分数关：只需要分数达标
            if (curScore < targetScore)
            {
                isWin = false;
            }
        }
        else // levelGameType == EnumLevelGameType.ELT_Target
        {
            // 目标关：需要所有激活的目标都完成
            foreach (var targetTypeToCheck in activeTargetTypes)
            {
                bool targetCompleted = false;
                
                switch (targetTypeToCheck)
                {
                    case EnumTargetType.ETT_Score:
                        targetCompleted = curScore >= targetScore;
                        break;
                        
                    case EnumTargetType.ETT_WoodenCrate:
                        targetCompleted = curWoodenCrateCount >= targetWoodenCrates;
                        break;
                        
                    case EnumTargetType.ETT_Bird:
                        targetCompleted = curBirdCount >= targetBirds;
                        break;
                        
                    case EnumTargetType.ETT_Cat:
                        targetCompleted = curCatCount >= targetCats;
                        break;

                    case EnumTargetType.ETT_Leaf:
                        targetCompleted = curLeafCount >= targetLeaves;
                        break;

                    case EnumTargetType.ETT_Ice:
                        targetCompleted = curIceCount >= targetIces;
                        break;

                    case EnumTargetType.ETT_Gem:
                        targetCompleted = true; // 假设完成，除非发现未完成的
                        foreach (var kv in curGemInfoDic)
                        {
                            var theId = kv.Key;
                            var theIndex = targetGems.FindIndex(ele => ele == theId);
                            if (theIndex >= 0 && theIndex < targetGemCounts.Count)
                            {
                                var theTargetNum = targetGemCounts[theIndex];
                                if (kv.Value.gemCount < theTargetNum)
                                {
                                    targetCompleted = false;
                                    break;
                                }
                            }
                        }
                        break;
                }
                
                if (!targetCompleted)
                {
                    isWin = false;
                    break;
                }
            }
        }

        if (isWin)
        {
            HandleGameVictory();
            bCanPlay = false;
            GameUtils.SetEventSystemEnable(false);
            if (bPlayVictory) HandlePlayVictory();
        }
    }

    void HandlePlayVictory()
    {
        if (gameResultType == EnumGameResultType.EFST_Victory)
        {
            // 如果已经在播放胜利动画，则直接返回，避免重复播放
            if (isPlayingVictoryAnimation) return;

            isPlayingVictoryAnimation = true; // 标记开始播放胜利动画
            bCanPlay = false;
            GameUtils.SetEventSystemEnable(false);
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1,
            () => {
                bCanPlay = true;
                UINode_Interlude.gameObject.SetActive(true);
                TMUtility.PlayAnimation(UINode_Interlude.GetComponent<Animator>(), "UINode_Interlude_ani",
                    () =>
                    {
                        UINode_Interlude.gameObject.SetActive(false);
                        isPlayingVictoryAnimation = false; // 动画播放完成，重置标记
                    });
                GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(3.2f, () =>
                {
                    GameUtils.SetEventSystemEnable(true);
                    PlayGameOver();
                }));

            }));
        }
    }

    Transform theOrigainGem;

    void RefreshTargetInfo(List<int> achiveGems = null, int rewardScore = 0, int rewardCrates = 0, int rewardBirds = 0, int rewardCats = 0)
    {
        UINode_Target.gameObject.SetActive(true);

        // 根据关卡类型显示UI
        if (levelGameType == EnumLevelGameType.ELT_Score)
        {
            // 分数关：只显示分数UI
            UINode_GemTarget.gameObject.SetActive(false);
            UISlider_ScoreTarget.gameObject.SetActive(true);

            UISlider_ScoreTarget.value = (float)curScore / targetScore;
            UITxt_ProgressEx.SetText(curScore.ToString());
            UITxt_End.SetText(targetScore.ToString());

            if (rewardScore > 0)
            {
                int tempScore = curScore;
                UIUtils.UpdateTextAnimation(UITxt_ProgressEx, rewardScore, curScore, 0.85f, () =>
                {
                    if (UITxt_ProgressEx == null) return;
                    UITxt_ProgressEx.SetText(tempScore.ToString());
                });
            }
        }
        else 
        {
            // 目标关：显示所有激活的目标类型
            bool hasNonGemTarget = activeTargetTypes.Any(t => t != EnumTargetType.ETT_Gem);
            UINode_GemTarget.gameObject.SetActive(true);
            UISlider_ScoreTarget.gameObject.SetActive(false);

            // 处理各种目标类型
            foreach (var targetTypeToShow in activeTargetTypes)
            {
                switch (targetTypeToShow)
                {
                    case EnumTargetType.ETT_WoodenCrate:
                        RefreshSpecialTargetInfo(SPECIAL_ID_WOODEN_CRATE, curWoodenCrateCount, targetWoodenCrates, "bm_game_big_wood", rewardCrates);
                        break;

                    case EnumTargetType.ETT_Bird:
                        RefreshSpecialTargetInfo(SPECIAL_ID_BIRD, curBirdCount, targetBirds, "bm_game_big_bird", rewardBirds);
                        break;

                    case EnumTargetType.ETT_Cat:
                        RefreshSpecialTargetInfo(SPECIAL_ID_CAT, curCatCount, targetCats, "bm_game_big_cat", 0);
                        break;

                    case EnumTargetType.ETT_Leaf:
                        RefreshSpecialTargetInfo(SPECIAL_ID_LEAF, curLeafCount, targetLeaves, "bm_game_overlay_Block1", 0);
                        break;

                    case EnumTargetType.ETT_Ice:
                        // 冰块目标显示使用3层冰块图标
                        RefreshSpecialTargetInfo(SPECIAL_ID_ICE, curIceCount, targetIces, "bm_game_Ice3", 0);
                        break;

                    case EnumTargetType.ETT_Gem:
                        if (targetGems.Count != targetGemCounts.Count)
                        {
                            CLog.Error($"宝石目标配置有误");
                            return;
                        }

                        if (theOrigainGem == null) theOrigainGem = UILayoutH_PlayGems.transform.Find("GemItem");
                        theOrigainGem.gameObject.SetActive(false);

                        if (achiveGems == null)
                        {
                            var theList = curGemInfoDic.Keys.ToList();
                            foreach (var theGemId in theList)
                            {
                                var curGemCount = 0;
                                if (StorageExtension.GameBlockLevelStorage.CollectGemDic.ContainsKey(theGemId))
                                {
                                    curGemCount = StorageExtension.GameBlockLevelStorage.CollectGemDic[theGemId];
                                }

                                GemInfo theInfo = curGemInfoDic[theGemId];
                                theInfo.gemCount = curGemCount;
                                theInfo.gemTs.gameObject.SetActive(false);
                                curGemInfoDic[theGemId] = theInfo;
                            }

                            int theCount = targetGems.Count;
                            for (int i = 0; i < theCount; i++)
                            {
                                var theId = targetGems[i];
                                var theNum = targetGemCounts[i];
                                RefreshGemInfo(theOrigainGem, theId, theNum);
                            }
                        }
                        else
                        {
                            foreach (var theId in achiveGems)
                            {
                                var theIndex = targetGems.FindIndex(ele => ele == theId);
                                if (theIndex >= 0 && theIndex < targetGemCounts.Count)
                                {
                                    var theNum = targetGemCounts[theIndex];
                                    RefreshGemInfo(theOrigainGem, theId, theNum);
                                }
                            }
                        }
                        break;
                }
            }
        }
    }

    #endregion

    #region 分数更新

    float secondLogicValue = 0;

    void HandleBlockPutDown(EventBlockPutDownInfo evt)
    {
        // 如果正在播放引导完成特效，则不处理块放下逻辑
        if (isGuideCompletingWithEffect)
        {
            CLog.Info("引导完成特效播放中，忽略块放下事件");
            return;
        }
        
        if (gameResultType == EnumGameResultType.EFST_None)
            gameResultType = EnumGameResultType.EFST_NoResult;

        RefreshComplexlnfo();

        if (CurUsingItem == EItemType.Bomb)
        {
            HandleRealUseItem();
            UserBombEffect();
            StopGuide();
        }

        // 引导相关逻辑
        StopMainGuide();
        TryShowNextStep_ForClearSceneGuide();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            GameUtils.SetEventSystemEnable(false);
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1f,
                () =>
                {
                    GameUtils.SetEventSystemEnable(true);
                    gameSys.HandleEndlessLayoutForGuide(step);
                    gameSys.HandleEndlessBlockForGuide(step);
                    PlayMainGuide();
                }));
        }

        var theComboNumber = evt.combolNum;
        var theMatchCount = evt.matchCount;
        if (theMatchCount >= 3)
        {
            UINode_CombolingBoardEffect.gameObject.SetActive(false);
            UINode_CombolingFullEffect.gameObject.SetActive(false);
            UINode_CombolingBoardEffect.gameObject.SetActive(true);
            UINode_CombolingFullEffect.gameObject.SetActive(true);
        }

        if (theMatchCount > 0)
        {
            var theBlockCount = BlockPlayManager.Instance.GetBlockCount();
            if (theBlockCount <= 0)
            {
                //清屏
                PlayBoard();
            }
        }

        // 震屏效果
        if (theMatchCount >= 3)
        {
            ScreenVibrationManager.Instance.SetVibration(false, 0, ViewRootRect,shakeAnim);
        }
    }

    void UserBombEffect()
    {
        ScreenVibrationManager.Instance.SetVibration(false, 0, ViewRootRect,shakeAnim);
        UINode_CombolingBoardEffect.gameObject.SetActive(false);
        UINode_CombolingFullEffect.gameObject.SetActive(false);
        UINode_CombolingBoardEffect.gameObject.SetActive(true);
        UINode_CombolingFullEffect.gameObject.SetActive(true);
    }

    void HandleRewardGem(EventGameGemAchive evt)
    {
        var theList = evt.gemList;
        var theCount = theList?.Count ?? 0;
        if (theCount <= 0) return;

        // 标记开始收集目标
        isCollectingTargets = true;
        
        // 使用DOTween创建同时出现、依次飞行的动画，动画完成后更新UI
        PlayGemFlyAnimationSequence(theList);
    }

    void HandleRewardScore(EventGameScoreChange evt)
    {
        int preScore = evt.current;
        int score = evt.delta;
        if (score <= 0) return;
        
        // 检查是否处于引导状态，如果是则不加分
        if (isGuideActive)
        {
            CLog.Info($"引导期间消除方块，不加分。");
            return;
        }
        
        curScore += score;

        if (targetType == EnumTargetType.ETT_Score)
        {
            RefreshTargetInfo(null, score);
            CheckGameVictory();
        }
    }

    // 新增：处理木箱收集事件
    void HandleRewardWoodenCrate(EventGameWoodenCrateAchive evt)
    {
        // 标记开始收集目标
        isCollectingTargets = true;
        
        // 添加到批量飞行队列，动画完成后更新计数
        if (curGemInfoDic.ContainsKey(SPECIAL_ID_WOODEN_CRATE))
        {
            var theInfo = curGemInfoDic[SPECIAL_ID_WOODEN_CRATE];
            AddToBatchFlyQueue(SPECIAL_ID_WOODEN_CRATE, evt.sourcePosition, theInfo.gemImage.transform.position,
                theInfo.gemImage.transform, () => {
                    if (this.GO == null) return;

                    // 在动画完成时更新计数
                    curWoodenCrateCount += evt.crateCount;
                    if (curWoodenCrateCount > targetWoodenCrates) curWoodenCrateCount = targetWoodenCrates;

                    // 更新UI显示
                    RefreshSpecialTargetInfo(SPECIAL_ID_WOODEN_CRATE, curWoodenCrateCount, targetWoodenCrates, "bm_game_big_wood", evt.crateCount);

                    // 播放目标UI动画
                    var theAnimator = theInfo.AnimatorCom;
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                    {
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                        var theEffect = theAnimator.transform.Find("vfx_Target");
                        if (theEffect != null)
                        {
                            theEffect.gameObject.SetActive(false);
                        }
                    });

                    // 播放音效
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");

                    // 检查游戏胜利
                    CheckGameVictory(curWoodenCrateCount >= targetWoodenCrates);
                    
                    // 标记收集完成
                    isCollectingTargets = false;
                });
        }
    }

    // 新增：处理鸟收集事件，使用真正的Bird预制体飞行动画
    void HandleRewardBird(EventGameBirdAchive evt)
    {
        // 标记开始收集目标
        isCollectingTargets = true;
        
        // 添加到批量飞行队列，动画完成后更新计数
        if (curGemInfoDic.ContainsKey(SPECIAL_ID_BIRD))
        {
            var theInfo = curGemInfoDic[SPECIAL_ID_BIRD];
            AddToBatchFlyQueue(SPECIAL_ID_BIRD, evt.sourcePosition, theInfo.gemImage.transform.position,
                theInfo.gemImage.transform, () => {
                    if (this.GO == null) return;

                    // 在动画完成时更新计数
                    curBirdCount += evt.birdCount;
                    if (curBirdCount > targetBirds) curBirdCount = targetBirds;

                    // 更新UI显示
                    RefreshSpecialTargetInfo(SPECIAL_ID_BIRD, curBirdCount, targetBirds, "bm_game_big_bird", evt.birdCount);

                    // 播放目标UI动画
                    var theAnimator = theInfo.AnimatorCom;
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                    {
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                        var theEffect = theAnimator.transform.Find("vfx_Target");
                        if (theEffect != null)
                        {
                            theEffect.gameObject.SetActive(false);
                        }
                    });

                    // 播放音效
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");

                    // 检查游戏胜利
                    CheckGameVictory(curBirdCount >= targetBirds);
                    
                    // 标记收集完成
                    isCollectingTargets = false;
                });
        }
    }
    
    /// <summary>
    /// 创建Bird飞行动画（复用现有系统）
    /// </summary>
    /// <param name="startPos">起始位置</param>
    /// <param name="endPos">目标位置</param>
    /// <param name="onComplete">完成回调</param>
    /// <param name="onSoundPlay">音效播放回调</param>
    private void CreateBirdFlyAnimation(Vector3 startPos, Vector3 endPos, Action onComplete = null, Action onSoundPlay = null)
    {
        // 1. 获取Bird预制体并实例化
        var birdPrefabRef = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>("Bird");
        if (birdPrefabRef == null)
        {
            CLog.Error("Bird预制体未找到，请检查资源路径：Assets/Res/Game/Prefabs/Effect/Bird.prefab");
            onComplete?.Invoke();
            return;
        }
        
        // 2. 实例化Bird对象到UI画布上
        var birdInstance = GameObject.Instantiate(birdPrefabRef.GetInstance(UINode_Main.gameObject), UINode_Main);
        if (birdInstance == null)
        {
            CLog.Error("Bird实例化失败");
            onComplete?.Invoke();
            return;
        }
        
        // 设置初始位置和状态
        birdInstance.transform.position = startPos;
        birdInstance.SetActive(true);
        
        // 3. 播放Spine fly动画
        SetupBirdFlyAnimation(birdInstance);
        
        // 4. 使用FlySys的BirdFly方法执行飞行
        GameGlobal.GetMod<FlySys>().BirdFly(
            birdInstance,
            startPos,
            endPos,
            false,                     
            0.65f,                     
            action: () => {
                // 飞行完成后清理
                CleanupBirdInstance(birdInstance);
                onComplete?.Invoke();
            },
            playSound: onSoundPlay
        );
    }
    
    /// <summary>
    /// 设置Bird的Spine飞行动画
    /// </summary>
    /// <param name="birdGameObject">Bird游戏对象</param>
    private void SetupBirdFlyAnimation(GameObject birdGameObject)
    {
        // 尝试通过SkeletonGraphic播放动画（UI版本）
        var skeletonGraphic = birdGameObject.GetComponentInChildren<Spine.Unity.SkeletonGraphic>();
        if (skeletonGraphic != null)
        {
            try
            {
                skeletonGraphic.AnimationState.SetAnimation(0, "fly", true);
                CLog.Info("Bird SkeletonGraphic fly动画已开始播放");
                return;
            }
            catch (System.Exception e)
            {
                CLog.Warning($"播放SkeletonGraphic动画失败: {e.Message}");
            }
        }
        
        // 尝试通过SpineAnimationEvent组件播放动画
        var spineEvent = birdGameObject.GetComponentInChildren<Spine.Unity.SpineAnimationEvent>();
        if (spineEvent != null)
        {
            try
            {
                spineEvent.PlaySpineAnimationLoop("fly", true);
                CLog.Info("Bird SpineAnimationEvent fly动画已开始播放");
                return;
            }
            catch (System.Exception e)
            {
                CLog.Warning($"播放SpineAnimationEvent动画失败: {e.Message}");
            }
        }
        
        CLog.Warning("未找到可用的Spine动画组件，Bird将只进行位移动画");
    }
    
    /// <summary>
    /// 清理Bird实例
    /// </summary>
    /// <param name="birdGameObject">Bird游戏对象</param>
    private void CleanupBirdInstance(GameObject birdGameObject)
    {
        if (birdGameObject != null)
        {
            // 停止所有动画
            var skeletonGraphic = birdGameObject.GetComponentInChildren<Spine.Unity.SkeletonGraphic>();
            if (skeletonGraphic != null)
            {
                try
                {
                    skeletonGraphic.AnimationState.ClearTracks();
                }
                catch (System.Exception e)
                {
                    CLog.Warning($"清理SkeletonGraphic动画失败: {e.Message}");
                }
            }
            
            var skeletonAnimation = birdGameObject.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
            if (skeletonAnimation != null)
            {
                try
                {
                    skeletonAnimation.state.ClearTracks();
                }
                catch (System.Exception e)
                {
                    CLog.Warning($"清理SkeletonAnimation动画失败: {e.Message}");
                }
            }
            
            // 销毁对象
            GameObject.Destroy(birdGameObject);
        }
    }

    // 新增：处理猫收集事件
    void HandleRewardCat(EventGameCatAchive evt)
    {
        // 标记开始收集目标
        isCollectingTargets = true;
        
        // 添加到批量飞行队列，动画完成后更新计数
        if (curGemInfoDic.ContainsKey(SPECIAL_ID_CAT))
        {
            var theInfo = curGemInfoDic[SPECIAL_ID_CAT];
            AddToBatchFlyQueue(SPECIAL_ID_CAT, evt.sourcePosition, theInfo.gemImage.transform.position,
                theInfo.gemImage.transform, () => {
                    if (this.GO == null) return;

                    // 在动画完成时更新计数
                    curCatCount += evt.catCount;
                    CLog.Info($"curCatCount: {curCatCount}");
                    if (curCatCount > targetCats) curCatCount = targetCats;

                    // 更新UI显示
                    RefreshSpecialTargetInfo(SPECIAL_ID_CAT, curCatCount, targetCats, "bm_game_big_cat", evt.catCount);

                    // 播放目标UI动画
                    var theAnimator = theInfo.AnimatorCom;
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                    {
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                        var theEffect = theAnimator.transform.Find("vfx_Target");
                        if (theEffect != null)
                        {
                            theEffect.gameObject.SetActive(false);
                        }
                    });

                    // 播放音效
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");

                    // 检查游戏胜利
                    CheckGameVictory(curCatCount >= targetCats);
                    
                    // 标记收集完成
                    isCollectingTargets = false;
                });
        }
    }

    // 新增：处理树叶收集事件
    void HandleRewardLeaf(EventGameLeafAchive evt)
    {
        // 标记开始收集目标
        isCollectingTargets = true;

        // 添加到批量飞行队列，动画完成后更新计数
        if (curGemInfoDic.ContainsKey(SPECIAL_ID_LEAF))
        {
            var theInfo = curGemInfoDic[SPECIAL_ID_LEAF];
            AddToBatchFlyQueue(SPECIAL_ID_LEAF, evt.sourcePosition, theInfo.gemImage.transform.position,
                theInfo.gemImage.transform, () => {
                    if (this.GO == null) return;

                    // 在动画完成时更新计数
                    curLeafCount += evt.leafCount;
                    if (curLeafCount > targetLeaves) curLeafCount = targetLeaves;

                    // 更新UI显示
                    RefreshSpecialTargetInfo(SPECIAL_ID_LEAF, curLeafCount, targetLeaves, "bm_game_overlay_Block1", evt.leafCount);

                    // 播放目标UI动画
                    var theAnimator = theInfo.AnimatorCom;
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                    {
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                        var theEffect = theAnimator.transform.Find("vfx_Target");
                        if (theEffect != null)
                        {
                            theEffect.gameObject.SetActive(false);
                        }
                    });

                    // 播放音效
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");

                    // 检查游戏胜利
                    CheckGameVictory(curLeafCount >= targetLeaves);

                    // 标记收集完成
                    isCollectingTargets = false;
                });
        }
    }

    // 新增：处理冰块收集事件
    void HandleRewardIce(EventGameIceAchive evt)
    {
        // 标记开始收集目标
        isCollectingTargets = true;

        // 添加到批量飞行队列，动画完成后更新计数
        if (curGemInfoDic.ContainsKey(SPECIAL_ID_ICE))
        {
            var theInfo = curGemInfoDic[SPECIAL_ID_ICE];
            AddToBatchFlyQueue(SPECIAL_ID_ICE, evt.sourcePosition, theInfo.gemImage.transform.position,
                theInfo.gemImage.transform, () => {
                    if (this.GO == null) return;

                    // 在动画完成时更新计数
                    curIceCount += evt.iceCount;
                    if (curIceCount > targetIces) curIceCount = targetIces;

                    // 更新UI显示（使用3层冰块图标）
                    RefreshSpecialTargetInfo(SPECIAL_ID_ICE, curIceCount, targetIces, "bm_game_Ice3", evt.iceCount);

                    // 播放目标UI动画
                    var theAnimator = theInfo.AnimatorCom;
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                    {
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                        var theEffect = theAnimator.transform.Find("vfx_Target");
                        if (theEffect != null)
                        {
                            theEffect.gameObject.SetActive(false);
                        }
                    });

                    // 播放音效
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");

                    // 检查游戏胜利
                    CheckGameVictory(curIceCount >= targetIces);

                    // 标记收集完成
                    isCollectingTargets = false;
                });
        }
    }

    /// <summary>
    /// 创建Cat飞行动画（类似Bird飞行动画）
    /// </summary>
    /// <param name="startPos">起始位置</param>
    /// <param name="endPos">目标位置</param>
    /// <param name="onComplete">完成回调</param>
    /// <param name="onSoundPlay">音效播放回调</param>
    private void CreateCatFlyAnimation(Vector3 startPos, Vector3 endPos, Action onComplete = null, Action onSoundPlay = null)
    {
        // 尝试获取Cat预制体
        var catPrefabRef = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>("Cat");
        if (catPrefabRef == null)
        {
            CLog.Error("Cat预制体未找到，请检查资源路径：Assets/Res/Game/Prefabs/Effect/Cat.prefab");
            // 使用备用方案：UI图像飞行
            if (curGemInfoDic.ContainsKey(SPECIAL_ID_CAT))
            {
                var theInfo = curGemInfoDic[SPECIAL_ID_CAT];
                var fallbackObj = GameGlobal.GetMod<FlySys>().GetFlyClone(theInfo.gemImage.transform);
                GameGlobal.GetMod<FlySys>().NormalFly(fallbackObj.gameObject, startPos, endPos, false, 0.65f, 
                    action: () => {
                        GameObject.Destroy(fallbackObj.gameObject);
                        onComplete?.Invoke();
                    },
                    playSound: onSoundPlay);
            }
            else
            {
                onComplete?.Invoke();
            }
            return;
        }
        
        // 实例化Cat对象到UI画布上
        var catInstance = GameObject.Instantiate(catPrefabRef.GetInstance(UINode_Main.gameObject), UINode_Main);
        if (catInstance == null)
        {
            CLog.Error("Cat实例化失败");
            onComplete?.Invoke();
            return;
        }
        
        // 设置初始位置和状态
        catInstance.transform.position = startPos;
        catInstance.SetActive(true);
        
        // 播放Cat动画
        SetupCatFlyAnimation(catInstance);
        
        // 使用FlySys的NormalFly方法执行飞行
        GameGlobal.GetMod<FlySys>().NormalFly(
            catInstance,
            startPos,
            endPos,
            false,                     // 不使用额外飞行特效
            0.65f,                     // 飞行时间
            action: () => {
                // 飞行完成后清理
                CleanupCatInstance(catInstance);
                onComplete?.Invoke();
            },
            playSound: onSoundPlay
        );
    }
    
    /// <summary>
    /// 设置Cat的动画
    /// </summary>
    /// <param name="catGameObject">Cat游戏对象</param>
    private void SetupCatFlyAnimation(GameObject catGameObject)
    {
        // 尝试通过SkeletonGraphic播放动画（UI版本）
        var skeletonGraphic = catGameObject.GetComponentInChildren<Spine.Unity.SkeletonGraphic>();
        if (skeletonGraphic != null)
        {
            try
            {
                skeletonGraphic.AnimationState.SetAnimation(0, "idle", true);
                CLog.Info("Cat SkeletonGraphic idle动画已开始播放");
                return;
            }
            catch (System.Exception e)
            {
                CLog.Warning($"播放Cat SkeletonGraphic动画失败: {e.Message}");
            }
        }
        
        // 尝试通过SpineAnimationEvent组件播放动画
        var spineEvent = catGameObject.GetComponentInChildren<Spine.Unity.SpineAnimationEvent>();
        if (spineEvent != null)
        {
            try
            {
                spineEvent.PlaySpineAnimationLoop("idle", true);
                CLog.Info("Cat SpineAnimationEvent idle动画已开始播放");
                return;
            }
            catch (System.Exception e)
            {
                CLog.Warning($"播放Cat SpineAnimationEvent动画失败: {e.Message}");
            }
        }
        
        CLog.Info("未找到可用的Spine动画组件，Cat将只进行位移动画");
    }
    
    /// <summary>
    /// 清理Cat实例
    /// </summary>
    /// <param name="catGameObject">Cat游戏对象</param>
    private void CleanupCatInstance(GameObject catGameObject)
    {
        if (catGameObject != null)
        {
            // 停止所有动画
            var skeletonGraphic = catGameObject.GetComponentInChildren<Spine.Unity.SkeletonGraphic>();
            if (skeletonGraphic != null)
            {
                try
                {
                    skeletonGraphic.AnimationState.ClearTracks();
                }
                catch (System.Exception e)
                {
                    CLog.Warning($"清理Cat SkeletonGraphic动画失败: {e.Message}");
                }
            }
            
            var skeletonAnimation = catGameObject.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
            if (skeletonAnimation != null)
            {
                try
                {
                    skeletonAnimation.state.ClearTracks();
                }
                catch (System.Exception e)
                {
                    CLog.Warning($"清理Cat SkeletonAnimation动画失败: {e.Message}");
                }
            }
            
            // 销毁对象
            GameObject.Destroy(catGameObject);
        }
    }

    #endregion

    #region 广告复活相关

    public int CurReviveCount => (coinReviveCount + adReviveCount);

    int coinReviveCount = 0;
    int adReviveCount = 0;
    public bool HandleRevive(int payCoin = 0)
    {
        if (payCoin > 0)
        {
            coinReviveCount++;
        }
        else
        {
            adReviveCount++;
        }
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventReliveSuccess, payCoin.ToString());
        //关卡模式无限复活
        var theParam = GenerateParam.GetNormalParam(EnumBlockProductType.EBPT_Revive);
        gameSys.GenerateBlockGo(theParam);
        
        // 重置收集目标标记
        isCollectingTargets = false;
        
        return true;
    }

    #endregion

    #region 展示开始时的提示信息

    Animator blockBoardAni;
    bool isPayBoard = false;

    void PlayBoard()
    {
        if (isPayBoard) return;
        isPayBoard = true;
        bCanPlay = false;
        GameUtils.SetEventSystemEnable(false);

        if (blockBoardAni == null)
        {
            var theNode = UINode_Main.Find("VFX_Block");
            blockBoardAni = theNode?.GetComponent<Animator>();
        }

        if (blockBoardAni)
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_1");
            blockBoardAni.gameObject.SetActive(true);
            blockBoardAni.PlayAnim("VFX_Block", AfterPlayBoard);
        }
        else
        {
            AfterPlayBoard();
        }
    }

    void AfterPlayBoard()
    {
        if (blockBoardAni) blockBoardAni.gameObject.SetActive(false);
        UINode_Content.gameObject.SetActive(true);
        bCanPlay = true;
        isPayBoard = false;
        GameUtils.SetEventSystemEnable(true);
        
        // 检查是否需要播放主要引导
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            PlayMainGuide();
        }
        
        TryTriggerGuide();
    }

    GameButton tipBtn;
    List<GemInfo> theTipGems = new List<GemInfo>();

    void CreateTipGemItem(int index, int gemId, int curCount, int targetCount, string iconPath)
    {
        var theOrigainGem = UILayoutH_Gems.transform.Find("GemItem");
        var gemListNum = theTipGems.Count;
        
        GemInfo theInfo;
        Transform theGemPlay = null;
        if (index >= gemListNum)
        {
            theGemPlay = GameObject.Instantiate(theOrigainGem);
            theGemPlay.SetParent(UILayoutH_Gems.transform);
            theGemPlay.localPosition = Vector3.zero;
            theGemPlay.localScale = Vector3.one;
            theGemPlay.gameObject.SetActive(true);
            theInfo.gemTs = theGemPlay;
            theInfo.AnimatorCom = theGemPlay.GetComponent<Animator>();
            theInfo.gemFull = null;
            theInfo.gemImage = theGemPlay.Find("Icon").GetComponent<Image>();
            theInfo.gemCountTxt = theGemPlay.Find("TextCount").GetComponent<LocalizeTextMeshProUGUI>();
            theInfo.gemId = gemId;
            theInfo.gemCount = curCount;
            theInfo.gemTargetCount = targetCount;
            theTipGems.Add(theInfo);
        }
        else
        {
            theInfo = theTipGems[index];
            theInfo.gemId = gemId;
            theInfo.gemCount = curCount;
            theInfo.gemTargetCount = targetCount;
            theTipGems[index] = theInfo;
            theGemPlay = theInfo.gemTs;
        }

        theGemPlay.gameObject.SetActive(true);
        var theIcon = theInfo.gemImage;
        var theTxt = theInfo.gemCountTxt;
        var theNum = Math.Max(0, theInfo.gemTargetCount - theInfo.gemCount);
        CoreUtils.SetImg(theIcon, Const_Common.GameAtlas, iconPath);
        theTxt.SetText(theNum.ToString());
    }

    void showTipInfo()
    {
        var curLevelInfo = StorageExtension.GameBlockLevelStorage.CurLevelInfo;
        int tipRankRadio = curLevelInfo.rankRadio;
        UINode_Tip.gameObject.SetActive(true);
        UINode_TipGem.gameObject.SetActive(targetType == EnumTargetType.ETT_Gem ||
                                           targetType == EnumTargetType.ETT_WoodenCrate ||
                                           targetType == EnumTargetType.ETT_Bird||
                                           targetType == EnumTargetType.ETT_Cat||
                                           targetType == EnumTargetType.ETT_Leaf||
                                           targetType == EnumTargetType.ETT_Ice);
        UINode_TipGold.gameObject.SetActive(targetType == EnumTargetType.ETT_Score);

        // 对于特殊目标关，确保顶部UI已经初始化
        if (targetType == EnumTargetType.ETT_WoodenCrate)
        {
            RefreshSpecialTargetInfo(SPECIAL_ID_WOODEN_CRATE, curWoodenCrateCount, targetWoodenCrates, "bm_game_big_wood", 0);
        }
        else if (targetType == EnumTargetType.ETT_Bird)
        {
            RefreshSpecialTargetInfo(SPECIAL_ID_BIRD, curBirdCount, targetBirds, "bm_game_big_bird", 0);
        }
        else if (targetType == EnumTargetType.ETT_Cat)
        {
            RefreshSpecialTargetInfo(SPECIAL_ID_CAT, curCatCount, targetCats, "bm_game_big_cat", 0);
        }
        else if (targetType == EnumTargetType.ETT_Leaf)
        {
            RefreshSpecialTargetInfo(SPECIAL_ID_LEAF, curLeafCount, targetLeaves, "bm_game_overlay_Block1", 0);
        }
        else if (targetType == EnumTargetType.ETT_Ice)
        {
            // 冰块目标显示使用3层冰块图标
            RefreshSpecialTargetInfo(SPECIAL_ID_ICE, curIceCount, targetIces, "bm_game_Ice3", 0);
        }

        if (targetType == EnumTargetType.ETT_Score)
        {
            UITxt_CoinTarget.SetText(targetScore.ToString());

            if (tipRankRadio <= 0)
            {
                tipRankRadio = UnityEngine.Random.Range(6000, 9001);
                curLevelInfo.rankRadio = tipRankRadio;
                StorageExtension.GameBlockLevelStorage.CurLevelInfo = curLevelInfo;
            }
            var theValue = (float)tipRankRadio / 100;

            var theContent =
                LocalizationManager.Instance.GetLocalizedStringWithFormat("&key.UI_level_player_tips_1", theValue.ToString());
            UITxt_Prompt.SetText(theContent);
        }
        else
        {
            // 目标关
            var theOrigainGem = UILayoutH_Gems.transform.Find("GemItem");
            if (theOrigainGem != null)
            {
                theOrigainGem.gameObject.SetActive(false);
            }

            var gemListNum = theTipGems.Count;
            foreach (var gem in theTipGems)
            {
                gem.gemTs.gameObject.SetActive(false);
            }

            // 处理所有激活的目标类型
            int itemIndex = 0;
            foreach (var activeTargetType in activeTargetTypes)
            {
                switch (activeTargetType)
                {
                    case EnumTargetType.ETT_Gem:
                        // 处理宝石目标
                        for (int i = 0; i < targetGems.Count; i++)
                        {
                            var theGemId = targetGems[i];
                            var curGemCount = 0;
                            if (StorageExtension.GameBlockLevelStorage.CollectGemDic.ContainsKey(theGemId))
                            {
                                curGemCount = StorageExtension.GameBlockLevelStorage.CollectGemDic[theGemId];
                            }
                            CreateTipGemItem(itemIndex, theGemId, curGemCount, targetGemCounts[i], $"bm_game_gem{theGemId}");
                            itemIndex++;
                        }
                        break;
                        
                    case EnumTargetType.ETT_WoodenCrate:
                        CreateTipGemItem(itemIndex, SPECIAL_ID_WOODEN_CRATE, curWoodenCrateCount, targetWoodenCrates, "bm_game_big_wood");
                        itemIndex++;
                        break;
                        
                    case EnumTargetType.ETT_Bird:
                        CreateTipGemItem(itemIndex, SPECIAL_ID_BIRD, curBirdCount, targetBirds, "bm_game_big_bird");
                        itemIndex++;
                        break;
                        
                    case EnumTargetType.ETT_Cat:
                        CreateTipGemItem(itemIndex, SPECIAL_ID_CAT, curCatCount, targetCats, "bm_game_big_cat");
                        itemIndex++;
                        break;

                    case EnumTargetType.ETT_Leaf:
                        CreateTipGemItem(itemIndex, SPECIAL_ID_LEAF, curLeafCount, targetLeaves, "bm_game_overlay_Block1");
                        itemIndex++;
                        break;

                    case EnumTargetType.ETT_Ice:
                        // 冰块目标显示使用3层冰块图标
                        CreateTipGemItem(itemIndex, SPECIAL_ID_ICE, curIceCount, targetIces, "bm_game_Ice3");
                        itemIndex++;
                        break;
                }
            }
            if (tipRankRadio <= 0)
            {
                tipRankRadio = UnityEngine.Random.Range(2000, 5001);
                curLevelInfo.rankRadio = tipRankRadio;
                StorageExtension.GameBlockLevelStorage.CurLevelInfo = curLevelInfo;
            }
            var theValue = (float)tipRankRadio / 100;
            var theContent =
                                 LocalizationManager.Instance.GetLocalizedStringWithFormat("&key.UI_level_player_tips_2", theValue.ToString());
            UITxt_Prompt_UINode_TipGem.SetText(theContent);
        }

        if (tipBtn == null)
        {
            tipBtn = UINode_Tip.GetComponent<GameButton>();
            tipBtn.onClick.AddListener(() =>
            {
                UIBtn_Close.enabled = false;
                UINode_Tip.gameObject.SetActive(false);
                if (levelGameType == EnumLevelGameType.ELT_Target)
                {
                    HandlePlayGems(HandleAfterShowTip);
                }
                else
                {
                    if (UITxt_CoinTarget == null || UISlider_ScoreTarget == null) return;
                    var theTs = UITxt_CoinTarget.transform.parent;
                    var flyObj = GameGlobal.GetMod<FlySys>().GetFlyClone(theTs);
                    var theTarget = UISlider_ScoreTarget.transform.Find("End");
                    // GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3");
                    GameGlobal.GetMod<FlySys>().NormalFly(flyObj.gameObject, theTs.position, theTarget.position,
                        false, action: () =>
                        {
                            if (theTarget != null)
                            {
                                var theEffect = theTarget.Find("Icon/vfx_Target");
                                if (theEffect != null)
                                {
                                    
                                    theEffect.gameObject.SetActive(true);
                                    GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1f,
                                      () =>
                                      {
                                          if (theEffect != null) theEffect.gameObject.SetActive(false);
                                      }));
                                }
                            }
                            flyObj.gameObject.SetActive(false);
                            GameObject.Destroy(flyObj.gameObject);
                            HandleAfterShowTip();
                        },playSound:() =>GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_3"));
                }

            });
        }
    }

    private void TryTriggerGuide()
    {
        // 检查道具引导开关
        if (!ENABLE_PROP_GUIDE)
        {
            return;
        }
        
        var guide = GameGlobal.GetMod<GuideSys>();
        if (null == levelConfig)
        {
            return;
        }
        if (!guide.IsFinished("GUIDE_301") && levelConfig.Id ==2)
        {
            guide.Trigger(GuideTrigger.Rotate, "1");


            //todo 固定三个块
        }
        else if (!guide.IsFinished("GUIDE_303") && levelConfig.Id ==5)
        {
            guide.Trigger(GuideTrigger.Bomb, "1");
        }
        else if (!guide.IsFinished("GUIDE_305") && levelConfig.Id ==8)
        {
            guide.Trigger(GuideTrigger.Convert, "1");
        }
    }

    void HandleAfterShowTip()
    {
        if (this.GO == null) return;
        UIBtn_Close.enabled = true;
        PlayBoard();

        // if (NeedGuid301)
        // {
        //     gameSys.HandleEndlessBlockForGuide301();
        // }
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            gameSys.HandleEndlessBlockForGuide(step);
        }
        else
        {
            gameSys.HandleBlockProduct();
        }
    }

    void HandlePlayGems(Action callBack = null)
    {
        // 构建要处理的目标列表
        // 现在支持多种目标类型的关卡
        List<int> targetIds = new List<int>();
        foreach (var activeTargetType in activeTargetTypes)
        {
            switch (activeTargetType)
            {
                case EnumTargetType.ETT_Gem:
                    targetIds.AddRange(targetGems);
                    break;
                case EnumTargetType.ETT_WoodenCrate:
                    targetIds.Add(SPECIAL_ID_WOODEN_CRATE);
                    break;
                case EnumTargetType.ETT_Bird:
                    targetIds.Add(SPECIAL_ID_BIRD);
                    break;
                case EnumTargetType.ETT_Cat:
                    targetIds.Add(SPECIAL_ID_CAT);
                    break;
                case EnumTargetType.ETT_Leaf:
                    targetIds.Add(SPECIAL_ID_LEAF);
                    break;
                case EnumTargetType.ETT_Ice:
                    targetIds.Add(SPECIAL_ID_ICE);
                    break;
            }
        }

        var theCount = targetIds.Count;
        var curIndex = 0;
        GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_2");
        
        for (int i = 0; i < theCount; i++)
        {
            var theTargetId = targetIds[i];
            bool theRes = curGemInfoDic.TryGetValue(theTargetId, out var tempInfo);
            if (theRes == false)
            {
                Debug.LogError($"目标{theTargetId} 在curGemInfoDic中不存在");
                break;
            }

            var tipInfo = theTipGems[i];
            var theGemTs = tipInfo.gemImage.transform;
            var flyObj = GameGlobal.GetMod<FlySys>().GetFlyClone(theGemTs);
            GameGlobal.GetMod<FlySys>().NormalFly(flyObj.gameObject, theGemTs.position,
                tempInfo.gemImage.transform.position,
                false, delayTime: i * 0.2f, action: () =>
                {
                    if (curGemInfoDic.ContainsKey(theTargetId) && curGemInfoDic[theTargetId].gemTs != null)
                    {
                        var theAnimator = curGemInfoDic[theTargetId].AnimatorCom;
                        TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                        {
                            TMUtility.PlayAnimation(theAnimator,"UIView_Gem_Normal");
                            var theEffect = theAnimator.transform.Find("vfx_Target");
                            if (theEffect != null)
                            {
                                theEffect.gameObject.SetActive(false);
                            }
                        });
                       
                    }

                    flyObj.gameObject.SetActive(false);
                    GameObject.Destroy(flyObj.gameObject);
                    curIndex++;
                    if (curIndex == theCount)
                    {
                        callBack?.Invoke();
                    }
                });
        }
        
        // 如果没有目标，直接调用回调
        if (theCount == 0)
        {
            callBack?.Invoke();
        }
    }

    #endregion

    #region UI Event

    void OnPointerDown(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        PointerEventData pointerEventData = baseEventData as PointerEventData;
        var thePos = pointerEventData.pointerCurrentRaycast.worldPosition;
        UINode_ClickEffect.gameObject.SetActive(false);
        UINode_ClickEffect.gameObject.SetActive(true);
        UINode_ClickEffect.position = thePos;
        gameSys.OnPointerDown(baseEventData, UINode_Refer,UINode_Content);
        if (CurUsingItem == EItemType.Bomb)
        {
            StopGuide();
        }
    }

    void OnPointerUp(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        bool bIsPutDown = false;
        gameSys.OnPointerUp(baseEventData, ref bIsPutDown);
#if DEVELOPMENT_BUILD
        if (bIsPutDown)
        {
            UITxt_Complexlnfo.gameObject.SetActive(true);
        }
#endif
    }

    bool dragBlock = false;
    void OnDrag(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        gameSys.OnDrag(baseEventData);
    }

    void OnBeginDrag(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        gameSys.OnBeginDrag(baseEventData);
    }

    void OnEndDrag(BaseEventData baseEventData)
    {
        //if (!bCanPlay) return;
        gameSys.OnEndDrag(baseEventData);
    }

    void onGameOver(BlockGameOver go)
    {
        if (gameResultType == EnumGameResultType.EFST_Victory)
            return;
            
        // 如果有目标收集事件正在处理中，延迟检查失败
        if (isCollectingTargets)
        {
            // 延迟1.6秒检查，等待所有收集动画完成
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1.6f, () => {
                if (gameResultType == EnumGameResultType.EFST_Victory)
                    return;
                    
                // 再次检查是否已经胜利，如果没有则处理失败
                HandleGameFailure();
            }));
            return;
        }
        
        // 没有收集事件，立即处理失败
        HandleGameFailure();
    }
    
    void HandleGameFailure()
    {
        gameResultType = EnumGameResultType.EFST_Fail;

        var theTime = 1.5f;
                 var theTipContent = LocalizationManager.Instance.GetLocalizedString("&key.UI_level_desc_37");
        GameGlobal.GetMod<ModTip>().ShowTip(theTipContent, showDuration: theTime);
        bCanPlay = false;
        GameUtils.SetEventSystemEnable(false);
        GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(theTime,
            () => {
                bCanPlay = true;
                GameUtils.SetEventSystemEnable(true);
                PlayGameOver();
            }));
    }

    void PlayGameOver()
    {
        GameVictoryData theViewData = new GameVictoryData();
        theViewData.curTargetType = targetType;
        theViewData.levelGameType = levelGameType;
        theViewData.activeTargetTypes = activeTargetTypes;
        theViewData.isVictory = gameResultType == EnumGameResultType.EFST_Victory;
        theViewData.achievedScore = curScore;
        theViewData.curGemInfoDic = curGemInfoDic;
        theViewData.levelId = curViewData.levelId;
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameVictory, theViewData);
    }

    void onExitGame(ExitGameEvent evt)
    {
        HandleLevelInfoBI(evt.resType);
        OnCloseBtn();
    }

    void onReplayGame(ReplayGameEvent evt)
    {
        BlockPlayManager.Instance.HandleReset();
        var theStorage = StorageExtension.GameBlockLevelStorage;
        var theInfo = theStorage.CurLevelInfo;
        theInfo.gameResult = (int)EnumGameResultType.EFST_Fail;
        theStorage.CurLevelInfo = theInfo;
        theStorage.HandleReset();

        OnOpenGame();
    }

    void HandleProductBlockEvent(EventProductBlockInfo evt)
    {
        var theType1 = gameSys.GetPruductName(evt.targetType);
        var theType2 = gameSys.GetPruductName(evt.realType);
        UITxt_PType?.SetText($"{theType1}->{theType2}");
    }

    List<RectTransform> tempBlocks = new List<RectTransform>(ModGame.genBlockCount) { null, null, null };
    List<int> tempGenList = new List<int>(ModGame.genBlockCount) { -1, -1, -1 };
    const string nodePropEffect = "PropEffect";


    void HandleDragAreaRaycastState(bool state, bool forBomb = false)
    {
        var theMg = UINode_FullShield.gameObject.GetComponent<MaskableGraphic>();
        if(forBomb == false )
        {
            if (theMg) theMg.raycastTarget = state;
        }
        

        var theDragAreas = gameSys.DragAreas;
        for (int i = 0; i < theDragAreas.Count; i++)
        {
            if (forBomb && i == 1) continue;
            var area = theDragAreas[i];
            theMg = area.gameObject.GetComponent<MaskableGraphic>();
            if (theMg) theMg.raycastTarget = state;
        }
    }


    void HandleRotateBlock(int pos)
    {
        var theRealGenList = ModGame.GenList;
        var theContainers = gameSys.BlockContainers;
        var theCount = theContainers?.Count ?? 0;
        if (pos < 0 || pos >= theRealGenList.Count || theCount != theRealGenList.Count) return;

        var theColors = gameSys.BlockColors;
        var theRealBlocks = gameSys.Blocks;

        var theContainer = theContainers[pos];
        var theRealBlock = theRealBlocks[pos];
        if (theRealBlock == null || theContainer == null) return;
        var theRealBlockId = theRealGenList[pos];

        var theConfigInfo = InGameConfigManager.GetBlockRotateConfig(theRealBlockId);
        if (theConfigInfo == null)
        {
            CLog.Error($"HandleRotateBlock--Block[{theRealBlockId}]的旋转block在配置表Table_InGame_BlockRotate里不存在");
            return;
        }

        var theColor = theColors[pos];
        var theTurnBlockId = theConfigInfo.RotateConfig;
        var theAssetName = gameSys.GetBlockName(theTurnBlockId);
        RectTransform theTurnBlock = gameSys.CreateOrGetBlock(theAssetName, theContainer,
            theColor);
        if (theTurnBlock == null) return;


        gameSys.HandleGemForRotateBlock(theRealBlock, theTurnBlock, theRealBlockId, theTurnBlockId, theColor);
        CurUsingItemWaitEffect = true;
        theRealBlock.gameObject.SetActive(false);
        theRealBlock.SetParent(UINode_BlockPool);
        theTurnBlock.gameObject.SetActive(true);
        theTurnBlock.SetAsFirstSibling();

        if (tempBlocks[pos] == null && tempGenList[pos] == -1)
        {
            tempBlocks[pos] = theRealBlock;
            tempGenList[pos] = theRealBlockId;
        }

        theRealBlocks[pos] = theTurnBlock;
        theRealGenList[pos] = theTurnBlockId;
        HandRotateGameOver();
    }

    private void HandRotateGameOver()
    {
        var imageBg = UIBtn_CloseItem1.GetComponent<Image>();
        var effectObj =   UIBtn_CloseItem1.transform.Find("VFX_Props_Light");
        if (gameSys.CheckGameOver())
        {
            UIBtn_CloseItem1.interactable = false;
            effectObj?.gameObject.SetActive(false);
            CoreUtils.SetImg(imageBg,"GameAtlas","bm_common_btn40");
                         var theContent =LocalizationManager.Instance.GetLocalizedString("&key.UI_common_level_cannot_use");
            GameGlobal.GetMod<ModTip>().ShowTip(theContent);
            return;
        }
        UIBtn_CloseItem1.interactable = true;
        CoreUtils.SetImg(imageBg,"GameAtlas","bm_common_btn10");
    }

    void HandleBombPlay()
    {
        var theRealBlocks = gameSys.Blocks;

        var theCount = theRealBlocks?.Count ?? 0;
        if (theCount != ModGame.genBlockCount) return;

        var theContainers = gameSys.BlockContainers;
        var theColors = gameSys.BlockColors;
        var theRealGenList = ModGame.GenList;

        if (theColors.Count != theCount
            || theContainers.Count != theCount
            || theRealGenList.Count != theCount
            || tempBlocks.Count != theCount
            || tempGenList.Count != theCount)
            return;

        var thePropNode = $"{EItemType.Bomb}Prop";

        //生成备用Block
        var tempPos = 1;
        var theNode = theContainers[tempPos];
        GenBlockInfo theInfo = GenBlockInfo.defaultInfo;
        theInfo.gemList = theInfo.gemPosList = new List<int>();
        var theTurnBlockId = ModGame.Block3x3Config;
        var theAssetName = gameSys.GetBlockName(theTurnBlockId);
        RectTransform theTurnBlock = gameSys.CreateOrGetBlock(theAssetName, theNode,
            theColors[tempPos], theInfo);
        theTurnBlock.gameObject.SetActive(true);

        for (int pos = 0; pos < theContainers.Count; pos++)
        {
            var theRealBlock = theRealBlocks[pos];
            var theRealBlockId = theRealGenList[pos];

            theNode = theContainers[pos];
            if (theNode == null) continue;

            if(pos == tempPos)
            {
                if (theRealBlock != null)
                {
                    theRealBlock.gameObject.SetActive(false);
                    theRealBlock.SetParent(UINode_BlockPool);
                    CurUsingItemWaitEffect = true;
                }

                tempBlocks[pos] = theRealBlock;
                tempGenList[pos] = theRealBlockId;

                theRealBlocks[pos] = theTurnBlock;
                theRealGenList[pos] = theTurnBlockId;

                List<BlockGrid> blockGrides = gameSys.GetBlockGridList(theTurnBlock);
                if (blockGrides != null)
                {
                    for (int i = 0; i < blockGrides.Count; i++)
                    {
                        var theGrid = blockGrides[i];
                        var theImage = theGrid.CurImage;
                        theImage.enabled = false;
                        var theChildImage = theImage.transform.Find("BG");
                        if (theChildImage) theChildImage.gameObject.SetActive(false);
                    }
                }

                theNode.localScale = Vector3.one;

                var tempName = nodePropEffect;
                var thePropEffect = theNode.Find(tempName);
                if (thePropEffect == null)
                {
                    thePropEffect = GameObject.Instantiate(UINode_PropEffect, theNode);
                    thePropEffect.name = tempName;
                    thePropEffect.localPosition = Vector3.zero;
                }

                if (thePropEffect == null) continue;

                thePropEffect.localScale = Vector3.one;
                thePropEffect.gameObject.SetActive(true);
                thePropEffect.SetAsLastSibling();
                var theChildCount = thePropEffect.childCount;
                Transform theBornEffect = null;
                for (int j = 0; j < theChildCount; j++)
                {
                    var theChild = thePropEffect.GetChild(j);
                    var theSame = theChild.name.Equals(thePropNode);
                    theChild.gameObject.SetActive(theSame);
                    if (theSame)
                    {
                        theChild.localScale = Vector3.one;
                        theBornEffect = theChild.Find("VFX_bomb_born");
                    }
                }

                if (theBornEffect != null)
                {
                    theBornEffect.gameObject.SetActive(false);
                    theBornEffect.gameObject.SetActive(true);
                }
            }
            else
            {
                if (theRealBlock != null)
                {
                    theRealBlock.gameObject.SetActive(false);
                    theRealBlock.SetParent(UINode_BlockPool);
                    CurUsingItemWaitEffect = true;
                }

                tempBlocks[pos] = theRealBlock;
                tempGenList[pos] = theRealBlockId;
                theRealBlocks[pos] = null;
            }
        }

        if (CurUsingItemWaitEffect)
        {
            var bombAsset = "VFX_Explode";
            var theGo = UINode_Content.Find(bombAsset);
            if (theGo) theGo.gameObject.SetActive(false);
        }
        HandleDragAreaRaycastState(false, true);
        PlayGuide();
    }

    Dictionary<GameButton, int> tempRotateBtns = new Dictionary<GameButton, int>();

    void HandleRotatePlay()
    {
        var theRealBlocks = gameSys.Blocks;

        var theCount = theRealBlocks?.Count ?? 0;
        if (theCount != ModGame.genBlockCount) return;

        var theContainers = gameSys.BlockContainers;
        var theColors = gameSys.BlockColors;
        var theRealGenList = ModGame.GenList;

        if (theColors.Count != theCount
            || theContainers.Count != theCount
            || theRealGenList.Count != theCount
            || tempBlocks.Count != theCount
            || tempGenList.Count != theCount)
            return;

        var thePropNode = $"{EItemType.Rotate}Prop";

       
        tempRotateBtns.Clear();

        for (int pos = 0; pos < theContainers.Count; pos++)
        {
            var theRealBlock = theRealBlocks[pos];
            var theRealBlockId = theRealGenList[pos];
            var theNode = theContainers[pos];
            if (theNode == null || theRealBlock == null) continue;
            //var theConfigInfo = InGameConfigManager.GetBlockRotateConfig(theRealBlockId);
            //if (theConfigInfo == null)
            //{
            //    CLog.Error($"HandleRotateBlock--Block[{theRealBlockId}]的旋转block在配置表Table_InGame_BlockRotate里不存在");
            //    continue;
            //}
            var guide = GameGlobal.GetMod<GuideSys>();
            if (ENABLE_PROP_GUIDE && !guide.IsFinished("GUIDE_302"))
            {
                guide.RegisterTarget(GuideTargetType.Rotate_2, theNode, pos.ToString());
            }
            var tempName = nodePropEffect;
            var thePropEffect = theNode.Find(tempName);
            if (thePropEffect == null)
            {
                thePropEffect = GameObject.Instantiate(UINode_PropEffect, theNode);
                thePropEffect.name = tempName;
                thePropEffect.localPosition = Vector3.zero;
            }

            if (thePropEffect == null) continue;
            thePropEffect.localScale = Vector3.one;
            thePropEffect.gameObject.SetActive(true);
            thePropEffect.SetAsLastSibling();

            GameButton rotateBtn = null;
            var theChildCount = thePropEffect.childCount;
            for (int j = 0; j < theChildCount; j++)
            {
                var theChild = thePropEffect.GetChild(j);
                var theSame = theChild.name.Equals(thePropNode);
                theChild.gameObject.SetActive(theSame);
                if (theSame)
                {
                    rotateBtn = theChild.gameObject.GetComponentInChildren<GameButton>();
                }
            }

            if (rotateBtn != null)
            {
                
               
                tempRotateBtns[rotateBtn] = pos;
                int curPos1 = pos;
                rotateBtn.onClick.RemoveAllListeners();
                rotateBtn.onClick.AddListener(() =>
                {
                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_21");
                    var theBtn = rotateBtn;
                    if (theBtn == null || !tempRotateBtns.ContainsKey(theBtn)) return;
                    var curPos = curPos1;
        
                    curPos = tempRotateBtns[theBtn];
                    if (ENABLE_PROP_GUIDE && guide.GetCurGuideId() == "GUIDE_302")
                    {
                        guide.FinishCurrent(GuideTargetType.Rotate_2);
                    }
                    HandleRotateBlock(curPos);
                });
            }
        }

        CurUsingItemWaitEffect = true;
        HandleDragAreaRaycastState(false);
    }

    void HandleConVertPlay()
    {
        var theRealBlocks = gameSys.Blocks;
        var theCount = theRealBlocks?.Count ?? 0;
        if (theCount != ModGame.genBlockCount) return;

        var theColors = gameSys.BlockColors;
        var theRealGenList = ModGame.GenList;

        if (theColors.Count != theCount
            || theRealGenList.Count != theCount
            || tempBlocks.Count != theCount
            || tempGenList.Count != theCount)
            return;

        var theNoShowList = new List<int>();
        for (int pos = 0; pos < theCount; pos++)
        {
            var theRealBlock = theRealBlocks[pos];
            var theRealBlockId = theRealGenList[pos];
            if (theRealBlock != null)
            {
                var theAssetName = gameSys.GetBlockName(ModGame.Block1x1Config);
                RectTransform theTurnBlock = gameSys.CreateOrGetBlock(theAssetName, theRealBlock.parent,
                    theColors[pos]);
                if (theTurnBlock != null)
                {
                    theRealBlock.gameObject.SetActive(false);
                    theRealBlock.SetParent(UINode_BlockPool);
                    theTurnBlock.gameObject.SetActive(true);

                    if (tempBlocks[pos] == null && tempGenList[pos] == -1)
                    {
                        CurUsingItemWaitEffect = true;
                        tempBlocks[pos] = theRealBlock;
                        tempGenList[pos] = theRealBlockId;
                    }

                    theRealBlocks[pos] = theTurnBlock;
                    theRealGenList[pos] = ModGame.Block1x1Config;
                }
                else
                {
                    theNoShowList.Add(pos);
                }
            }
            else
            {
                theNoShowList.Add(pos);
            }
        }

        PlayBlockBornEffect(theNoShowList);
    }

    Dictionary<EItemType, int> useItemToCount = new Dictionary<EItemType, int>();

    void OnUseItemForGame(EItemType itemId)
    {
        if (gameSys.IsBlockGenerating || this.GO == null || !bCanPlay) return; //正在生成block

        gameSys.RestDragAreas();
        var theRealBlocks = gameSys.Blocks;
        var theCheckCount = theRealBlocks?.Count ?? 0;
        if (theCheckCount != ModGame.genBlockCount) return;

        if (CurUsingItem != EItemType.None)
        {
            return;
        }

        var theConfig = InGameConfigManager.GetIngameItemConfig((int)itemId);
        if (theConfig == null) return;

        if (levelConfig.Sort < theConfig.UnlockLevelId)
        {
            var theContent =
                                 LocalizationManager.Instance.GetLocalizedStringWithFormat("&key.UI_prop_level_locked", theConfig.UnlockLevelId.ToString());
            GameGlobal.GetMod<ModTip>().ShowTip(theContent);
            return;
        }

        var theCount = bagSys.GetItemCount(itemId);
        if (theCount == 0) return;

        if(itemId == EItemType.Convert)
        {
            if (_blockBornEffectCoroutine != null || _blockClearEffectCoroutine != null) return;
        }

        gameSys.MoveHomeBlockImmediately(true);
        CLog.Warning($"UseItem--请求使用物品{CurUsingItem}");
        bool bUse = true;
        var thePropTerm = "";
        var thePropIcon = "";
        Button closePropBtn = null;
        UIBtn_CloseItem1.gameObject.SetActive(false);
        UIBtn_CloseItem2.gameObject.SetActive(false);
        UIBtn_CloseItem3.gameObject.SetActive(false);
        var guide = GameGlobal.GetMod<GuideSys>();
        switch (itemId)
        {
            case EItemType.Rotate:
            {
                thePropTerm = "UI_level_desc_47";
                thePropIcon = "bm_icon_props3";
                if (ENABLE_PROP_GUIDE && guide.GetCurGuideId() == "GUIDE_301")
                {
                    guide.FinishCurrent(GuideTargetType.Rotate);
                }
                GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_20");
                HandleRotatePlay();
                closePropBtn = UIBtn_CloseItem1;
                break;
            }
            case EItemType.Bomb:
            {
                thePropTerm = "UI_level_desc_49";
                thePropIcon = "bm_icon_props5";
                if (ENABLE_PROP_GUIDE && guide.GetCurGuideId() == "GUIDE_303")
                {
                    guide.FinishCurrent(GuideTargetType.Bomb);
                }
                GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_22");
                HandleBombPlay();
                //closePropBtn = UIBtn_CloseItem2;
                break;
            }
            case EItemType.Convert:
            {
                thePropTerm = "UI_level_desc_51";
                thePropIcon = "bm_icon_props1";
                GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_26");

                var theNoShowList = new List<int>();
                for (int pos = 0; pos < theCheckCount; pos++)
                {
                    var theRealBlock = theRealBlocks[pos];
                    if (theRealBlock == null)
                    {                      
                        theNoShowList.Add(pos);
                    }
                }

                PlayBlockClearEffect(theNoShowList, () =>
                {
                    if (this.GO == null) return;
                    HandleConVertPlay();
                    if (ENABLE_PROP_GUIDE && guide.GetCurGuideId() == "GUIDE_305")
                    {
                        guide.FinishCurrent(GuideTargetType.Convert);
                    }
                    HandleRealUseItem();
                });
                
                    
                //closePropBtn = UIBtn_CloseItem3;
                break;
            }
            default:
            {
                bUse = false;
                break;
            }
        }

        if (bUse)
        {
            CLog.Warning($"UseItem--请求使用物品{CurUsingItem}成功");

            UINode_UseItemBG.gameObject.SetActive(true);
            UINode_Props.gameObject.SetActive(true);
            UINode_CloseProp.gameObject.SetActive(true);
            UITxt_PropDes.SetTerm(thePropTerm);
            CoreUtils.SetImg(UIImg_PropIcon, Const_Common.GameAtlas, thePropIcon);
            CurUsingItem = itemId;

            if (closePropBtn)
            {
                closePropBtn.gameObject.SetActive(true);
                closePropBtn.onClick.RemoveAllListeners();
                closePropBtn.onClick.AddListener(() =>
                {
                    if (closePropBtn == null) return;
                    closePropBtn.gameObject.SetActive(false);
                    HandleRealUseItem();
                    //OnCancleUseItem(CurUsingItem);
                });

                var theEffect = closePropBtn.transform.Find("VFX_Props_Light");
                if (theEffect != null) theEffect.gameObject.SetActive(true);
            }         
        }
    }

    void HandleCancleBlockTurn()
    {
        if (!CurUsingItemWaitEffect) return;
        var theContainers = gameSys.BlockContainers;
        var theCount = theContainers?.Count ?? 0;
        if (theCount <= 0 || ModGame.GenList.Count != theCount) return;

        var theColors = gameSys.BlockColors;
        var putCount = 0;
        var theRealGenList = ModGame.GenList;
        var theRealBlocks = gameSys.Blocks;

        for (int i = 0; i < tempBlocks.Count; i++)
        {
            var theTurnBlock = theRealBlocks[i];
            if (theTurnBlock != null)
            {              
                theTurnBlock.gameObject.SetActive(false);
                theTurnBlock.SetParent(UINode_BlockPool);
            }

            var theRealBlock = tempBlocks[i];
            if (theRealBlock != null)
            {
                if ( CurUsingItem == EItemType.Bomb)
                {
                    List<BlockGrid> blockGrides = gameSys.GetBlockGridList(theRealBlock);
                    if (blockGrides != null)
                    {
                        for (int index = 0; index < blockGrides.Count; index++)
                        {
                            var theGrid = blockGrides[index];
                            var theImage = theGrid.CurImage;
                            theImage.enabled = true;
                            theGrid.ColorState = theColors[i];
                            var theChildImage = theImage.transform.Find("BG");
                            if (theChildImage) theChildImage.gameObject.SetActive(true);
                        }
                    }
                }

                theRealBlock.SetParent(theContainers[i]);
                theRealBlock.transform.localPosition = Vector3.zero;
                theRealBlock.transform.localScale = Vector3.one;
            }

            theRealBlocks[i] = theRealBlock;
            tempBlocks[i] = null;

            theRealGenList[i] = tempGenList[i];
            tempGenList[i] = -1;

            if (theRealBlocks[i] != null)
            {
                theRealBlocks[i].gameObject.SetActive(true);
            }
            else
            {
                putCount++;
            }
        }

        gameSys.SetPutdownCount(putCount);
        
    }

    void OnCancleUseItem(EItemType itemId)
    {
        if (CurUsingItem == EItemType.None || CurUsingItem != itemId)
        {
            CLog.Exception($"OnCancleUseItem 取消使用物品出异常{itemId.ToString()}");
            return;
        }

        switch (itemId)
        {
            case EItemType.Rotate:
            case EItemType.Bomb:
            {
                HandleCancleBlockTurn();
                CLog.Warning($"UseItem--取消使用物品{CurUsingItem}成功");
                break;
            }
            case EItemType.Convert:
            {
                CLog.Warning($"UseItem--使用物品{CurUsingItem}成功");
                break;
            }
            default:
                break;
        }


        HandleDragAreaRaycastState(true, itemId == EItemType.Bomb);
        CurUsingItem = EItemType.None;
        CurUsingItemWaitEffect = false;

        UINode_UseItemBG.gameObject.SetActive(false);
        UINode_Props.gameObject.SetActive(false);
        UINode_CloseProp.gameObject.SetActive(false);


        var theBlocks = gameSys.Blocks;
        var theContainers = gameSys.BlockContainers;
        for (int pos = 0; pos < theContainers.Count; pos++)
        {
            var container = theContainers[pos];
            if (container == null) continue;
            var tempName = nodePropEffect;
            var thePropEffect = container.Find(tempName);
            if (thePropEffect == null) continue;
            thePropEffect.gameObject.SetActive(false);
        }
    }


    CoroutineHandler coroutinePlayBomb;
    void OnPlayBombExplode(EventPlayBombExplode ev)
    {
        var theContainers = gameSys.BlockContainers;
        var theCount = theContainers?.Count ?? 0;
        if (theCount <= 0 || ModGame.GenList.Count != theCount) return;
        if (coroutinePlayBomb != null) GameGlobal.GetMod<ModCoroutine>().StopCoroutine(coroutinePlayBomb);

        {
            
            var bombAsset = "VFX_Explode";
            var theGo = UINode_Content.Find(bombAsset);
            if (theGo == null)
            {
                theGo = UINode_Content.CreateChild(bombAsset).transform;
                var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>(bombAsset).GetInstance(theGo.gameObject);
                if (theAsset != null)
                {
                    var theEffectGo = GameObject.Instantiate(theAsset, theGo);
                    theEffectGo.transform.localScale = Vector3.one;
                    theEffectGo.transform.localPosition = Vector3.zero;
                    theEffectGo.gameObject.SetActive(true);
                }
                theGo.localScale = Vector3.one;
                theGo.localPosition = Vector3.zero;
            }

            Vector3 thePos = ev.tPos;
            theGo.localPosition = thePos;
            theGo.gameObject.SetActive(true);
            coroutinePlayBomb = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1f,
              () =>
              {
                  coroutinePlayBomb = null;
                  theGo.gameObject.SetActive(false);
              }));

        }
    }

    void HandleRealUseItem()
    {
        HandleDragAreaRaycastState(true);
        var theContainers = gameSys.BlockContainers;
        if (CurUsingItemWaitEffect && CurUsingItem != EItemType.None)
        {
            if(useItemToCount.ContainsKey(CurUsingItem))
            {
                useItemToCount[CurUsingItem]++;
            }
            else
            {
                useItemToCount.Add(CurUsingItem, 1);
            }

            int consumeCount = 1;
            var theItemChangeBiArg = new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.PlayLevel);
            theItemChangeBiArg.data1 = levelConfig.Id.ToString();
            theItemChangeBiArg.data2 = $"{BIHelper.GetBIItemByLogicItemType(CurUsingItem).ToString()}:{consumeCount}";

            bagSys.ConsumeItem(CurUsingItem, consumeCount,theItemChangeBiArg);

            if (CurUsingItem == EItemType.Bomb)
            {
                HandleCancleBlockTurn();
            }
            else
            {
                var theBlocks = gameSys.Blocks;
                for (int i = 0; i < tempBlocks.Count; i++)
                {
                    var theRealBlock = tempBlocks[i];
                    if (theRealBlock != null)
                    {
                        var theIndex = theBlocks.FindIndex(ele => ele == theRealBlock);
                        if (theIndex == -1)
                        {
                            theRealBlock.gameObject.SetActive(false);
                            theRealBlock.SetParent(UINode_BlockPool);
                        }

                        tempBlocks[i] = null;
                        tempGenList[i] = -1;
                    }
                }
            }

            CLog.Warning($"UseItem--使用物品{CurUsingItem}成功");
        }
        else
        {
            HandleCancleBlockTurn();
        }

        UINode_UseItemBG.gameObject.SetActive(false);
        UINode_Props.gameObject.SetActive(false);
        UINode_CloseProp.gameObject.SetActive(false);

        for (int pos = 0; pos < theContainers.Count; pos++)
        {
            var container = theContainers[pos];
            if (container == null) continue;
            var tempName = nodePropEffect;
            var thePropEffect = container.Find(tempName);
            if (thePropEffect == null) continue;
            thePropEffect.gameObject.SetActive(false);
        }

        if (CurUsingItem == EItemType.Rotate)
        {
            gameSys.HandleGameOver();
        }

        CurUsingItem = EItemType.None;
        CurUsingItemWaitEffect = false;
    }

    void OnAddItemForGame(EItemType itemId)
    {
        var theConfig = InGameConfigManager.GetIngameItemConfig((int)itemId);
        if (theConfig == null) return;

        if (levelConfig.Id < theConfig.UnlockLevelId)
        {
            return;
        }

        HandleBuyProp(itemId);
    }

    #endregion

    #region API

    public void OnHardProduct()
    {
        curHardCount++;
    }

    public int GetCurCompleteProgress(int theSum)
    {
        int curCompleteRatio = 0;
        if (targetType == EnumTargetType.ETT_Gem)
        {
            int allGemTargetSum = 0;
            int curGemCount = 0;
            foreach (var item in curGemInfoDic)
            {
                allGemTargetSum += item.Value.gemTargetCount;
                curGemCount += item.Value.gemCount;
            }

            curCompleteRatio = curGemCount * theSum / allGemTargetSum;
        }
        else if (targetType == EnumTargetType.ETT_WoodenCrate)
        {
            curCompleteRatio = curWoodenCrateCount * theSum / targetWoodenCrates;
        }
        else if (targetType == EnumTargetType.ETT_Bird)
        {
            curCompleteRatio = curBirdCount * theSum / targetBirds;
        }
        else if (targetType == EnumTargetType.ETT_Cat)
        {
            curCompleteRatio = curCatCount * theSum / targetCats;
        }
        else if (targetType == EnumTargetType.ETT_Leaf)
        {
            curCompleteRatio = curLeafCount * theSum / targetLeaves;
        }
        else if (targetType == EnumTargetType.ETT_Ice)
        {
            curCompleteRatio = curIceCount * theSum / targetIces;
        }
        else // ETT_Score
        {
            curCompleteRatio = curScore * theSum / targetScore;
        }

        return curCompleteRatio;
    }

    public List<int> GetProductSubTypeWeights(EnumBlockProductType theType)
    {
        List<int> weights = null;
        var curPhase = GetCurPhase();
        switch (curPhase)
        {
            case 1:
                {
                    if (levelConfig.EasyExperienceType.Count > 0 &&
                        levelConfig.EasyExperienceType.Count == levelConfig.EasyExperienceWeight.Count)
                    {                       
                        if (theType == EnumBlockProductType.EBPT_ComplexityUp)
                        {
                            weights = levelConfig.ComplexityUpWeight1;
                        }
                        else if (theType == EnumBlockProductType.EBPT_ComplexityDown)
                        {
                            weights = levelConfig.ComplexityDownWeight1;
                        }
                    }
                    break;
                }
            case 2:
                {
                    if (levelConfig.NormalExperienceType.Count > 0 && levelConfig.NormalExperienceType.Count ==
                        levelConfig.NormalExperienceWeight.Count)
                    {                       
                        if (theType == EnumBlockProductType.EBPT_ComplexityUp)
                        {
                            weights = levelConfig.ComplexityUpWeight2;
                        }
                        else if (theType == EnumBlockProductType.EBPT_ComplexityDown)
                        {
                            weights = levelConfig.ComplexityDownWeight2;
                        }
                    }
                    break;
                }
            case 3:
                {
                    if (levelConfig.HardExperienceType.Count > 0 &&
                        levelConfig.HardExperienceType.Count == levelConfig.HardExperienceWeight.Count)
                    {                       
                        if (theType == EnumBlockProductType.EBPT_ComplexityUp)
                        {
                            weights = levelConfig.ComplexityUpWeight3;
                        }
                        else if (theType == EnumBlockProductType.EBPT_ComplexityDown)
                        {
                            weights = levelConfig.ComplexityDownWeight3;
                        }
                    }
                    break;
                }
        }
        return weights;
    }

    public int GetCurPhase()
    {
        var thePhaseList = levelConfig.LevelTargetRatio;
        var theSum = thePhaseList.Sum();
        int curCompleteRatio = GetCurCompleteProgress(theSum);
        int curSum = 0;
        int curPhase = 1;
        for (int i = 0; i < thePhaseList.Count; i++)
        {
            curSum += thePhaseList[i];
            if (curCompleteRatio <= curSum)
            {
                curPhase = i + 1;
                break;
            }
        }

        CLog.Info($"GetLevelGameBlockProductType--curPhase is {curPhase}");
        return curPhase;
    }

    List<int> curTempWeight = new List<int>();
    public GenerateParam GetLevelGameBlockProductType(ref List<int> weights)
    {
        var theResParam = GenerateParam.defaultP;
        var theType = EnumBlockProductType.EBPT_Normal;
        bool isNeedHardProduct = false;
        var curPhase = GetCurPhase();

        var theComplexity = BlockPlayManager.Instance.Complexity;
        var limitClear = theComplexity < gameSys.LimitClearComplextiy;
        CLog.Info($"GetLevelGameBlockProductType--curPhase:{curPhase},limitClear:{limitClear}，{theComplexity}，{levelConfig.Sort}");
        var theSum = 0;
        int curSum = 0;
        List<int> tempWeight = null;
        List<int> tempType = null;
        switch (curPhase)
        {
            case 1:
            {
                if (levelConfig.EasyExperienceType.Count > 0 &&
                    levelConfig.EasyExperienceType.Count == levelConfig.EasyExperienceWeight.Count)
                {
                    tempType = levelConfig.EasyExperienceType;
                    tempWeight = levelConfig.EasyExperienceWeight;
                    if (limitClear)
                    {
                        var theIndex = tempType.FindIndex(x => x == (int)EnumBlockProductType.EBPT_ComplexityDown);
                        if (theIndex >= 0)
                        {
                            curTempWeight.Clear();
                            curTempWeight.AddRange(tempWeight);
                            tempWeight = curTempWeight;
                            tempWeight[theIndex] = 0;
                        }
                    }
                    theSum = tempWeight.Sum() + 1;
                    var theRandom = UnityEngine.Random.Range(1, theSum);
                    curSum = 0;
                    for (int i = 0; i < tempWeight.Count; i++)
                    {
                        curSum += tempWeight[i];
                        if (theRandom <= curSum)
                        {
                            theType = (EnumBlockProductType)tempType[i];
                            break;
                        }
                    }

                    if(theType == EnumBlockProductType.EBPT_ComplexityUp)
                    {
                        weights = levelConfig.ComplexityUpWeight1;
                    }
                    else if(theType == EnumBlockProductType.EBPT_ComplexityDown)
                    {
                        weights = levelConfig.ComplexityDownWeight1;
                    }

                    CLog.Info($"GetLevelGameBlockProductType--curPhase:{curPhase},theRandom:{theRandom}");
                }
                else
                {
                    CLog.Exception($"{levelConfig.Id}的EasyExperienceType配置有误");
                }

                break;
            }
            case 2:
            {
                if (levelConfig.NormalExperienceType.Count > 0 && levelConfig.NormalExperienceType.Count ==
                    levelConfig.NormalExperienceWeight.Count)
                {
                    tempType = levelConfig.NormalExperienceType;
                    tempWeight = levelConfig.NormalExperienceWeight;
                    if (limitClear)
                    {
                        var theIndex = tempType.FindIndex(x => x == (int)EnumBlockProductType.EBPT_ComplexityDown);
                        if (theIndex >= 0)
                        {
                            curTempWeight.Clear();
                            curTempWeight.AddRange(tempWeight);
                            tempWeight = curTempWeight;
                            tempWeight[theIndex] = 0;
                        }
                    }
                    theSum = tempWeight.Sum() + 1;
                    var theRandom = UnityEngine.Random.Range(1, theSum);
                    curSum = 0;
                    for (int i = 0; i < tempWeight.Count; i++)
                    {
                        curSum += tempWeight[i];
                        if (theRandom <= curSum)
                        {
                            theType = (EnumBlockProductType)tempType[i];
                            break;
                        }
                    }

                    if (theType == EnumBlockProductType.EBPT_ComplexityUp)
                    {
                        weights = levelConfig.ComplexityUpWeight2;
                    }
                    else if (theType == EnumBlockProductType.EBPT_ComplexityDown)
                    {
                        weights = levelConfig.ComplexityDownWeight2;
                    }

                    CLog.Info($"GetLevelGameBlockProductType--curPhase:{curPhase},theRandom:{theRandom}");
                }
                else
                {
                    CLog.Exception($"{levelConfig.Id}的 NormalExperienceWeight 配置有误");
                }

                break;
            }
            case 3:
            {
                if (levelConfig.HardExperienceType.Count > 0 &&
                    levelConfig.HardExperienceType.Count == levelConfig.HardExperienceWeight.Count)
                {
                    tempType = levelConfig.HardExperienceType;
                    tempWeight = levelConfig.HardExperienceWeight;
                    if (limitClear)
                    {
                        var theIndex = tempType.FindIndex(x => x == (int)EnumBlockProductType.EBPT_ComplexityDown);
                        if (theIndex >= 0)
                        {
                            curTempWeight.Clear();
                            curTempWeight.AddRange(tempWeight);
                            tempWeight = curTempWeight;
                            tempWeight[theIndex] = 0;
                        }
                    }
                    theSum = tempWeight.Sum() + 1;
                    var theRandom = UnityEngine.Random.Range(1, theSum);
                    curSum = 0;
                    for (int i = 0; i < tempWeight.Count; i++)
                    {
                        curSum += tempWeight[i];
                        if (theRandom <= curSum)
                        {
                            theType = (EnumBlockProductType)tempType[i];
                            break;
                        }
                    }

                    if (theType == EnumBlockProductType.EBPT_ComplexityUp)
                    {
                        weights = levelConfig.ComplexityUpWeight3;
                    }
                    else if (theType == EnumBlockProductType.EBPT_ComplexityDown)
                    {
                        weights = levelConfig.ComplexityDownWeight3;
                    }

                    CLog.Info(
                    $"GetLevelGameBlockProductType--curPhase:{curPhase},theRandom:{theRandom}, curHardCount:{curHardCount}");
                }
                else
                {
                    CLog.Exception($"{levelConfig.Id}的 HardExperienceWeight 配置有误");
                }
                break;
            }
        }

        int curCompleteRatio = GetCurCompleteProgress(100);
        if (curCompleteRatio >= levelConfig.LevelProgressDiffTrigger)
        {
            CLog.Info($"GetLevelGameBlockProductType--curCompleteRatio:{curCompleteRatio}, curHardCount:{curHardCount}");
            if (curHardCount < levelConfig.ChallengeLimitedTime)
            {
                var theValue = InGameConfigManager.GetGlobalConfig<int>("LevelComplextiyRange");
                if (BlockPlayManager.Instance.Complexity >= theValue) isNeedHardProduct = true;
            }
        }

        theResParam = GenerateParam.GetNormalParam(theType);
        if (isNeedHardProduct)
        {
            theResParam.pType = EnumBlockProductType.EBPT_Hard;
            theResParam.execMilliSecond = 500;
            theResParam.pType1 = theType;
            theResParam.execMilliSecond1 = 500;
        }

        return theResParam;
    }

    List<int> tempGemList = new List<int>();
    List<int> tempGemWeightList = new List<int>();

    public KeyValuePair<int, int> GenerateGem()
    {
        var theRes = new KeyValuePair<int, int>(-1, -1);
        var theRandom = UnityEngine.Random.Range(1, 101);
        var bGenerate = theRandom <= levelConfig.GemAppearWeight;
        CLog.Info($"GenerateGem--GemAppearRandom:{theRandom}");
        if (!bGenerate) return theRes;

        tempGemList.Clear();
        tempGemWeightList.Clear();
        // 数据来源不再从google表获取
        //tempGemList.AddRange(levelConfig.GemstoneType);
        tempGemList.AddRange(targetGems);
        var theWeightList = levelConfig.GemstoneTypeWeight;
        var theGemId = -1;
        //if (theWeightList.Count != levelConfig.GemstoneType.Count)
        if (theWeightList.Count != targetGems.Count)
        {
            tempGemList.RemoveAll((int ele) =>
            {
                if (!curGemInfoDic.ContainsKey(ele)) return true;
                var theInfo = curGemInfoDic[ele];
                return theInfo.gemCount >= theInfo.gemTargetCount;
            });

            if (tempGemList.Count > 0)
            {
                var randomIndex = UnityEngine.Random.Range(0, tempGemList.Count);
                //theGemId = levelConfig.GemstoneType[randomIndex];
                theGemId = targetGems[randomIndex];
            }
        }
        else
        {
            tempGemWeightList.AddRange(theWeightList);
            while (true)
            {
                var theIndex = tempGemList.FindIndex((int ele) =>
                {
                    if (!curGemInfoDic.ContainsKey(ele)) return true;
                    var theInfo = curGemInfoDic[ele];
                    return theInfo.gemCount >= theInfo.gemTargetCount;
                });
                if (theIndex < 0) break;
                tempGemList.RemoveAt(theIndex);
                tempGemWeightList.RemoveAt(theIndex);
                if (tempGemList.Count <= 0) break;
            }

            var theSum = tempGemWeightList.Sum() + 1;
            theRandom = UnityEngine.Random.Range(1, theSum);
            int curSum = 0;
            for (int i = 0; i < tempGemWeightList.Count; i++)
            {
                var tempWeight = tempGemWeightList[i];
                var tempId = tempGemList[i];

                curSum += tempWeight;
                if (theRandom <= curSum)
                {
                    theGemId = tempId;
                    break;
                }
            }

            CLog.Info($"GenerateGem--GemGenerateRandom:{theRandom}");
        }

        if (theGemId == -1) return theRes;

        var theGemCount = 1;
        if (levelConfig.GemNumberRange.Count == 2)
        {
            theGemCount = UnityEngine.Random.Range(levelConfig.GemNumberRange[0], levelConfig.GemNumberRange[1]);
        }

        theRes = new KeyValuePair<int, int>(theGemId, theGemCount);
        return theRes;
    }


    public void HandleStorageGemInfo(ref StorageDictionary<int, int> storeDic)
    {
        storeDic.Clear();
        foreach (var item in curGemInfoDic)
        {
            storeDic.Add(item.Key, item.Value.gemCount);
        }
    }

    private CoroutineHandler _blockBornEffectCoroutine;
    List<Transform> blockBornEffect = new List<Transform>();
    public void PlayBlockBornEffect(List<int> noShowList = null)
    {
        if (_blockBornEffectCoroutine != null || this.GO == null) return;

        if (blockBornEffect.Count <= 0)
        {
            var theChildCount = UINode_BlockBornEffect.childCount;
            for (int i = 0; i < theChildCount; i++)
            {
                blockBornEffect.Add(UINode_BlockBornEffect.GetChild(i));
            }
        }

        UINode_BlockBornEffect.gameObject.SetActive(true);
        for(int i = 0; i < blockBornEffect.Count; i++)
        {
            if (noShowList == null)
            {
                blockBornEffect[i].gameObject.SetActive(true);
            }
            else
            {
                var theIndex = noShowList.FindIndex(ele => ele == i);
                blockBornEffect[i].gameObject.SetActive(theIndex == -1);
            }
            
        }
        
        _blockBornEffectCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(0.5f,
     () =>
     {
         _blockBornEffectCoroutine = null;
         UINode_BlockBornEffect.gameObject.SetActive(false);
     }));
    }

    private CoroutineHandler _blockClearEffectCoroutine;
    List<Transform> blockClearEffect = new List<Transform>();
    public void PlayBlockClearEffect(List<int> noShowList = null, Action cb = null)
    {
        if(this.GO == null) return;
        if (_blockClearEffectCoroutine != null || _blockBornEffectCoroutine != null) return;

        if (blockClearEffect.Count <= 0)
        {
            var theChildCount = UINode_BlockClearEffect.childCount;
            for (int i = 0; i < theChildCount; i++)
            {
                blockClearEffect.Add(UINode_BlockClearEffect.GetChild(i));
            }
        }

        UINode_BlockClearEffect.gameObject.SetActive(true);
        for (int i = 0; i < blockClearEffect.Count; i++)
        {
            if (noShowList == null)
            {
                blockClearEffect[i].gameObject.SetActive(true);
            }
            else
            {
                var theIndex = noShowList.FindIndex(ele => ele == i);
                blockClearEffect[i].gameObject.SetActive(theIndex == -1);
            }

        }

        _blockClearEffectCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(0.5f,
     () =>
     {
         _blockClearEffectCoroutine = null;
         UINode_BlockClearEffect.gameObject.SetActive(false);
         cb?.Invoke();
     }));
    }

    #endregion

    public void HandleGameVictory()
    {
        {
            if (levelConfig != null) BIHelper.SendTrackingEvent_PassLevel(levelConfig.Id);
            var theEnergySys = GameGlobal.GetMod<EnergySys>();
            var InInfintyEnergyState = theEnergySys.IsInfiniteEnergy();
            if (!InInfintyEnergyState)
            {
                var theItemChangeBiArg = new BIHelper.ItemChangeReasonArgs(BiEventBlockMatch1.Types.ItemChangeReason.EnergyPassLevel);
                theItemChangeBiArg.data1 = levelConfig.Id.ToString();

                //返还体力
                GameGlobal.GetMod<EnergySys>().AddEnergy(1, theItemChangeBiArg);
            }
        }

        gameResultType = EnumGameResultType.EFST_Victory;
        gameSys.HandleGameReward(gameResultType);
    }

    void HandleDispose(bool fromDistory = false)
    {
        if (_blockBornEffectCoroutine != null)
        {
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_blockBornEffectCoroutine);
            _blockBornEffectCoroutine = null;
        }

        if (buffItems != null)
        {
            foreach (var item in buffItems)
            {
                FlyTarget.RemoveTarget(item.ItemId, item.RootTs);
                item.HandleDispose();
            }

            buffItems.Clear();
        }

        if (coroutinePlayBomb != null)
        {
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(coroutinePlayBomb);
            coroutinePlayBomb = null;
        }

        CurUsingItem = EItemType.None;
        CurUsingItemWaitEffect = false;
        isGuideActive = false;  // 重置引导状态

        if (fromDistory) return;

        HandleCancleBlockTurn();       
        gameSys.HandleGameDispose(gameResultType);
        StopGuide();
    }

    public EnumGameResultType GameResultType => gameResultType;


    private Tween guideTween;
    private bool isTriggerGuide = false;
    private void PlayGuide()
    {
        // 检查道具引导开关
        if (!ENABLE_PROP_GUIDE)
        {
            return;
        }
        
        var guide = GameGlobal.GetMod<GuideSys>();
        bool finished = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideBombFinish;
        if ( finished ||levelConfig.Id != 5 ||  !guide.IsFinished("GUIDE_303"))
        {
            return;
        }

        isTriggerGuide = true;
        UINode_finger.gameObject.SetActive(true);
        UINode_finger.transform.localPosition = new Vector3(0, UINode_BlockArea.localPosition.y, 0);
        guideTween =  UINode_finger.transform.DOLocalMoveY(128f,2.3f).SetLoops(999).SetEase(Ease.OutQuad);
        guideTween.PlayForward();
    }

    private void StopGuide()
    {
        if (ENABLE_PROP_GUIDE && isTriggerGuide)
        {
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideBombFinish = true;
        }
        guideTween?.Kill();
        UINode_finger.gameObject.SetActive(false);
        isTriggerGuide = false;
    }

    bool NeedGuid301
    {
        get
        {
            if (!ENABLE_PROP_GUIDE || null == levelConfig) return false;
            var guide = GameGlobal.GetMod<GuideSys>();
            if (!guide.IsFinished("GUIDE_301") && levelConfig.Id == 2)
            {
                return true;

            }
            return false;
        }
    }

    #region GM相关

    private GameButton hideDebugBtn;

    private void OnShowDebugBtn()
    {
        UINode_Debug.gameObject.SetActive(true);
        if (hideDebugBtn == null)
        {
            hideDebugBtn = UINode_Debug.GetComponent<GameButton>();
            hideDebugBtn.onClick.AddListener(OnHideDebugBtn);
        }
    }

    void RefreshComplexlnfo()
    {
#if DEVELOPMENT_BUILD
        var theValue = BlockPlayManager.Instance.Complexity;
        string infoStr = $"Complex:{theValue}\nSparsity:{BlockPlayManager.Instance.Sparsity},Connected:" +
                         $"{BlockPlayManager.Instance.ConnectedComponentsCount}\nEdgeCount:{BlockPlayManager.Instance.EdgeCountEx},RowAndColum:{BlockPlayManager.Instance.RowColVariability}\nEntropy:{BlockPlayManager.Instance.Entropy}";
        UITxt_Complexlnfo.SetText(infoStr);
        UITxt_Complex.SetText(theValue.ToString());
#endif
    }

    private void OnHideDebugBtn()
    {
        UINode_Debug.gameObject.SetActive(false);
    }

    private void OnRandomBlockBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_Normal);
    }

    private void OnHardBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_Hard);
    }

    private void OnComplexityUpBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ComplexityUp);
    }

    private void OnComplexityDownBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ComplexityDown);
    }

#if DEBUG || DEVELOPMENT_BUILD
    public void HandleChangeBlockPlan()
    {
        if (gameSys == null) return;
        var thePlan = gameSys.CurBlockProductIsNative ? "Native" : "Local";
        UITxt_BlockPlan.SetText(thePlan);
    }
#endif
    #endregion

    /// <summary>
    /// 获取引导放置位置的静态方法
    /// </summary>
    public static void GetGuidePutPos(ref Vector2Int tPos)
    {
        tPos = ModGame.InvaildPos;
        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        //var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        if (step > 2 /*|| (!guide.IsFinished("GUIDE_104") && guildGroup == EABTestGroup.Group1)*/)
        {
            return;
        }

        if (step == 0)
        {
            tPos.x = 3;
            tPos.y = 3;
        }
        else if (step == 1)
        {
            tPos.x = 3;
            tPos.y = 2;
        }
    }

    /// <summary>
    /// 播放基础引导动画
    /// </summary>
    private void PlayMainGuide()
    {
        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        if (step > 2 /*|| (!guide.IsFinished("GUIDE_104") && guildGroup == EABTestGroup.Group1)*/)
        {
            return;
        }

        isMainGuideTriggered = true;
        // 设置引导激活状态，防止引导期间加分
        isGuideActive = true;
        var thePos = UINode_Main.parent.InverseTransformPoint(UINode_Content.position);
        GameObject finger;
        if (step < 1)
        {
            finger = UINode_finger2.gameObject;
            BIHelper.SendGameEvent(guildGroup == EABTestGroup.Group2
                ? BiEventBlockMatch1.Types.GameEventType.GameEventFteTestGameClickBlock1
                : BiEventBlockMatch1.Types.GameEventType.GameEventFteGameClickBlock1);
        }
        else
        {
            thePos.x -= Grid.HalfGridWidth;
            finger = UINode_finger1.gameObject;
            BIHelper.SendGameEvent(guildGroup == EABTestGroup.Group2
                ? BiEventBlockMatch1.Types.GameEventType.GameEventFteTestGameClickBlock2
                : BiEventBlockMatch1.Types.GameEventType.GameEventFteGameClickBlock2);
        }

        finger.SetActive(true);
        finger.transform.localPosition = new Vector3(0, UINode_BlockArea.localPosition.y, 0);
        mainGuideTween = finger.transform.DOLocalMove(thePos, GuideMoveDuration).SetLoops(GuideLoopCount).SetEase(Ease.OutQuad);
        mainGuideTween.PlayForward();
    }

    /// <summary>
    /// 停止基础引导动画
    /// </summary>
    private void StopMainGuide()
    {
        if (isMainGuideTriggered)
        {
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep++;
            if (SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep >= 2)
            {
                // 两步引导完成后，先播放清屏特效，再设置引导结束标志和加载关卡信息
                isMainGuideTriggered = false;  // 立即设置为false，防止重复触发
                isGuideCompletingWithEffect = true;
                PlayGuideCompletionEffect();
                return;
            }
        }

        mainGuideTween?.Kill();
        UINode_finger1.gameObject.SetActive(false);
        UINode_finger2.gameObject.SetActive(false);
    }
    
    /// <summary>
    /// 播放引导完成的清屏特效
    /// </summary>
    private void PlayGuideCompletionEffect()
    {
        CLog.Info("两步引导完成，播放清屏特效");
        
        // 停止手指动画
        mainGuideTween?.Kill();
        UINode_finger1.gameObject.SetActive(false);
        UINode_finger2.gameObject.SetActive(false);
        
        // 播放清屏特效（显示所有位置的特效）
        PlayBlockClearEffect(null, () =>
        {
            if (this.GO == null) return;
            
            // 特效完成后设置引导结束标志和状态
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessEnd = true;
            isGuideCompletingWithEffect = false;
            isGuideActive = false;  // 引导真正结束，允许加分
            
            CLog.Info("引导清屏特效完成，开始加载第一关关卡信息");
            
            // 加载第一关的关卡信息
            LoadFirstLevelInfo();
        });
    }
    
    /// <summary>
    /// 加载第一关的关卡信息
    /// </summary>
    private void LoadFirstLevelInfo()
    {
        // 设置为第一关关卡
        curViewData.levelId = 1;
        levelConfig = InGameConfigManager.GetInGameLevelCfgByLevelId(1);
        
        if (levelConfig == null)
        {
            CLog.Exception($"UIView_Game.LoadFirstLevelInfo--第一关[{1}]配置有误或不存在");
            return;
        }

        CLog.Info($"成功加载第一关关卡信息，关卡Sort：{levelConfig.Sort}，ABTest分组[{levelConfig.AbTestGroup}]");
        
        // 重新初始化关卡信息
        HandleInitInfo();
        
        // 重置游戏状态
        curHardCount = 0;
        bCanPlay = false;
        isPlayingVictoryAnimation = false; // 重置胜利动画标记
        blockBoardAni = null;
        isPayBoard = false;
        
        // 重新初始化游戏系统
        OpenGameInfo theInfo = new OpenGameInfo()
        {
            enumBlockGameType = EnumBlockGameType.BlockGame_Stage,
            blockPool = UINode_BlockPool,
            gridContainer = UINode_Content,
            tipPlay = UINode_TipPlay,
            enumTargetType = targetType,
            levelId = levelConfig.Sort,
        };
        gameSys.OnOpenGame(theInfo);
        
        // 重置分数
        curScore = StorageExtension.GameBlockLevelStorage.AchiveScore;
        
        // 显示提示信息
        //showTipInfo();
        // 出块
        gameSys.HandleBlockProduct();
        // 刷新目标信息显示
        RefreshTargetInfo();
        bCanPlay = true;
        // 重置游戏结果状态
        gameResultType = EnumGameResultType.EFST_None;
    }

    /// <summary>
    /// 销毁所有清屏引导相关资源
    /// </summary>
    private void DisposeClearGuide()
    {
        StopClearScreenGuide();
        tempClearList.Clear();
        tempBlockList.Clear();

        if (tempBlock != null)
        {
            GameObject.Destroy(tempBlock);
            tempBlock = null;
        }

        UINode_finger1.gameObject.SetActive(false);
    }

    /// <summary>
    /// 停止当前清屏引导动画并清理资源
    /// </summary>
    private void StopClearScreenGuide()
    {
        clearFingerTween?.Kill();
        clearFingerTween = null;

        UINode_finger1.gameObject.SetActive(false);

        if (tempBlock != null)
        {
            GameObject.DestroyImmediate(tempBlock);
            tempBlock = null;
        }
    }

    /// <summary>
    /// 初始化并播放清屏引导动画
    /// </summary>
    private void PlayClearScreenGuide()
    {
        // 设置初始位置
        UINode_finger1.transform.localPosition = Vector3.zero;
        tempClearIndex = gameSys.CurBlockIsNewOrder ? SelectIndexsNew[tempCurIndex]: SelectIndexs[tempCurIndex];
        var info = tempClearList[tempCurIndex];
        Vector3 targetPosition = gameSys.GetBoardUIPosByPutInfo(info);
        CLog.Info($"触发清屏引导--》:index:{tempClearIndex} targetPosition：{targetPosition} info targetPos:{info.targetPos} blockConfig:{info.blockConfig}");

        // 实例化透明方块
        InstantiateTransparentBlock(info);

        // 设置手指位置
        SetFingerPositionBasedOnIndex();

        // 显示引导手指并开始移动动画
        UINode_finger1.gameObject.SetActive(true);
        clearFingerTween = UINode_finger1.DOLocalMove(targetPosition, GuideMoveDuration)
            .SetLoops(GuideLoopCount)
            .SetEase(Ease.OutQuad)
            .SetUpdate(true);
    }

    /// <summary>
    /// 实例化一个半透明方块用于引导提示
    /// </summary>
    private void InstantiateTransparentBlock(BlockPutInfo info)
    {
        if (tempBlock != null) GameObject.DestroyImmediate(tempBlock);

        tempBlock = GameObject.Instantiate(tempBlockList[tempClearIndex].gameObject,Vector3.zero,Quaternion.identity,UINode_finger1.transform);
        tempBlock.transform.localScale = Vector3.one;
        tempBlock.transform.SetAsFirstSibling();
        tempBlock.transform.localPosition = Vector3.zero;
        // 设置半透明效果
        var images = tempBlock.GetComponentsInChildren<Image>(true);
        foreach (var image in images)
        {
            var color = image.color;
            image.color = new Color(color.r, color.g, color.b, 0.5f);
        }
    }

    /// <summary>
    /// 根据当前索引设置手指位置
    /// </summary>
    private void SetFingerPositionBasedOnIndex()
    {
        switch (tempClearIndex)
        {
            case 0:
                UINode_finger1.position = UINode_Block1.position;
                break;
            case 1:
                UINode_finger1.position = UINode_Block2.position;
                break;
            case 2:
                UINode_finger1.position = UINode_Block3.position;
                break;
        }
    }

    /// <summary>
    /// 检查是否进入下一步引导
    /// 若用户操作不匹配则重置引导
    /// </summary>
    private void TryShowNextStep_ForClearSceneGuide()
    {
        if (!isPlayClearGuide) return;

        var handleGuide = tempClearList.Count > 0 && tempClearList.Count <= MaxClearGuideSteps;
        if (handleGuide == false)
        {
            return;
        }

        if (tempCurIndex == tempClearList.Count - 1 )
        {
            //end
            ResetClearGuide();
            return;
        }
        StopClearScreenGuide();
        if (tempClearList.Count - 1 > tempCurIndex)
        {
            tempCurIndex++;
        }

        PlayClearScreenGuide();
    }

    /// <summary>
    /// 重置整个清屏引导流程
    /// </summary>
    private void ResetClearGuide()
    {
        tempClearList.Clear();
        StopClearScreenGuide();
        isPlayClearGuide = false;
        tempClearIndex = 0;
        tempCurIndex = 0;
    }

    /// <summary>
    /// 尝试触发清屏引导逻辑
    /// 条件：
    /// - 当前关卡支持清屏类型
    /// - 用户未触发过清屏引导
    /// - 最高分 > 1800
    /// - 清屏块数量在允许范围内（1~2）
    /// </summary>
    public void TryTriggerClearGuide(EnumBlockProductType curProductType, List<BlockPutInfo> clearList,
        List<RectTransform> blockList)
    {
        if (
            curProductType != EnumBlockProductType.EBPT_ClearScreen ||
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide ||
            StorageExtension.GameEndlssStorage.CurFirstScore < 1800 ||
            clearList.Count <= 0 || clearList.Count > MaxClearGuideSteps)
        {
            CLog.Info($"无法触发清屏引导--》:curProductType:{curProductType}  " +
                      $"是否已经清屏过:{SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide} " +
                      $"当前最大得分：{ StorageExtension.GameEndlssStorage.CurFirstScore}  clearList.Count：{ clearList.Count}");
            return;
        }

        tempClearList.AddRange(clearList);
        tempBlockList.Clear();
        tempBlockList.AddRange(blockList);
        SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide = true;

        StopClearScreenGuide();
        isPlayClearGuide = true;
        tempCurIndex = 0;
        PlayClearScreenGuide();
    }

    // 新增：使用DOTween播放宝石飞行动画序列（同时出现，依次飞行）
    private void PlayGemFlyAnimationSequence(List<KeyValuePair<int, Vector3>> gemList)
    {
        if (gemList == null || gemList.Count == 0) return;

        var sequence = DOTween.Sequence();
        List<int> achiveGems = new List<int>();
        int completedCount = 0;

        // 第一步：同时创建所有飞行对象
        var flyObjects = new List<Transform>();
        for (int i = 0; i < gemList.Count; i++)
        {
            var tempKv = gemList[i];
            var gemId = tempKv.Key;

            if (!curGemInfoDic.ContainsKey(gemId)) continue;

            if (!achiveGems.Contains(gemId)) achiveGems.Add(gemId);

            var theInfo = curGemInfoDic[gemId];
            var flyObj = GameGlobal.GetMod<FlySys>().GetFlyClone(theInfo.gemImage.transform);

            // 设置初始位置和状态
            var z = GameGlobal.GetMgr<UIMgr>().UICanvas.transform.position.z;
            Vector3 startPos = tempKv.Value;
            startPos.x += UnityEngine.Random.Range(-0.2f, 0.2f);
            startPos.y += UnityEngine.Random.Range(-0.2f, 0.2f);
            startPos.z = z;

            flyObj.position = startPos;
            flyObj.localScale = Vector3.one; // 立即可见
            flyObj.gameObject.SetActive(true);

            flyObjects.Add(flyObj);
        }

        // 第二步：使用DOTween的Join操作实现同时播放但有延迟的动画
        for (int i = 0; i < flyObjects.Count; i++)
        {
            var flyObj = flyObjects[i];
            var tempKv = gemList[i];
            var gemId = tempKv.Key;
            var theInfo = curGemInfoDic[gemId];

            var targetPos = theInfo.gemImage.transform.position;
            targetPos.z = GameGlobal.GetMgr<UIMgr>().UICanvas.transform.position.z;

            // 创建单个飞行动画序列
            var flySequence = DOTween.Sequence();
            flySequence.AppendInterval(i * 0.05f); // 延迟时间
            flySequence.Append(flyObj.DOScale(Vector3.one * 1.1f, 0.3f));
            flySequence.Append(CreateFlyPath(flyObj, targetPos));
            flySequence.AppendCallback(() => {
                if (this.GO == null) return;

                // 更新宝石计数（在动画完成时）
                GemInfo gemInfo = curGemInfoDic[gemId];
                if (gemInfo.gemCount < gemInfo.gemTargetCount) gemInfo.gemCount += 1;
                curGemInfoDic[gemId] = gemInfo;

                // 播放目标UI反馈动画
                var theAnimator = theInfo.AnimatorCom;
                TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Bounce", () =>
                {
                    TMUtility.PlayAnimation(theAnimator, "UIView_Gem_Normal");
                    var theEffect = theAnimator.transform.Find("vfx_Target");
                    if (theEffect != null)
                    {
                        theEffect.gameObject.SetActive(false);
                    }
                });

                // 清理飞行对象
                flyObj.gameObject.SetActive(false);
                GameObject.Destroy(flyObj.gameObject);

                // 检查是否所有动画都完成了
                completedCount++;
                if (completedCount >= gemList.Count)
                {
                    // 所有动画完成后，更新UI显示
                    RefreshTargetInfo(achiveGems);

                    // 检查宝石收集是否完成
                    bool allGemsCompleted = true;
                    foreach (var kv in curGemInfoDic)
                    {
                        var theId = kv.Key;
                        var theIndex = targetGems.FindIndex(ele => ele == theId);
                        if (theIndex >= 0 && theIndex < targetGemCounts.Count)
                        {
                            var theTargetNum = targetGemCounts[theIndex];
                            if (kv.Value.gemCount < theTargetNum)
                            {
                                allGemsCompleted = false;
                                break;
                            }
                        }
                    }

                    CheckGameVictory(allGemsCompleted);
                    
                    // 标记收集完成
                    isCollectingTargets = false;
                }
            });

            // 使用Join让所有动画同时开始
            if (i == 0)
                sequence.Append(flySequence);
            else
                sequence.Join(flySequence);
        }

        sequence.Play();
    }

    // 新增：创建飞行路径动画
    private Tween CreateFlyPath(Transform flyObj, Vector3 targetPos)
    {
        Vector3 startPos = flyObj.position;
        Vector3 control = new Vector3(startPos.x + 0.3f, startPos.y - 0.3f, startPos.z);
        Vector3 control1 = Vector3.MoveTowards(control, targetPos, 1);

        return flyObj.DOPath(new[] { startPos, control, control1, targetPos }, 0.65f, DG.Tweening.PathType.CatmullRom)
            .SetEase(DG.Tweening.Ease.InQuart);
    }

    // 新增：添加到批量飞行队列
    private void AddToBatchFlyQueue(int targetId, Vector3 sourcePos, Vector3 targetPos, Transform uiTransform, System.Action onComplete)
    {
        pendingFlyAnimations.Add(new TargetFlyInfo
        {
            targetId = targetId,
            sourcePos = sourcePos,
            targetPos = targetPos,
            uiTransform = uiTransform,
            onComplete = onComplete
        });

        // 启动批量处理（如果还没有启动）
        if (!isBatchProcessing)
        {
            isBatchProcessing = true;
            // 使用DOTween延迟一帧后处理批量动画
            DOTween.Sequence().AppendInterval(0.01f).AppendCallback(() => {
                ProcessBatchFlyAnimations();
            });
        }
    }

    // 新增：处理批量飞行动画
    private void ProcessBatchFlyAnimations()
    {
        if (pendingFlyAnimations.Count > 0)
        {
            // 创建当前批次的副本
            var currentBatch = new List<TargetFlyInfo>(pendingFlyAnimations);
            pendingFlyAnimations.Clear();

            // 使用通用的飞行动画序列方法
            PlayTargetFlyAnimationSequence(currentBatch);
        }

        isBatchProcessing = false;
    }

    // 新增：通用的目标物飞行动画序列（类似宝石的实现）
    private void PlayTargetFlyAnimationSequence(List<TargetFlyInfo> targetList)
    {
        if (targetList == null || targetList.Count == 0) return;

        var sequence = DOTween.Sequence();

        // 第一步：根据目标类型创建不同的飞行动画
        for (int i = 0; i < targetList.Count; i++)
        {
            var targetInfo = targetList[i];

            // 创建单个飞行动画序列
            var flySequence = DOTween.Sequence();
            flySequence.AppendInterval(i * 0.15f); // 延迟时间

            // 根据目标类型选择不同的动画方式
            if (targetInfo.targetId == SPECIAL_ID_BIRD)
            {
                // 鸟使用特殊的CreateBirdFlyAnimation
                flySequence.AppendCallback(() => {
                    CreateBirdFlyAnimation(targetInfo.sourcePos, targetInfo.targetPos,
                        onComplete: () => {
                            if (this.GO == null) return;
                            targetInfo.onComplete?.Invoke();
                        },
                        onSoundPlay: () => {
                            // 音效在CreateBirdFlyAnimation内部处理
                        });
                });
            }
            else
            {
                // 其他目标物使用通用的UI飞行动画
                flySequence.AppendCallback(() => {
                    var flyObj = GameGlobal.GetMod<FlySys>().GetFlyClone(targetInfo.uiTransform);

                    // 设置初始位置和状态
                    var z = GameGlobal.GetMgr<UIMgr>().UICanvas.transform.position.z;
                    Vector3 startPos = targetInfo.sourcePos;
                    startPos.x += UnityEngine.Random.Range(-0.2f, 0.2f);
                    startPos.y += UnityEngine.Random.Range(-0.2f, 0.2f);
                    startPos.z = z;

                    flyObj.position = startPos;
                    flyObj.localScale = Vector3.one; // 立即可见
                    flyObj.gameObject.SetActive(true);

                    var targetPos = targetInfo.targetPos;
                    targetPos.z = z;

                    // 创建飞行动画
                    var flyAnimation = DOTween.Sequence();
                    flyAnimation.Append(flyObj.DOScale(Vector3.one * 1.1f, 0.3f));
                    flyAnimation.Append(CreateFlyPath(flyObj, targetPos));
                    flyAnimation.AppendCallback(() => {
                        if (this.GO == null) return;

                        // 执行完成回调
                        targetInfo.onComplete?.Invoke();

                        // 清理飞行对象
                        flyObj.gameObject.SetActive(false);
                        GameObject.Destroy(flyObj.gameObject);
                    });
                    flyAnimation.Play();
                });
            }

            // 使用Join让所有动画同时开始
            if (i == 0)
                sequence.Append(flySequence);
            else
                sequence.Join(flySequence);
        }

        sequence.Play();
    }

}
