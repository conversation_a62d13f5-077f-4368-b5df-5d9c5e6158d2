/************************************************
 * Config class : Table_Global_Item
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Global
{
    public partial class Table_Global_Item:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// #物品类型
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// #物品类型
        /// </summary>
        public int Amount { get; set; }

        /// <summary>
        /// #物品类型
        /// </summary>
        public bool Infinity { get; set; }

        /// <summary>
        /// #物品类型
        /// </summary>
        public int InfinityItemId { get; set; }

        /// <summary>
        /// #物品类型
        /// </summary>
        public int ItemType { get; set; }

        
        /// <summary>
        /// #名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// #图集名称
        /// </summary>
        public string Atlas { get; set; }
        
        /// <summary>
        /// #图标名称
        /// </summary>
        public string Icon { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}