/************************************************
 * Config class : Table_InGame_BlockGroup
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_BlockGroup:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #判断玩家当前分数小於等於该值; 从下往上优先级越高
        /// </summary>
        public int ScoreNumber { get; set; }
        
        /// <summary>
        /// 特殊块ID
        /// </summary>
        public List<int> SpecialBlockList { get; set; }
        
        /// <summary>
        /// 块的ID列表(参与不同分段的选块规则); 清屏和多消不受此配置限制
        /// </summary>
        public List<int> BlockList { get; set; }
        
        /// <summary>
        /// 块ID对应的权重（选出的块按照权重值选择对应的块）
        /// </summary>
        public List<int> ComplextiyUpBlockWeight { get; set; }
        
        /// <summary>
        /// 块ID对应的权重（选出的块按照权重值选择对应的块）
        /// </summary>
        public List<int> ComplextiyDownBlockWeight { get; set; }
        
        /// <summary>
        /// 块ID对应的权重（选出的块按照权重值选择对应的块）
        /// </summary>
        public List<int> RandomBlockWeight { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}