/************************************************
 * Config class : Table_InGame_ComplextiyUpAlgorithm
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_ComplextiyUpAlgorithm:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #组ID; 流失预测值达到超过一定数值后启用新的组ID配置
        /// </summary>
        public int ComplexityUpGroupId { get; set; }
        
        /// <summary>
        /// 分数区间; （大于等于最小值，小于最大值）
        /// </summary>
        public List<int> ScoreRange { get; set; }
        
        /// <summary>
        /// 构筑算法1基础权重：; 优先复杂度增加
        /// </summary>
        public int ComplexityUpWeight { get; set; }
        
        /// <summary>
        /// 构筑算法2基础权重：; 优先边数减少
        /// </summary>
        public int EdgeDownWeight { get; set; }
        
        /// <summary>
        /// 构筑算法3基础权重：; 优先边数减少含次优解
        /// </summary>
        public int EdgeSecondDownWeight { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}