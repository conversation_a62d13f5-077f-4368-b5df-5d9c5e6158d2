/************************************************
 * Config class : Table_InGame_BlockRotate
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_BlockRotate:ConfigBase
    {   
        /// <summary>
        /// 需要旋转的BLOCK
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 旋转之后的BLOCK
        /// </summary>
        public int RotateConfig { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}