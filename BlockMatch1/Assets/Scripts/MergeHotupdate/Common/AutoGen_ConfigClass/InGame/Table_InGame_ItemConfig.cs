/************************************************
 * Config class : Table_InGame_ItemConfig
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_ItemConfig:ConfigBase
    {   
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 道具类型; 101.转换方向; 102.炸弹; 103.变成单块
        /// </summary>
        public int ItemType { get; set; }
        
        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 道具描述
        /// </summary>
        public string Des { get; set; }
        
        /// <summary>
        /// 第几关解锁
        /// </summary>
        public int UnlockLevelId { get; set; }
        
        /// <summary>
        /// 解锁后的初始数量
        /// </summary>
        public int InitCount { get; set; }
        
        /// <summary>
        /// 道具单价
        /// </summary>
        public int ItemPrice { get; set; }
        
        /// <summary>
        /// 购买的价格
        /// </summary>
        public int BuyPrice { get; set; }
        
        /// <summary>
        /// 购买的数量
        /// </summary>
        public int BuyCount { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}