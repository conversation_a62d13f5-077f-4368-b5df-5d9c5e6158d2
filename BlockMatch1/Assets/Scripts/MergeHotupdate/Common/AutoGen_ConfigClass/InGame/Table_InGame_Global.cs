/************************************************
 * Config class : Table_InGame_Global
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_Global:ConfigBase
    {   
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 相机缩放最小值
        /// </summary>
        public float CameraZoomMin { get; set; }
        
        /// <summary>
        /// 相机缩放最大值
        /// </summary>
        public float CameraZoomMax { get; set; }
        
        /// <summary>
        /// 相机缩放速度
        /// </summary>
        public float CameraZoomSpeed { get; set; }
        
        /// <summary>
        /// 相机默认缩放值
        /// </summary>
        public float CameraZoomDefault { get; set; }
        
        /// <summary>
        /// 相机缩放最大值（PAD）
        /// </summary>
        public float CameraZoomMax_Pad { get; set; }
        
        /// <summary>
        /// 相机默认缩放值（PAD）
        /// </summary>
        public float CameraZoomDefaultMax_Pad { get; set; }
        
        /// <summary>
        /// 相机缓动速度
        /// </summary>
        public float CameraSmoothSpeed { get; set; }
        
        /// <summary>
        /// 车移动的速度
        /// </summary>
        public float CarSpeed { get; set; }
        
        /// <summary>
        /// 云移动的速度最小值
        /// </summary>
        public float CloudSpeedMin { get; set; }
        
        /// <summary>
        /// 云移动的速度最大值
        /// </summary>
        public float CloudSpeedMax { get; set; }
        
        /// <summary>
        /// 云循环生成的间隔
        /// </summary>
        public float CloudCycleInterval { get; set; }
        
        /// <summary>
        /// 云1和云2生成的间隔
        /// </summary>
        public float Cloud1to2Interval { get; set; }
        
        /// <summary>
        /// 云2和云3生成的间隔
        /// </summary>
        public float Cloud2to3Interval { get; set; }
        
        /// <summary>
        /// 局内道具每次购买的数量
        /// </summary>
        public int ItemBuyCountPer { get; set; }
        
        /// <summary>
        /// 进局消耗的体力
        /// </summary>
        public int CostEnergy { get; set; }
        
        /// <summary>
        /// 关卡结算星星参数-比例
        /// </summary>
        public List<int> WinStartRateList { get; set; }
        
        /// <summary>
        /// 关卡结算星星参数-数量
        /// </summary>
        public List<int> WinStartCountList { get; set; }
        
        /// <summary>
        /// 到达关卡警告的时间
        /// </summary>
        public int WarningTime { get; set; }
        
        /// <summary>
        /// 胜利获取装修币数量
        /// </summary>
        public int GetDecoCoinCount { get; set; }
        
        /// <summary>
        /// 星星宝箱解锁
        /// </summary>
        public int StarChestUnlcokLevel { get; set; }
        
        /// <summary>
        /// 等级宝箱解锁
        /// </summary>
        public int LevelChestUnlcokLevel { get; set; }
        
        /// <summary>
        /// 周挑战解锁通过关卡
        /// </summary>
        public int WeeklyChallengeUnlcokLevel { get; set; }
        
        /// <summary>
        /// 从大到小排序，1：装修币 2：体力 3：周挑战 4：星星
        /// </summary>
        public List<int> GainItemSort { get; set; }
        
        /// <summary>
        /// 收集元素时拖尾特效缩放系数
        /// </summary>
        public float CollectTrailEffectScaleRatio { get; set; }
        
        /// <summary>
        /// 暂停时的时间缩放值
        /// </summary>
        public float PauseTimeScale { get; set; }
        
        /// <summary>
        /// 视为滑动的距离
        /// </summary>
        public float AsDragDist { get; set; }
        
        /// <summary>
        /// 每次进关看广告复活的最大次数
        /// </summary>
        public int ReviveByAdLimit { get; set; }
        
        /// <summary>
        /// 复活礼包功能解锁通过关卡
        /// </summary>
        public int ReviveGiftPackUnlockLevel { get; set; }
        
        /// <summary>
        /// 新手礼包功能解锁通过关卡
        /// </summary>
        public int NewbieGiftPackUnlockLevel { get; set; }
        
        /// <summary>
        /// 破冰礼包功能解锁通过关卡
        /// </summary>
        public int IceBreakGiftPackUnlockLevel { get; set; }
        
        /// <summary>
        /// 轻松体验阶段玩家的分数区间(大于等于最小值，小于最大值)
        /// </summary>
        public List<int> RelaxedExperience { get; set; }
        
        /// <summary>
        /// 普通体验阶段玩家的分数区间(大于等于最小值，小于最大值)
        /// </summary>
        public List<int> ModerateExperience { get; set; }
        
        /// <summary>
        /// 适度压力的体验阶段玩家的分数区间(大于等于最小值，小于最大值)
        /// </summary>
        public List<int> SlightPressureExperience { get; set; }
        
        /// <summary>
        /// 压力体验阶段玩家的分数区间(大于等于最小值，小于最大值)
        /// </summary>
        public List<int> StressfulExperience { get; set; }
        
        /// <summary>
        /// 高难度压力体验阶段玩家的分数区间(大于等于最小值，小于最大值)
        /// </summary>
        public List<int> ChallengingExperience { get; set; }
        
        /// <summary>
        /// N秒内连续消除计算为COMBO状态（单位：秒）
        /// </summary>
        public int ComboTime { get; set; }
        
        /// <summary>
        /// 过关的装修币奖励数值（关卡模式）
        /// </summary>
        public int BuildCoinLevelReward { get; set; }
        
        /// <summary>
        /// 过关的金币奖励数值（关卡模式）
        /// </summary>
        public int CoinLevelReward { get; set; }
        
        /// <summary>
        /// 复活消耗的金币数
        /// </summary>
        public int ReviveCostCoins { get; set; }
        
        /// <summary>
        /// 轻松体验阶段玩家的基础分数（局间体验）
        /// </summary>
        public int RelaxedExperienceBase { get; set; }
        
        /// <summary>
        /// 普通体验阶段玩家的基础分数（局间体验）
        /// </summary>
        public int ModerateExperienceBase { get; set; }
        
        /// <summary>
        /// 适度压力体验阶段玩家的基础分数（局间体验）
        /// </summary>
        public int SlightPressureExperienceBase { get; set; }
        
        /// <summary>
        /// 压力体验阶段玩家的基础分数（局间体验）
        /// </summary>
        public int StressfulExperienceBase { get; set; }
        
        /// <summary>
        /// 轻松体验阶段玩家局间的分数正负随机值范围
        /// </summary>
        public List<int> RelaxedExperienceRandomRange { get; set; }
        
        /// <summary>
        /// 普通体验阶段玩家局间的分数正负随机值范围
        /// </summary>
        public List<int> ModerateExperienceRandomRange { get; set; }
        
        /// <summary>
        /// 适度压力体验阶段玩家局间的分数正负随机值范围
        /// </summary>
        public List<int> SlightPressureExperienceRandomRange { get; set; }
        
        /// <summary>
        /// 高难度压力体验阶段玩家局间的分数正负随机值范围
        /// </summary>
        public List<int> StressfulExperienceRandomRange { get; set; }
        
        /// <summary>
        /// 关卡模式难题触发条件，复杂度大于等于该值则会优先触发难题算法
        /// </summary>
        public int LevelComplextiyRange { get; set; }
        
        /// <summary>
        /// 当前棋盘布局的边数小于等于该值时，会优先判断清屏
        /// </summary>
        public int ClearScreenTriggerEdge { get; set; }
        
        /// <summary>
        /// 清屏触发之后，下次优先判断清屏触发的出块轮数
        /// </summary>
        public int ClearScreenBlockInterval { get; set; }
        
        /// <summary>
        /// 多消触发之后，下次优先判断多消触发的出块轮数
        /// </summary>
        public int MultiClearBlockInterval { get; set; }
        
        /// <summary>
        /// 消除算法出现1X1后，下次出现1X1块的出块间隔轮数
        /// </summary>
        public int SmallBlockTriggerInterval { get; set; }
        
        /// <summary>
        /// 关卡模式当前棋盘复杂度小于该值时，则不会触发消除算法
        /// </summary>
        public int ComplextiyDownTriggerNumber { get; set; }
        
        /// <summary>
        /// 每局重开游戏，初始触发清屏的出块间隔数
        /// </summary>
        public int FirstClearScreenBlockInterval { get; set; }
        
        /// <summary>
        /// 首次清屏的出块间隔启用条件为历史最高分数大于等于该值
        /// </summary>
        public int FirstClearScreenBestScore { get; set; }
        
        /// <summary>
        /// 每一次新的无尽模式开局时，如果当前流失预测值大于等于该配置值，则以下三个表（COMPLEXTIYUPALGORITHM、COMPLEXTIYDOWNALGORITHM、OPTIMALCONFIGURATION）启用组ID=2的配置
        /// </summary>
        public float PredictChurnValue { get; set; }
        
        /// <summary>
        /// 无尽模式金币复活消耗
        /// </summary>
        public List<int> GoldReviveCost { get; set; }
        
        /// <summary>
        /// 无尽模式复活需要分数(>=)
        /// </summary>
        public int EndlessReviveNeedScore { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}