/************************************************
 * Config class : Table_Room_RoomAnimTimeLine
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Room
{
    public partial class Table_Room_RoomAnimTimeLine:ConfigBase
    {   
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 房间ID
        /// </summary>
        public int RoomId { get; set; }
        
        /// <summary>
        /// 节点完成时播放星星音效ID，空则不播
        /// </summary>
        public int StarSfx { get; set; }
        
        /// <summary>
        /// 展示动画总时间
        /// </summary>
        public int ShowTime { get; set; }
        
        /// <summary>
        /// 装修完成展示动画 时间轴
        /// </summary>
        public List<float> ShowTimeLine { get; set; }
        
        /// <summary>
        /// 展示动画 节点ID
        /// </summary>
        public List<int> ShowRoomNode { get; set; }
        
        /// <summary>
        /// 总览背景音
        /// </summary>
        public int Sound { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}