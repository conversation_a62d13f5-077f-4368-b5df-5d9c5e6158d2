/************************************************
 * Config class : Table_Room_RoomNode
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.Room
{
    public partial class Table_Room_RoomNode:ConfigBase
    {   
        /// <summary>
        /// 挂点ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 下一个挂点; 0:分支结束; -1：房间结束
        /// </summary>
        public List<int> NextNode { get; set; }
        
        /// <summary>
        /// 解锁此挂点的 ; 前置挂点ID
        /// </summary>
        public List<int> PreNode { get; set; }
        
        /// <summary>
        /// 选中此挂点 隐藏其他挂点(三选一)
        /// </summary>
        public List<int> ChoiceHideNodes { get; set; }
        
        /// <summary>
        /// 房间ID
        /// </summary>
        public int RoomId { get; set; }
        
        /// <summary>
        /// 是否默认开启
        /// </summary>
        public bool IsOpen { get; set; }
        
        /// <summary>
        /// 是否是清扫节点
        /// </summary>
        public bool IsClear { get; set; }
        
        /// <summary>
        /// 是否1次清理动画
        /// </summary>
        public bool OneTimeClean { get; set; }
        
        /// <summary>
        /// 解锁挂点需要货币类型
        /// </summary>
        public int UnLockResType { get; set; }
        
        /// <summary>
        /// 解锁挂点花费
        /// </summary>
        public int UnLockPrice { get; set; }
        
        /// <summary>
        /// 音效
        /// </summary>
        public int Sound { get; set; }
        
        /// <summary>
        /// 音频文件名
        /// </summary>
        public string SoundStr { get; set; }
        
        /// <summary>
        /// 一次打扫动画音效
        /// </summary>
        public int OneTimeSound { get; set; }
        
        /// <summary>
        /// 多语言名字KEY
        /// </summary>
        public string NameKey { get; set; }
        
        /// <summary>
        /// 图标名字
        /// </summary>
        public string IconResName { get; set; }
        
        /// <summary>
        /// 装修图片
        /// </summary>
        public string DecoraResName { get; set; }
        
        /// <summary>
        /// 是否RV广告获得
        /// </summary>
        public bool IsRvGet { get; set; }
        
        /// <summary>
        /// 是否为关卡解锁
        /// </summary>
        public bool IsUnlockLevel { get; set; }
        
        /// <summary>
        /// 选中此挂点显示的节点
        /// </summary>
        public List<string> ShowNodes { get; set; }
        
        /// <summary>
        /// 播放此挂点动画隐藏的元素
        /// </summary>
        public List<string> HideNodes { get; set; }
        
        /// <summary>
        /// 播放动画时开始层级
        /// </summary>
        public int OrderLayer { get; set; }
        
        /// <summary>
        /// 聚焦X
        /// </summary>
        public float FocusX { get; set; }
        
        /// <summary>
        /// 聚焦Y
        /// </summary>
        public float FocusY { get; set; }
        
        /// <summary>
        /// 节点缩放
        /// </summary>
        public float FocusScale { get; set; }
        
        /// <summary>
        /// 节点完成时播放星星音效ID，空则不播
        /// </summary>
        public int StarSfx { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}