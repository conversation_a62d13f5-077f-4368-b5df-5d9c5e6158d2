/************************************************
 * Storage class : StorageDailyTask
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StorageDailyTask : StorageBase
    {
        
        // 弹出次数
        [JsonProperty]
        int popupCount;
        [JsonIgnore]
        public int PopupCount
        {
            get
            {
                return popupCount;
            }
            set
            {
                if(popupCount != value)
                {
                    popupCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当天已经领取的任务ID
        [JsonProperty]
        StorageList<int> receivedTaskId = new StorageList<int>();
        [JsonIgnore]
        public StorageList<int> ReceivedTaskId
        {
            get
            {
                return receivedTaskId;
            }
        }
        // ---------------------------------//
        
        // 当天最高分数
        [JsonProperty]
        int curMaxScore;
        [JsonIgnore]
        public int CurMaxScore
        {
            get
            {
                return curMaxScore;
            }
            set
            {
                if(curMaxScore != value)
                {
                    curMaxScore = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当天通过关卡数量
        [JsonProperty]
        int passLevelCount;
        [JsonIgnore]
        public int PassLevelCount
        {
            get
            {
                return passLevelCount;
            }
            set
            {
                if(passLevelCount != value)
                {
                    passLevelCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 当天参与无尽模式的次数
        [JsonProperty]
        int playEndlessCount;
        [JsonIgnore]
        public int PlayEndlessCount
        {
            get
            {
                return playEndlessCount;
            }
            set
            {
                if(playEndlessCount != value)
                {
                    playEndlessCount = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 重置任务的时间
        [JsonProperty]
        long lastTime;
        [JsonIgnore]
        public long LastTime
        {
            get
            {
                return lastTime;
            }
            set
            {
                if(lastTime != value)
                {
                    lastTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
        // 完成任务的ID
        [JsonProperty]
        StorageList<int> fulfilTaskId = new StorageList<int>();
        [JsonIgnore]
        public StorageList<int> FulfilTaskId
        {
            get
            {
                return fulfilTaskId;
            }
        }
        // ---------------------------------//
        
        // 全部完成的BI是否上报
        [JsonProperty]
        bool allClearBi;
        [JsonIgnore]
        public bool AllClearBi
        {
            get
            {
                return allClearBi;
            }
            set
            {
                if(allClearBi != value)
                {
                    allClearBi = value;
                    Profile.Instance.UpdateLocalVersion();
                    
                    
                }
            }
        }
        // ---------------------------------//
        
    }
}