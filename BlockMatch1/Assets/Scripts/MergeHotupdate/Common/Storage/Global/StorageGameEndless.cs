/************************************************
 * Storage class : StorageWeeklyChallenge
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    ///无尽玩法相关存档
    [System.Serializable]
    public class StorageGameEndless : StorageBase
    {       
        // 当前最高分
        [JsonProperty]
        int curFirstScore;
        [JsonIgnore]
        public int CurFirstScore
        {
            get
            {
                return curFirstScore;
            }
            set
            {
                if(curFirstScore != value)
                {
                    curFirstScore = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//
        
        // 当前次高分
        [JsonProperty]
        int curSecondScore;
        [JsonIgnore]
        public int CurSecondScore
        {
            get
            {
                return curSecondScore;
            }
            set
            {
                if(curSecondScore != value)
                {
                    curSecondScore = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//
        
        // 当前第三高分数
        [JsonProperty]
        int curThirdScore;
        [JsonIgnore]
        public int CurThirdScore
        {
            get
            {
                return curThirdScore;
            }
            set
            {
                if(curThirdScore != value)
                {
                    curThirdScore = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//

        // 当天玩的次数
        [JsonProperty]
        int todayPlayCount;
        [JsonIgnore]
        public int TodayPlayCount
        {
            get
            {
                return todayPlayCount;
            }
            set
            {
                if (todayPlayCount != value)
                {
                    todayPlayCount = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//


        //游戏结果状态
        [JsonProperty]
        int gameResult;
        [JsonIgnore]
        public int GameResult
        {
            get
            {
                return gameResult;
            }
            set
            {
                if (gameResult != value)
                {
                    gameResult = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//

        // 获取的分数
        [JsonProperty]
        int achiveScore;
        [JsonIgnore]
        public int AchiveScore
        {
            get
            {
                return achiveScore;
            }
            set
            {
                if (achiveScore != value)
                {
                    achiveScore = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//

        // 关卡布局
        [JsonProperty]
        StorageList<BlockGridInfo> blockGridInfos = new StorageList<BlockGridInfo>();
        [JsonIgnore]
        public StorageList<BlockGridInfo> BlockGridInfos => blockGridInfos;
        // ---------------------------------//

        // 出块存储
        [JsonProperty]
        StorageList<GenBlockInfo> genBlockInfos = new StorageList<GenBlockInfo>();
        [JsonIgnore]
        public StorageList<GenBlockInfo> GenBlockInfos => genBlockInfos;
        // ---------------------------------//

        // 进入次数
        [JsonProperty]
        int enterCount;
        [JsonIgnore]
        public int EnterCount
        {
            get
            {
                return enterCount;
            }
            set
            {
                if (enterCount != value)
                {
                    enterCount = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//

        // 上次触发过初始棋盘的索引
        [JsonProperty]
        int lastBaseBoardIndex;
        [JsonIgnore]
        public int LastBaseBoardIndex
        {
            get
            {
                return lastBaseBoardIndex;
            }
            set
            {
                if (lastBaseBoardIndex != value)
                {
                    lastBaseBoardIndex = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        
        
        [JsonProperty]
        bool _isNotFirstPlayClearEffect;
        [JsonIgnore]
        public bool IsNotFirstPlayClearEffect
        {
            get
            {
                return _isNotFirstPlayClearEffect;
            }
            set
            {
                if (_isNotFirstPlayClearEffect != value)
                {
                    _isNotFirstPlayClearEffect = value;
                    Profile.Instance.UpdateLocalVersion();
                }
            }
        }
        // ---------------------------------//

        // 最近所玩五局的得分
        [JsonProperty]
        StorageList<int> closestFivePlayScores = new StorageList<int>();
        [JsonIgnore]
        public StorageList<int> ClosestFivePlayScores => closestFivePlayScores;


        public void HandleReset()
        {
            AchiveScore = 0;
            blockGridInfos.Clear();
            GenBlockInfos.Clear();
        }
    }
}