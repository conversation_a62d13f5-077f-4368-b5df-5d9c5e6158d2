/************************************************
 * Storage class : StorageUserData
 * This file is can not be modify !!!
 * If there is some problem, ask hong.zhou.
 ************************************************/

using System;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DragonPlus.Save;

namespace TMGame.Storage
{
    [System.Serializable]
    public class StorageUserData : StorageBase
    {
        
        // 首次登录时间
        [JsonProperty]
        long firstLoginTime;
        [JsonIgnore]
        public long FirstLoginTime
        {
            get
            {
                return firstLoginTime;
            }
            set
            {
                if(firstLoginTime != value)
                {
                    firstLoginTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 首次登录APP版本
        [JsonProperty]
        string firstAppVersion = "";
        [JsonIgnore]
        public string FirstAppVersion
        {
            get
            {
                return firstAppVersion;
            }
            set
            {
                if(firstAppVersion != value)
                {
                    firstAppVersion = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 首次登录资源版本
        [JsonProperty]
        string firstResVersion = "";
        [JsonIgnore]
        public string FirstResVersion
        {
            get
            {
                return firstResVersion;
            }
            set
            {
                if(firstResVersion != value)
                {
                    firstResVersion = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 上一次登录时间
        [JsonProperty]
        long lastLoginTime;
        [JsonIgnore]
        public long LastLoginTime
        {
            get
            {
                return lastLoginTime;
            }
            set
            {
                if(lastLoginTime != value)
                {
                    lastLoginTime = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 上一次登录APP版本
        [JsonProperty]
        string lastAppVersion = "";
        [JsonIgnore]
        public string LastAppVersion
        {
            get
            {
                return lastAppVersion;
            }
            set
            {
                if(lastAppVersion != value)
                {
                    lastAppVersion = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 上一次登录资源版本
        [JsonProperty]
        string lastResVersion = "";
        [JsonIgnore]
        public string LastResVersion
        {
            get
            {
                return lastResVersion;
            }
            set
            {
                if(lastResVersion != value)
                {
                    lastResVersion = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
        // 是否接受过隐私协议
        [JsonProperty]
        bool acceptPrivacy;
        [JsonIgnore]
        public bool AcceptPrivacy
        {
            get
            {
                return acceptPrivacy;
            }
            set
            {
                if(acceptPrivacy != value)
                {
                    acceptPrivacy = value;
                    Profile.Instance.UpdateLocalVersion();
                    Profile.Instance.ForceSaveToDisk();
                    
                }
            }
        }
        // ---------------------------------//
        
    }
}