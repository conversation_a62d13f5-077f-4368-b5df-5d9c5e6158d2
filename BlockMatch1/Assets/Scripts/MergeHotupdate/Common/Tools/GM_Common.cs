#if DEBUG || DEVELOPMENT_BUILD

using System.Collections.Generic;
using System.ComponentModel;
using DragonPlus.Config.InGame;
using DragonPlus.Core;
using DragonPlus.Save;
using Framework;
using TMGame;
using TMGame.Storage;
using UnityEngine;

public partial class DebugOption
{
    public const string Group_Common = "通用";
    public const string Group_Other = "其它";

    [Category(Group_Common)]
    [SROptions.DisplayName("参数1")]
    [SROptions.SortAttribute(1)]
    public string Common_Param1
    {
        get { return common_Param1; }
        set { common_Param1 = value; }
    }

    private string common_Param1;

    [Category(Group_Common)]
    [SROptions.DisplayName("参数2")]
    [SROptions.SortAttribute(2)]
    public string Common_Param2
    {
        get { return common_Param2; }
        set { common_Param2 = value; }
    }

    private string common_Param2;

    [Category(Group_Common)]
    [SROptions.DisplayName("显示信息")]
    public void Common_ShowInfo()
    {
        ulong playerId = SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId;
        string infoStr = string.Empty;
        infoStr += $"数字id：{playerId}\n";
        infoStr += $"游戏id：{SDKUtil.Misc.PlayerIdToString(playerId)}\n";
        infoStr += $"Version：{GameConfig.Version}\n";
        infoStr += $"VersionCode：{GameConfig.VersionCode}\n";
        infoStr += $"AppVersion：{GameConfig.AppVersion}\n";
        infoStr += $"ResVersion：{GameConfig.ResVersion}\n";
        infoStr += $"网络是否连接：{Application.internetReachability != NetworkReachability.NotReachable}\n";
        UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
        {
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("显示用户分层信息")]
    public void Common_ShowUserGroupInfo()
    {
        ulong playerId = SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId;
        string infoStr = string.Empty;
        infoStr += $"当前AD总用户分组：{GameGlobal.GetMod<AdSys>().GetCurGroup()}\n";
        infoStr += $"当前RewardAD用户分组：{GameGlobal.GetMod<AdSys>().GetAdRewardCurrentGroup()}\n";
        infoStr += $"当前InterstitialAD用户分组：{GameGlobal.GetMod<AdSys>().GetAdInterstitialCurrentGroup()}\n";
        infoStr += $"当前BannerAD用户分组：{GameGlobal.GetMod<AdSys>().GetBannerCurrentGroup()}\n";
        infoStr += $"当前RemoveAD用户分组：{GameGlobal.GetMod<IAPSys>().GetAdMappingList()[0].RemoveAdGroup}\n";
        // infoStr += $"当前AdTask用户分组：{GameGlobal.GetMod<IAPSys>().GetAdMappingList()[0].AdTask}\n";
        infoStr += $"当前IAP总用户分组：{GameGlobal.GetMod<IAPSys>().GetCurGroup()}\n";
        // infoStr += $"当前市场内购分组：{GameGlobal.GetMod<IAPSys>().GetMappingList()[0].IapGroup}\n";
        // infoStr += $"当前客户端内购分组：{GameGlobal.GetMod<IAPSys>().GetClientGroupModel().GetClientGroupId()}\n";
        // infoStr += $"当前客户端内购分组类型：{SDK<IStorage>.Instance.Get<StorageGlobal>().ClientGroup.ClientType}\n";
        UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
        {
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("显示设备信息")]
    public void Common_ShowDeviceInfo()
    {
        string infoStr = string.Empty;
        infoStr += $"网络是否连接：{GameUtils.HasNetwork()}\n";
        infoStr += $"系统语言：{Application.systemLanguage}\n";
        infoStr += $"设备型号：{SystemInfo.deviceModel}\n";
        infoStr += $"自定义设备名称：{SystemInfo.deviceName}\n";
        infoStr += $"设备类型：{SystemInfo.deviceType}\n";
        infoStr += $"设备唯一标识：{SystemInfo.deviceUniqueIdentifier}\n";
        infoStr += $"系统运行内存（MB）：{SystemInfo.systemMemorySize}\n";
        infoStr += $"操作系统：{SystemInfo.operatingSystem}\n";
        infoStr += $"操作系统类型：{SystemInfo.operatingSystemFamily}\n";
        infoStr += $"处理器类型：{SystemInfo.processorType}\n";
        infoStr += $"处理器核数：{SystemInfo.processorCount}\n";
        infoStr += $"处理器频率（MHz）：{SystemInfo.processorFrequency}\n";
        infoStr += $"显卡设备ID：{SystemInfo.graphicsDeviceID}\n";
        infoStr += $"显卡名称：{SystemInfo.graphicsDeviceName}\n";
        infoStr += $"显卡类型：{SystemInfo.graphicsDeviceType}\n";
        infoStr += $"显卡供应商：{SystemInfo.graphicsDeviceVendor}\n";
        infoStr += $"显卡供应商ID：{SystemInfo.graphicsDeviceVendorID}\n";
        infoStr += $"显卡版本：{SystemInfo.graphicsDeviceVersion}\n";
        infoStr += $"显卡内存（MB）：{SystemInfo.graphicsMemorySize}\n";
        infoStr += $"电池电量：{SystemInfo.batteryLevel}\n";
        infoStr += $"电池状态：{SystemInfo.batteryStatus}\n";
        infoStr += $"支持的渲染目标数量：{SystemInfo.supportedRenderTargetCount}\n";
        infoStr += $"支持的最大图片尺寸：{SystemInfo.maxTextureSize}\n";
        infoStr += $"是否支持多线程渲染：{SystemInfo.graphicsMultiThreaded}\n";
        infoStr += $"是否支持震动反馈：{SystemInfo.supportsVibration}\n";
        infoStr += $"是否支持陀螺仪：{SystemInfo.supportsGyroscope}\n";
        infoStr += $"是否支持位置服务：{SystemInfo.supportsLocationService}\n";
        UIView_Notice.ViewData openData = new UIView_Notice.ViewData()
        {
            titleKey = "设备信息",
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, openData);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("显示App信息")]
    public void Common_ShowAppInfo()
    {
        string infoStr = string.Empty;
        infoStr += $"Unity版本：{Application.unityVersion}\n";
        infoStr += $"是否为Debug版本：{GameUtils.IsDevelopmentEnv()}\n";
        infoStr += $"当前平台：{Application.platform}\n";
        infoStr += $"分辨率：{Screen.currentResolution}\n";
        infoStr += $"DPI：{Screen.dpi}\n";
        infoStr += $"屏幕旋转类型：{Screen.orientation}\n";
        infoStr +=
            $"性能质量类型：{QualitySettings.names[QualitySettings.GetQualityLevel()]}（{QualitySettings.GetQualityLevel()}）\n";
        UIView_Notice.ViewData openData = new UIView_Notice.ViewData()
        {
            titleKey = "App信息",
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, openData);
    }

    
    [Category(Group_Common)]
    [SROptions.DisplayName("加积分")]
    public void Common_AddScore()
    {
        if (!int.TryParse(Common_Param1, out int _count))
            return;
        if (_count <= 0)
            return;
        var gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.HandleRewardScore(_count);
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }
    [Category(Group_Common)]
    [SROptions.DisplayName("设置debug购买")]
    public void Common_SetDebugPay()
    {
        GameGlobal.GetMod<IAPSys>().inDebugPayMode = !GameGlobal.GetMod<IAPSys>().inDebugPayMode;
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("设置时间缩放")]
    public void Common_SetTimeScale()
    {
        if (!float.TryParse(Common_Param1, out float _timeScale))
            return;
        Time.timeScale = _timeScale;
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("清空弹窗数据")]
    public void Common_ClearPopupsData()
    {
        SDK<IStorage>.Instance.Get<StorageGlobal>().Popups.Clear();
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("保存Block面板")]
    public void Common_SaveBlockGride()
    {
        var theBoard = BlockPlayManager.Instance.GetBoardInfo();
        if (theBoard == 0) return;
        var gameSys = GameGlobal.GetMod<ModGame>();
        if (gameSys.CurGameType == EnumBlockGameType.BlockGame_None) return;

        var theGameBloard = gameSys.CurGridLayout;
        if (theBoard != theGameBloard)
        {
            CLog.Exception($"Common_SaveBlockGride is Error--{theGameBloard},{theBoard}");
            return;
        }

        string infoStr = $"盘面Board:{theBoard}";
        UIView_Notice.ViewData openData = new UIView_Notice.ViewData()
        {
            titleKey = "盘面布局",
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, openData);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("加载Block面板")]
    public void Common_LoadBlockGride()
    {       
        var gameSys = GameGlobal.GetMod<ModGame>();
        if (gameSys.CurGameType == EnumBlockGameType.BlockGame_None) return;
        
        if (!ulong.TryParse(Common_Param1, out ulong _boardInfo))
            return;
        if (_boardInfo <= 0)
            return;
        
        gameSys.LoadBoardInfo(_boardInfo);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("死局检测")]
    public void Common_TestBlockGameDead()
    {
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
        if (theView == null) return;

        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.TestCheckBlockGameDead();

        //ulong playerId = SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId;
        //string infoStr = string.Empty;
        ////infoStr += $"综合复杂度:{BlockPlayManager.Instance.Complexity}\n稀疏度:{BlockPlayManager.Instance.Sparsity}\n联通复杂度:" +
        ////    $"{BlockPlayManager.Instance.ConnectedComponentsCount}\n盘面边数:{BlockPlayManager.Instance.EdgeCount}\n行列复杂度:{BlockPlayManager.Instance.RowAndColumVariability}\n";

        //var theStr1 = LocalizationManager.Instance.GetLocalizedString("UI_difficulty_overall_complexity");
        //var theStr2 = LocalizationManager.Instance.GetLocalizedString("UI_difficulty_sparsity");
        //var theStr3 = LocalizationManager.Instance.GetLocalizedString("UI_difficulty_connectivity_complexity");
        //var theStr4 = LocalizationManager.Instance.GetLocalizedString("UI_difficulty_edge_complexity");
        //var theStr5 = LocalizationManager.Instance.GetLocalizedString("UI_difficulty_row_and_column_complexity");

        //infoStr += $"{theStr1}:{BlockPlayManager.Instance.Complexity}\n{theStr2}:{BlockPlayManager.Instance.Sparsity}\n{theStr3}:" +
        //    $"{BlockPlayManager.Instance.ConnectedComponentsCount}\n{theStr4}:{BlockPlayManager.Instance.EdgeCountEx}\n{theStr5}:{BlockPlayManager.Instance.RowAndColumVariability}\nEntropy:{BlockPlayManager.Instance.Entropy}";


        //UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
        //{
        //    showMidBtn = true,
        //    content = infoStr,
        //};
        //GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("加载上次布局")]
    public void Common_TestLoadPreLayout()
    {
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.PreLayoutLoad();
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("指定block生成")]
    public void Common_RefreshBlock()
    {
        if(!int.TryParse(Common_Param1, out int blockId))
            return;
        if (!int.TryParse(Common_Param2, out int pos))
            return;

        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.Debug_RefreshBlock(blockId, pos - 1);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("跳到指定block关卡")]
    public void Common_JumpStage()
    {
        if (!int.TryParse(Common_Param1, out int levelId))
            return;

        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.Debug_JumpStage(levelId);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("禁止block结算界面")]
    public void Common_TestForbidBlockRewardUI()
    {
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.GM_HandleRewardUI(true);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("开启block结算界面")]
    public void Common_TestOpenBlockRewardUI9()
    {
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.GM_HandleRewardUI(false);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("原始边计算方式")]
    public void Common_TestEdge1()
    {
        BlockPlayManager.isOrigainEdge = true;
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("新型边计算方式")]
    public void Common_TestEdge2()
    {
        BlockPlayManager.isOrigainEdge = false;
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("关卡直接胜利")]
    public void Common_TestLevelVictory()
    {
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.GM_HandleVictory();
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("无尽模式直接失败")]
    public void Common_EndlessFailNow()
    {
        var gameSys = GameGlobal.GetMod<ModGame>();
        if (gameSys == null || gameSys.CurGameType != EnumBlockGameType.BlockGame_Endless)
            return;

        var endlessView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
        if (endlessView == null)
            return;

        EventBus.Dispatch(new BlockGameOver(gameSys.CurLife));
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("切换出块计划")]
    public void Common_ChangeBlockProductPlan()
    {
        ModGame gameSys = GameGlobal.GetMod<ModGame>();
        gameSys.ChangeBlockProductPlan();

#if DEBUG || DEVELOPMENT_BUILD
        var theView = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_GameEndless) as UIView_GameEndless;
        if (theView != null)
        {
            theView.HandleChangeBlockPlan();
        }
        else
        {
            var theView1 = GameGlobal.GetMgr<UIMgr>().FindViewFirst(UIViewName.UIView_Game) as UIView_Game;
            if (theView1 != null)
            {
                theView1.HandleChangeBlockPlan();
            }
        }
#endif
    }


    [Category(Group_Other)]
    [SROptions.DisplayName("打开五星好评")]
    public void Common_UIPopupRateUs()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_RateUsSubmit);
        GameGlobal.GetMgr<UIMgr>().Close(UIViewName.UIDebug);
    }

    [Category(Group_Common)]
    [SROptions.DisplayName("出块策略Test")]
    public void Common_GenerateBlockRandom()
    {
        if (!int.TryParse(Common_Param1, out int configId))
            return;
        if (!int.TryParse(Common_Param2, out int popTime))
            return;

        if (popTime < 0) return;

        var theConfigs = GameGlobal.GetMgr<ConfigMgr>().GetConfigs<Table_InGame_Complextiy>();
        var theInfo = theConfigs.Find(ele=>ele.Id == configId);
        if (theInfo == null) return;

        Dictionary<EnumBlockProductType, int> dict = new Dictionary<EnumBlockProductType, int>();
        var theWeight1 = theInfo.ComplexityIncreaseWeight;
        var theWeight2 = theInfo.ComplexityDecreaseWeight;
        var theWeight3 = theInfo.RandomWeight;
        var theWeight4 = theInfo.DifficultyWeight;
        var theSum = theWeight1 + theWeight2 + theWeight3 + theWeight4;

        for (int i = 0; i < popTime; i++)
        {
            EnumBlockProductType pType = EnumBlockProductType.EBPT_Normal;
            var theRandom = 0;
            if (theInfo != null)
            {
                theRandom = UnityEngine.Random.Range(1, theSum + 1);
                var thePreValue = 0;
                var theCurValue = theWeight1;
                if (theRandom > thePreValue && theRandom <= theCurValue)
                {
                    pType = EnumBlockProductType.EBPT_ComplexityUp;
                }
                else
                {
                    thePreValue = theCurValue;
                    theCurValue += theWeight2;
                    if (theRandom > thePreValue && theRandom <= theCurValue)
                    {
                        pType = EnumBlockProductType.EBPT_ComplexityDown;
                    }
                    else
                    {
                        thePreValue = theCurValue;
                        theCurValue += theWeight3;
                        if (theRandom > thePreValue && theRandom <= theCurValue)
                        {
                            pType = EnumBlockProductType.EBPT_Normal;
                        }
                        else
                        {
                            thePreValue = theCurValue;
                            theCurValue += theWeight4;
                            if (theRandom > thePreValue && theRandom <= theCurValue)
                            {
                                pType = EnumBlockProductType.EBPT_Hard;
                            }
                        }
                    }
                }
            }

            if(dict.ContainsKey(pType))
            {
                dict[pType]++;
            }
            else
            {
                dict[pType] = 1;
            }
        }

        ulong playerId = SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId;
        string infoStr = string.Empty;
        foreach (var item in dict)
        {
            infoStr += $"{item.Key}:{item.Value},";
        }
        UIView_Notice.ViewData viewData = new UIView_Notice.ViewData()
        {
            titleKey = "出块策略信息",
            showMidBtn = true,
            content = infoStr,
        };
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_Notice, viewData);
    }


#if DEVELOPMENT_BUILD
    [Category(Group_Common)]
    [SROptions.DisplayName("随机最优解Log")]
    public void Common_ShowBestRandomLog()
    {
        BlockPlayManager.BestRandomShowLog = !BlockPlayManager.BestRandomShowLog;
    }
#endif


}

#endif