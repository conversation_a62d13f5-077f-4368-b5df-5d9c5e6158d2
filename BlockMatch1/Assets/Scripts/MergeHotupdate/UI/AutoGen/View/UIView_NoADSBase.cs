/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-4-2 11:53:27*****/
/*****************************/

using Framework;
using DragonPlus;
using UnityEngine;
using UnityEngine.UI;

public class UIView_NoADSBase : UIViewBase
{
	protected LocalizeTextMeshProUGUI UITxt_TitleText;
	protected RectTransform UINode_TitleGroup;
	protected Button UIBtn_CloseButton;
	protected LocalizeTextMeshProUGUI UITxt_TipsText;
	protected RectTransform UINode_Item1;
	protected LocalizeTextMeshProUGUI UITxt_NumberText;
	protected LocalizeTextMeshProUGUI UITxt_DescribeText;
	protected RectTransform UINode_Item2;
	protected Button UIBtn_BuyButton;
	protected Button UIBtn_ContiuneButton;

    protected override void BindComponent()
    {
		UITxt_TitleText = GO.transform.Find("Root/Layout/BG/Top/UINode_TitleGroup/UITxt_TitleText").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_TitleGroup = GO.transform.Find("Root/Layout/BG/Top/UINode_TitleGroup").GetComponent<RectTransform>();
		UIBtn_CloseButton = GO.transform.Find("Root/Layout/BG/Top/UIBtn_CloseButton").GetComponent<Button>();
		UITxt_TipsText = GO.transform.Find("Root/Layout/BG/MiddleGruop/UINode_Item1/UITxt_TipsText").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_Item1 = GO.transform.Find("Root/Layout/BG/MiddleGruop/UINode_Item1").GetComponent<RectTransform>();
		UITxt_NumberText = GO.transform.Find("Root/Layout/BG/MiddleGruop/UINode_Item2/BG/UITxt_NumberText").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_DescribeText = GO.transform.Find("Root/Layout/BG/MiddleGruop/UINode_Item2/UITxt_DescribeText").GetComponent<LocalizeTextMeshProUGUI>();
		UINode_Item2 = GO.transform.Find("Root/Layout/BG/MiddleGruop/UINode_Item2").GetComponent<RectTransform>();
		UIBtn_BuyButton = GO.transform.Find("Root/Layout/BG/MiddleGruop/UIBtn_BuyButton").GetComponent<Button>();
		UIBtn_ContiuneButton = GO.transform.Find("Root/Layout/UIBtn_ContiuneButton").GetComponent<Button>();

    }
}
