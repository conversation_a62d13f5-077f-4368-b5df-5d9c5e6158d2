/******************************/
/*****自动生成的UIView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-3-4 10:30:44*****/
/*****************************/

using Framework;
using DragonPlus;
using UnityEngine.UI;

public class UIView_SettingBase : UIViewBase
{
	protected LocalizeTextMeshProUGUI UITxt_TitleText;
	protected Button UIBtn_Close;
	protected Button UIBtn_CloseButton;
	protected Image UIImg_SoundOn;
	protected Image UIImg_SoundOff;
	protected Button UIBtn_Sound;
	protected Image UIImg_MusicOn;
	protected Image UIImg_MusicOff;
	protected Button UIBtn_Music;
	protected Image UIImg_NoticeOn;
	protected Image UIImg_NoticeOff;
	protected Button UIBtn_Notice;
	protected Image UIImg_VibrateOn;
	protected Image UIImg_VibrateOff;
	protected Button UIBtn_Vibrate;
	protected HorizontalLayoutGroup UILayoutH_FunctionGroup;
	protected Button UIBtn_LanguageButton;
	protected Button UIBtn_SupportButton;
	protected Button UIBtn_OtherButton;
	protected Button UIBtn_RestoreBuyButton;
	protected Button UIBtn_SaveButton;
	protected Button UIBtn_Deletion;
	protected VerticalLayoutGroup UILayoutV_InsideGroup;
	protected LocalizeTextMeshProUGUI UITxt_VersionText;
	protected LocalizeTextMeshProUGUI UITxt_PrivacyText;
	protected Button UIBtn_Privacy;
	protected LocalizeTextMeshProUGUI UITxt_UserText;
	protected LocalizeTextMeshProUGUI UITxt_ServiceText;
	protected Button UIBtn_Service;
	protected VerticalLayoutGroup UILayoutV_ReplaceDiff;

    protected override void BindComponent()
    {
		UITxt_TitleText = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/TitleGroup/UITxt_TitleText").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Close = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/TitleGroup/UIBtn_CloseButton/UIBtn_Close").GetComponent<Button>();
		UIBtn_CloseButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/TitleGroup/UIBtn_CloseButton").GetComponent<Button>();
		UIImg_SoundOn = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Sound/UIImg_SoundOn").GetComponent<Image>();
		UIImg_SoundOff = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Sound/UIImg_SoundOff").GetComponent<Image>();
		UIBtn_Sound = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Sound").GetComponent<Button>();
		UIImg_MusicOn = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Music/UIImg_MusicOn").GetComponent<Image>();
		UIImg_MusicOff = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Music/UIImg_MusicOff").GetComponent<Image>();
		UIBtn_Music = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Music").GetComponent<Button>();
		UIImg_NoticeOn = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Notice/UIImg_NoticeOn").GetComponent<Image>();
		UIImg_NoticeOff = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Notice/UIImg_NoticeOff").GetComponent<Image>();
		UIBtn_Notice = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Notice").GetComponent<Button>();
		UIImg_VibrateOn = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Vibrate/UIImg_VibrateOn").GetComponent<Image>();
		UIImg_VibrateOff = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Vibrate/UIImg_VibrateOff").GetComponent<Image>();
		UIBtn_Vibrate = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup/UIBtn_Vibrate").GetComponent<Button>();
		UILayoutH_FunctionGroup = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutH_FunctionGroup").GetComponent<HorizontalLayoutGroup>();
		UIBtn_LanguageButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_LanguageButton").GetComponent<Button>();
		UIBtn_SupportButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_SupportButton").GetComponent<Button>();
		UIBtn_OtherButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_OtherButton").GetComponent<Button>();
		UIBtn_RestoreBuyButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_RestoreBuyButton").GetComponent<Button>();
		UIBtn_SaveButton = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_SaveButton").GetComponent<Button>();
		UIBtn_Deletion = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup/UIBtn_Deletion").GetComponent<Button>();
		UILayoutV_InsideGroup = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/UILayoutV_InsideGroup").GetComponent<VerticalLayoutGroup>();
		UITxt_VersionText = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UITxt_VersionText").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_PrivacyText = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UIBtn_Privacy/UITxt_PrivacyText").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Privacy = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UIBtn_Privacy").GetComponent<Button>();
		UITxt_UserText = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UITxt_UserText").GetComponent<LocalizeTextMeshProUGUI>();
		UITxt_ServiceText = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UIBtn_Service/UITxt_ServiceText").GetComponent<LocalizeTextMeshProUGUI>();
		UIBtn_Service = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff/BG_ReplaceDiff2/BottomGroup/UIBtn_Service").GetComponent<Button>();
		UILayoutV_ReplaceDiff = GO.transform.Find("Root/BG/UILayoutV_ReplaceDiff").GetComponent<VerticalLayoutGroup>();

    }
}
