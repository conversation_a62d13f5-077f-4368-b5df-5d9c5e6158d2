/******************************/
/*****自动生成的UISubView界面代码，禁止手动修改*****/
/*****界面逻辑写在子类中*****/
/*****生成时间：2025-2-20 15:28:49*****/
/*****************************/

using Framework;
using TMPro;
using UnityEngine.UI;

public class UIView_GameExitBase : UISubViewBase
{
	protected TextMeshProUGUI UITxt_Title;
	protected Button UIBtn_ExitButton;
	protected Button UIBtn_OkayButton;
	protected Button UIBtn_CancelButton;
	protected Button UIBtn_Close;

    protected override void BindComponent()
    {
		UITxt_Title = GO.transform.Find("Root/UIBg/Top/WindowsGroup/UITxt_Title").GetComponent<TextMeshProUGUI>();
		UIBtn_ExitButton = GO.transform.Find("Root/UIBg/Top/ButtonsGroup/UIBtn_ExitButton").GetComponent<Button>();
		UIBtn_OkayButton = GO.transform.Find("Root/UIBg/Top/ButtonsGroup/UIBtn_OkayButton").GetComponent<Button>();
		UIBtn_CancelButton = GO.transform.Find("Root/UIBg/Top/ButtonsGroup/UIBtn_CancelButton").GetComponent<Button>();
		UIBtn_Close = GO.transform.Find("Root/UIBg/UIBtn_Close").GetComponent<Button>();

    }
}
