using System;
using System.Collections.Generic;
using UnityEngine;

public enum EChildAlignmentType
{
    MiddleLeft = 1,
    MiddleCenter,
    MiddleRight,
}

/// <summary>
/// 3D水平布局组件
/// </summary>
/// todo:目前只支持居左对齐，后面扩展
[ExecuteInEditMode]
public class HorizontalLayoutGroup3D : MonoBehaviour
{
    public EChildAlignmentType alignmentType;
    public float gridX;
    public float space;
    public float paddingLeft;
    public float paddingRight;

    private float startPosX;

    private void Update()
    {
        CalcStartPosX();
        UpdateChildPos();
    }

    private void CalcStartPosX()
    {
        var com_SpriteRenderer = GetComponent<SpriteRenderer>();
        switch (alignmentType)
        {
            case EChildAlignmentType.MiddleLeft:
                if (com_SpriteRenderer == null)
                {
                    startPosX = -transform.localScale.x / 2;
                }
                else
                {
                    startPosX = -Mathf.Max(transform.localScale.x / 2, com_SpriteRenderer.size.x / 2);
                }
                startPosX += gridX / 2;
                startPosX += paddingLeft;
                break;

            case EChildAlignmentType.MiddleRight:
                if (com_SpriteRenderer == null)
                {
                    startPosX = transform.localScale.x / 2;
                }
                else
                {
                    startPosX = Mathf.Max(transform.localScale.x / 2, com_SpriteRenderer.size.x / 2);
                }
                startPosX -= gridX / 2;
                startPosX -= paddingRight;
                break;
        }
    }

    private void UpdateChildPos()
    {
        for (int i = 0; i < transform.childCount; i++)
        {
            if (!transform.gameObject.activeSelf)
                continue;
            transform.GetChild(i).transform.position = new Vector3(startPosX, transform.position.y);
            switch (alignmentType)
            {
                case EChildAlignmentType.MiddleLeft:
                    startPosX += gridX + space;
                    break;

                case EChildAlignmentType.MiddleRight:
                    startPosX -= (gridX + space);
                    break;
            }
        }
    }
}