using System.Collections.Generic;
using UnityEngine;

public class AssetAutoGCMonoBehaviour : MonoBehaviour
{
    private HashSet<AssetDTO> dtoList= new HashSet<AssetDTO>();

    // Update is called once per frame
    private void OnDestroy()
    {
        foreach(var dto in dtoList)
        {
            dto.RemoveReference(gameObject);
        }
        dtoList.Clear();
    }

    //
    public void AddAssetDTO(AssetDTO dto)
    {
        dtoList.Add(dto);
    }
}
