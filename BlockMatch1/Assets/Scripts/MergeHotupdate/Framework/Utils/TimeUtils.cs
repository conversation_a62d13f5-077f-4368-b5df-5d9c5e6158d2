using System;
using UnityEngine;

public static class TimeUtils
{
    public const int SecPerMinute = 60; //一分钟的秒数
    public const int SecPerHour = 3600; //一小时的秒数
    public const int SecPerDay = 86400; //一天的秒数

    private static DateTime StartTime = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    /// <summary>
    /// 格式化时间
    /// </summary>
    /// useColon：是否使用冒号
    public static string FormatTime(int sec, bool useColon = true)
    {
        string timeStr = "";
        TimeSpan timeSpan = new TimeSpan(0, 0, 0, sec);
        if (timeSpan.Days > 0)
        {
            timeStr = CoreUtils.GetLocalization(useColon ? "UI_TimeFormatWithDay_2" : "UI_TimeFormatWithDay", timeSpan.Days, timeSpan.Hours);
        }
        else if (timeSpan.Hours > 0)
        {
            timeStr = CoreUtils.GetLocalization(useColon ? "UI_TimeFormatWithHour_2" : "UI_TimeFormatWithHour", timeSpan.Hours, timeSpan.Minutes);
        }
        else
        {
            timeStr = CoreUtils.GetLocalization(useColon ? "UI_TimeFormatWithMinute_2" : "UI_TimeFormatWithMinute", timeSpan.Minutes, timeSpan.Seconds);
        }
        return timeStr;
    }

    /// <summary>
    /// 格式化日期
    /// </summary>
    public static string FormatDateTime(DateTime dt, string format = "yyyy/MM/dd HH:mm:ss")
    {
        return dt.ToString(format);
    }

    /// <summary>
    /// 格式化日期
    /// </summary>
    public static string FormatDateTime(double timeStamp, string format = "yyyy/MM/dd HH:mm:ss")
    {
        var dateTime = StartTime.AddSeconds(timeStamp);
        string ret = FormatDateTime(dateTime, format);
        return ret;
    }

    /// <summary>
    /// 获取间隔时间
    /// </summary>
    public static int GetDuration(DateTime dt1, DateTime dt2)
    {
        TimeSpan ts = dt2 - dt1;
        return Mathf.Max(0, (int)ts.TotalSeconds);
    }

    /// <summary>
    /// 获取是当前周的第几天
    /// </summary>
    private static int GetDayOfWeek(DayOfWeek dayOfWeekType)
    {
        if (dayOfWeekType == DayOfWeek.Sunday)
        {
            return 7;
        }
        return (int)dayOfWeekType;
    }
}