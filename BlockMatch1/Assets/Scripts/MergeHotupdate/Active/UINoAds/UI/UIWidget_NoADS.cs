using DragonPlus.Core;
using DragonU3DSDK.Network.API.Protocol;
using Framework;
using TMGame;
using UnityEngine.UI;

public class UIWidget_NoADS : UIWidget_NoADSBase
{
    protected override void OnShow()
    {
        base.OnShow();
        InitAds();
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<IAPSuccess>(OnIAPSuccess);
        GO.GetComponent<Button>().onClick.AddListener(OnButtonClick);
    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        EventBus.Unsubscribe<IAPSuccess>(OnIAPSuccess);
        GO.GetComponent<Button>().onClick.RemoveListener(OnButtonClick);
    }

    private void InitAds()
    {
        GO.SetActive(Active.NoAds.Model.Instance.NoAdsEntrance());
    }
    private void OnIAPSuccess(IAPSuccess evt)
    {
        if (evt.TableGameShopConfig.GetIAPShopType() == ShopType.NoAd)
        {
            InitAds();
        }
    }
    private void OnButtonClick()
    {
        var cfg = GameGlobal.GetMod<IAPSys>().GetRemoveAdCfg();
        if (cfg.RemoveAdsGiftId.Count > 0)
        {
            BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventIconClick, UIViewName.UIView_NoADS.ToString());
            UIView_NoADS.OpenData openData = new UIView_NoADS.OpenData()
            {
                isPop = false,
                shopId = cfg.RemoveAdsGiftId[0],
            };
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_NoADS, openData);
        }
    }
}