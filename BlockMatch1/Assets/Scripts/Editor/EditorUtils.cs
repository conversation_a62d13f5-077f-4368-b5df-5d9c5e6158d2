using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public static class EditorUtils
{
    /// <summary>
    /// 显示对话框
    /// </summary>
    public static void ShowDialogWindow(string title, string content, string btn1Name)
    {
        CommonDialogWindow.CreateWindow(title, content, btn1Name);
    }

    /// <summary>
    /// 获取选择的所有路径
    /// </summary>
    public static string[] GetSelectPathArray()
    {
        string[] assetGUIDs = Selection.assetGUIDs;
        string[] assetPaths = new string[assetGUIDs.Length];
        for (int i = 0; i < assetGUIDs.Length; i++)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(assetGUIDs[i]);
            assetPaths[i] = assetPath;
        }
        return assetPaths;
    }

    /// <summary>
    /// 获取所有选择的文件
    /// </summary>
    /// searchPatternArray：*.asset
    public static List<string> GetSelectedFileList(SearchOption searchOption = SearchOption.TopDirectoryOnly, params string[] searchPatternArray)
    {
        List<string> fileList = new List<string>();
        string[] assetGuidArray = Selection.assetGUIDs;
        foreach (var assetGuid in assetGuidArray)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);
            fileList.Add(assetPath);
        }
        var ret = GetFileList(fileList, searchOption, searchPatternArray);
        return ret;
    }

    /// <summary>
    /// 获取某个路径列表中的文件
    /// </summary>
    /// searchPatternArray：*.asset
    public static List<string> GetFileList(List<string> pathList, SearchOption searchOption = SearchOption.TopDirectoryOnly, params string[] searchPatternArray)
    {
        var ret = new HashSet<string>();
        foreach (var path in pathList)
        {
            if (IOUtils.IsFilePath(path))
            {
                ret.Add(path);
            }
            else
            {
                if (searchPatternArray.Length == 0)
                {
                    string[] filePathArray = Directory.GetFiles(path, "*", searchOption);
                    foreach (var filePath in filePathArray)
                    {
                        ret.Add(filePath);
                    }
                }
                else
                {
                    foreach (var searchPattern in searchPatternArray)
                    {
                        string[] filePathArray = Directory.GetFiles(path, searchPattern);
                        foreach (var filePath in filePathArray)
                        {
                            ret.Add(filePath);
                        }
                    }
                }
            }
        }
        return ret.ToList();
    }

    /// <summary>
    /// 获取当前时间
    /// </summary>
    /// <returns></returns>
    public static string GenCurDateTimeStr()
    {
        string dateTimeStr = DateTime.Now.ToString("yyyy-M-d H:m:s");
        return dateTimeStr;
    }

    /// <summary>
    /// 删除某个GameObject下所有子物体上missing的脚本
    /// </summary>
    public static void DeleteAllMissingMonoBehaviour(GameObject go)
    {
        GameObjectUtility.RemoveMonoBehavioursWithMissingScript(go);
        for (int i = 0; i < go.transform.childCount; i++)
        {
            DeleteAllMissingMonoBehaviour(go.transform.GetChild(i).gameObject);
        }
    }
}