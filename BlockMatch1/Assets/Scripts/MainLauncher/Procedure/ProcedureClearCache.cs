using Cysharp.Threading.Tasks;
using YooAsset;

public class ProcedureClearCache : Procedure
{
    public ProcedureClearCache(ProcedureContext context) : base(context)
    {
    }

    public override async UniTask ExecuteProcedure()
    {
        base.ExecuteProcedure();
        var defaultPackage = YooAssets.GetPackage(GlobalSetting.Ins.DefaultPackageName);
        var operation = defaultPackage.ClearUnusedCacheFilesAsync();
        await operation;
    }
}