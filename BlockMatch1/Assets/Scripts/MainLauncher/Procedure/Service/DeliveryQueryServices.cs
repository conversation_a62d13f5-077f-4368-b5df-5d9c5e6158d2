using System;
using YooAsset;

public class DeliveryQueryServices : IDeliveryQueryServices
{
    // /// <summary>
    // /// 查询是否为开发者分发的资源
    // /// </summary>
    // public bool QueryDeliveryFiles(string packageName, string fileName)
    // {
    //     return false;
    // }
    //
    // /// <summary>
    // /// 获取开发者分发的资源信息
    // /// </summary>
    // public DeliveryFileInfo GetDeliveryFileInfo(string packageName, string fileName)
    // {
    //     throw new NotImplementedException();
    // }
    public bool Query(string packageName, string fileName, string fileCRC)
    {
        return false;
    }
    public string GetFilePath(string packageName, string fileName)
    {
        throw new NotImplementedException();
    }
    public bool QueryDeliveryFiles(string packageName, string fileName)
    {
        return false;
    }
    public DeliveryFileInfo GetDeliveryFileInfo(string packageName, string fileName)
    {
        throw new NotImplementedException();
    }
}